import mysql2 from 'mysql2/promise';
import { ConfigData } from '../config/environment';

const getPoolConfig = (configs: ConfigData): mysql2.PoolOptions => {
  const dbName = configs.dbConfig.database;

  if (configs.dbConfig.host === 'localhost' || configs.dbConfig.host === 'bookmarks_mysql') {
    const poolConfig: mysql2.PoolOptions = {
      host: configs.dbConfig.host,
      user: configs.dbConfig.username,
      password: configs.dbConfig.password,
      database: dbName,
      namedPlaceholders: true,
    };
    return poolConfig;
  }

  const poolConfig: mysql2.PoolOptions = {
    host: configs.dbConfig.host,
    user: configs.dbConfig.username,
    password: configs.dbConfig.password,
    database: dbName,
    namedPlaceholders: true,
    ssl: { rejectUnauthorized: false },
    connectionLimit: 10,
    queueLimit: 0,
  };
  return poolConfig;
};

export const databasePool = (configs: ConfigData): mysql2.Pool => {
  const poolConfig = getPoolConfig(configs);
  const pool = mysql2.createPool(poolConfig);
  return pool;
};
