:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* In src/styles/globals.css */
ol,
ul {
  margin-left: 0.75rem;
  padding-left: 0.75rem;
  margin-bottom: 0.75rem;
}

li {
  line-height: 1.65;
  margin-bottom: 0.5rem;
}

a {
  color: #0070f3;
  text-decoration: none;
}

.like-med {
  max-width: 100%;
  max-height: 600px;
  height: 100vh;
}

.like-large {
  max-width: 100%;
  max-height: 800px;
  height: 100vh;
}
