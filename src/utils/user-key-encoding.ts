/**
 * Utility functions for encoding and decoding user keys
 * Following the Java service pattern: {country_code},{user_id}
 */

export interface DecodedUserKey {
  countryCode: string;
  userId: string;
}

/**
 * Encode a user key from country code and user ID
 * @param countryCode Country code (e.g., "US", "GB")
 * @param userId User ID
 * @returns Encoded user key string
 */
export function encodeUserKey(countryCode: string, userId: string): string {
  return `${countryCode},${userId}`;
}

/**
 * Decode a user key to extract country code and user ID
 * @param userKey Encoded user key string
 * @returns Decoded user key object
 * @throws Error if user key format is invalid
 */
export function decodeUserKey(userKey: string): DecodedUserKey {
  if (!userKey || typeof userKey !== 'string') {
    throw new Error('Invalid user key: must be a non-empty string');
  }

  // decode base64 if necessary
  try {
    userKey = Buffer.from(userKey, 'base64').toString('utf-8');
  } catch {
    // Not base64, use as-is
  }

  const parts = userKey.split(',');
  if (parts.length !== 2) {
    throw new Error('Invalid user key format: must be {country_code},{user_id}');
  }

  const [countryCode, userId] = parts;

  if (!countryCode || !userId) {
    throw new Error('Invalid user key: country code and user ID must be non-empty');
  }

  return {
    countryCode: countryCode.trim(),
    userId: userId.trim(),
  };
}

/**
 * Validate a user key format
 * @param userKey Encoded user key string
 * @returns True if valid, false otherwise
 */
export function isValidUserKey(userKey: string): boolean {
  try {
    decodeUserKey(userKey);
    return true;
  } catch {
    return false;
  }
}

/**
 * Encode user key for URL safe usage (base64 encoding)
 * @param countryCode Country code
 * @param userId User ID
 * @returns Base64 encoded user key
 */
export function encodeUserKeyForUrl(countryCode: string, userId: string): string {
  const userKey = encodeUserKey(countryCode, userId);
  return Buffer.from(userKey).toString('base64');
}

/**
 * Decode URL safe user key (base64 decoding)
 * @param encodedUserKey Base64 encoded user key
 * @returns Decoded user key object
 */
export function decodeUserKeyFromUrl(encodedUserKey: string): DecodedUserKey {
  try {
    const userKey = Buffer.from(encodedUserKey, 'base64').toString('utf-8');
    return decodeUserKey(userKey);
  } catch (error) {
    throw new Error(`Invalid encoded user key: ${(error as Error).message}`);
  }
}
