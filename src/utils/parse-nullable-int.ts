/**
 * Parses a string int an integer.
 * Handles NaN by returning undefined.
 * Will short circuit with null or undefined files as undefined.
 * @param value The raw string to parse, or null or undefined.
 * @returns     The parsed integer, or undefined.
 */
export function parseNullableInt(value: string | null | undefined): number | undefined {
  // Short circuit for empty string, null, undefined
  if (!value) {
    return undefined;
  }

  // Parse the string into an integer
  const num = Number.parseInt(value, 10);

  // Return undefined if the number is invalid (NaN)
  if (Number.isNaN(num)) {
    return undefined;
  }

  // Return the parsed integer
  return num;
}
