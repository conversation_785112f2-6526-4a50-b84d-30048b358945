import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';

const REGIONS: Record<string, string> = {
  qa: 'us-east-1',
  stag: 'us-west-2',
  prod: 'us-west-2',
};

const SECRET_ARNS: Record<string, string> = {
  qa: 'arn:aws:secretsmanager:us-east-1:762359486493:secret:bc/ecs/qa/bookmarkservice',
  stag: 'arn:aws:secretsmanager:us-west-2:762359486493:secret:bc/ecs/stag/bookmarkservice',
  prod: 'arn:aws:secretsmanager:us-west-2:551062308343:secret:bc/ecs/prod/bookmarkservice',
};

/**
 * Validates AWS configuration by checking required environment variables
 * and attempting to access the configured Secrets Manager secret
 */
export async function validateAwsConfig(env?: string): Promise<boolean> {
  const environment = (env || process.env.ENVIRONMENT || 'dev').toLowerCase();

  if (environment === 'dev') {
    console.info('Skipping AWS validation for dev environment');
    return true;
  }

  const region = REGIONS[environment];
  const secretArn = SECRET_ARNS[environment];

  if (!region || !secretArn) {
    console.error(`Unknown environment: ${environment}`);
    return false;
  }

  console.info(`Validating AWS config for ${environment}...`);
  console.info(`Region: ${region}`);
  console.info(`Secret ARN: ${secretArn}`);

  try {
    const client = new SecretsManagerClient({ region });
    await client.send(new GetSecretValueCommand({ SecretId: secretArn }));
    console.info('AWS validation successful');
    return true;
  } catch (error) {
    console.error('AWS validation failed:', error);
    return false;
  }
}

export default validateAwsConfig;
