import * as crypto from 'crypto';

/**
 * TypeScript equivalent of the Java KeyEncrypter class.
 *
 * Code here are duplicated inside Intl2. Ideally we should have a common lib that is used both by
 * Intl and notification. We cannot put this inside BcUtils since BcUtils is too big for this microservice.
 *
 * Provides AES encryption/decryption functionality to match the Java service behavior.
 */
export class KeyEncrypter {
  private static readonly ALGO = 'AES';

  // This needs to be exactly 16 characters
  private static readonly SECRET_KEY = 'Hi Ni Hao Maomi!';

  private static readonly keyValue = Buffer.from(KeyEncrypter.SECRET_KEY, 'utf8');

  /**
   * Encrypts data using AES encryption and returns base64-encoded result.
   * Equivalent to Java's KeyEncrypter.encode() method.
   *
   * @param data - The plain text data to encrypt
   * @returns Base64-encoded encrypted string
   */
  static encode(data: string): string {
    try {
      // For Node.js v22+, we need to use the aes-128-ecb with proper key handling
      const algorithm = 'aes-128-ecb';
      const key = new Uint8Array(KeyEncrypter.keyValue.subarray(0, 16)); // Convert to Uint8Array

      // ECB mode doesn't use an IV, but Node.js requires it to be null for ECB
      const cipher = crypto.createCipheriv(algorithm, key, null);
      let encrypted = cipher.update(data, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      return encrypted;
    } catch (error) {
      // Fallback for compatibility if the above doesn't work
      throw new Error(`Encryption failed: ${error}`);
    }
  }

  /**
   * Decrypts base64-encoded data using AES decryption.
   * Equivalent to Java's KeyEncrypter.decode() method.
   *
   * @param data - The base64-encoded encrypted string to decrypt
   * @returns Decrypted plain text string
   */
  static decode(data: string): string {
    try {
      // For Node.js v22+, we need to use the aes-128-ecb with proper key handling
      const algorithm = 'aes-128-ecb';
      const key = new Uint8Array(KeyEncrypter.keyValue.subarray(0, 16)); // Convert to Uint8Array

      // ECB mode doesn't use an IV, but Node.js requires it to be null for ECB
      const decipher = crypto.createDecipheriv(algorithm, key, null);
      let decrypted = decipher.update(data, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      // Fallback for compatibility if the above doesn't work
      throw new Error(`Decryption failed: ${error}`);
    }
  }
}
