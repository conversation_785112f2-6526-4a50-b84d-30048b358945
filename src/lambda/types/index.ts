export interface BaseCommunityEventDto {
  eventType: string;
  countryCode: string;
  contentId: string;
  globalMemberId: string;
  siteMemberId?: number;
  snippet?: string;
  genericContent?: {
    globalUserId: string;
    postId?: number;
    replyId?: number;
    href?: string;
    message?: string;
    messageMultipleOccurrence?: string;
    messageMultipleOccurrenceOriginal?: string;
    userScreenName?: string;
  };
}

export interface SubscriptionEventDto {
  countryCode: string;
  globalMemberId: string;
  subscriptionId: string;
  subscribed: boolean;
}

export interface NotificationId {
  userKey: string;
  contentActionKey: string;
}

export interface Notification {
  id: NotificationId;
  count: number;
  read: boolean;
  createDateTime: Date;
  snippet?: string;
  postId?: number;
  replyId?: number;
  genericContent?: any;
}

export interface GlobalSubscription {
  contentKey: string;
  globalMemberId: string;
  countryCode: string;
  contentId: number;
}

export interface MemberActivityStatus {
  userKey: string;
  countryCode: string;
  userId: string;
  lastActivity: Date;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export enum NotificationTypeEnum {
  COMMENT = 'comment',
  REPLY = 'reply',
  REACT_ON_POST = 'reactOnPost',
  REACT_ON_COMMENT = 'reactOnComment',
  DIRECT_MESSAGE = 'directMessage',
}
