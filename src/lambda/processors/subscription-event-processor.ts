import {
  DynamoDBDocumentClient,
  PutCommand,
  DeleteCommand,
  GetCommand,
} from '@aws-sdk/lib-dynamodb';
import { SubscriptionEventDto, GlobalSubscription } from '../types';
import { logger } from '../utils/logger';

export class SubscriptionEventProcessor {
  constructor(private readonly dynamoClient: DynamoDBDocumentClient) { }

  async processSubscriptionEvent(dto: SubscriptionEventDto): Promise<void> {
    logger.info('Processing subscription event', {
      subscribed: dto.subscribed,
      globalMemberId: dto.globalMemberId,
      subscriptionId: dto.subscriptionId,
    });

    if (dto.subscribed) {
      await this.addSubscription(dto);
    } else {
      await this.removeSubscription(dto);
    }
  }

  private async addSubscription(dto: SubscriptionEventDto): Promise<void> {
    const contentKey = `${dto.countryCode}#${dto.subscriptionId}`;
    const userRangeKey = `${dto.countryCode}#${dto.globalMemberId}`;

    const subscription: GlobalSubscription = {
      contentKey,
      globalMemberId: dto.globalMemberId,
      countryCode: dto.countryCode,
      contentId: parseInt(dto.subscriptionId),
    };

    const putParams = {
      TableName: process.env.DYNAMO_GLOBAL_SUBSCRIPTION_TABLE!,
      Item: {
        ContentHashKey: subscription.contentKey,
        UserRangeKey: userRangeKey,
        GlobalMemberId: subscription.globalMemberId,
        CountryCode: subscription.countryCode,
        ContentId: subscription.contentId,
        CreateDateTime: new Date().toISOString(),
      },
    };

    try {
      await this.dynamoClient.send(new PutCommand(putParams));
      logger.info('Successfully added subscription', {
        contentKey,
        globalMemberId: dto.globalMemberId,
      });
    } catch (error) {
      logger.error('Error adding subscription', {
        error: error instanceof Error ? error.message : 'Unknown error',
        contentKey,
        globalMemberId: dto.globalMemberId,
      });
      throw error;
    }
  }

  private async removeSubscription(dto: SubscriptionEventDto): Promise<void> {
    const contentKey = `${dto.countryCode}#${dto.subscriptionId}`;
    const userRangeKey = `${dto.countryCode}#${dto.globalMemberId}`;

    // Check if subscription exists before trying to delete
    const getParams = {
      TableName: process.env.DYNAMO_GLOBAL_SUBSCRIPTION_TABLE!,
      Key: {
        ContentHashKey: contentKey,
        UserRangeKey: userRangeKey,
      },
    };

    try {
      const existingResult = await this.dynamoClient.send(new GetCommand(getParams));

      if (existingResult.Item) {
        const deleteParams = {
          TableName: process.env.DYNAMO_GLOBAL_SUBSCRIPTION_TABLE!,
          Key: {
            ContentHashKey: contentKey,
            UserRangeKey: userRangeKey,
          },
        };

        await this.dynamoClient.send(new DeleteCommand(deleteParams));
        logger.info('Successfully removed subscription', {
          contentKey,
          globalMemberId: dto.globalMemberId,
        });
      } else {
        logger.warn('Subscription not found for removal', {
          contentKey,
          globalMemberId: dto.globalMemberId,
        });
      }
    } catch (error) {
      logger.error('Error removing subscription', {
        error: error instanceof Error ? error.message : 'Unknown error',
        contentKey,
        globalMemberId: dto.globalMemberId,
      });
      throw error;
    }
  }
}
