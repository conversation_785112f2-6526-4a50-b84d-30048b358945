import {
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  UpdateCommand,
  DeleteCommand,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import {
  BaseCommunityEventDto,
  Notification,
  GlobalSubscription,
  NotificationTypeEnum,
} from '../types';
import { logger } from '../utils/logger';

export class CommunityEventProcessor {
  private readonly NOTIFICATION_LIMIT = 500;
  private readonly notificationLifetime: number;

  constructor(private readonly dynamoClient: DynamoDBDocumentClient) {
    this.notificationLifetime = parseInt(process.env.NOTIFICATION_LIFETIME_DAYS || '7');
  }

  async processCommunityEvent(dto: BaseCommunityEventDto): Promise<void> {
    logger.info('Processing community event', {
      eventType: dto.eventType,
      contentId: dto.contentId,
      globalMemberId: dto.globalMemberId,
    });

    // Clean up old notifications beyond limit
    await this.deleteNotificationBeyondLimit(dto);

    switch (dto.eventType) {
      case NotificationTypeEnum.COMMENT:
        await this.handleCommentOnPost(dto);
        break;
      case NotificationTypeEnum.REPLY:
        await this.handleReplyOnComment(dto);
        break;
      case NotificationTypeEnum.REACT_ON_POST:
      case NotificationTypeEnum.REACT_ON_COMMENT:
        await this.handleReaction(dto);
        break;
      case NotificationTypeEnum.DIRECT_MESSAGE:
        await this.handleDirectMessage(dto);
        break;
      default:
        logger.warn('Unknown event type', { eventType: dto.eventType });
    }
  }

  private async handleCommentOnPost(dto: BaseCommunityEventDto): Promise<void> {
    const commenter = dto.genericContent?.globalUserId;
    const postAuthor = dto.globalMemberId;

    if (!commenter) {
      logger.warn('Missing commenter globalUserId in genericContent');
      return;
    }

    // Don't notify post author if they're commenting on their own post
    if (postAuthor !== commenter) {
      await this.saveCommentOnPostNotification(dto, postAuthor);
    }

    // Notify all subscribers except post author and commenter
    await this.notifySubscribers(dto, [postAuthor, commenter]);
  }

  private async handleReplyOnComment(dto: BaseCommunityEventDto): Promise<void> {
    const replier = dto.globalMemberId;
    const originalCommenter = dto.genericContent?.globalUserId;

    if (!originalCommenter) {
      logger.warn('Missing original commenter globalUserId in genericContent');
      return;
    }

    // Only notify original commenter if they're not replying to themselves
    if (replier !== originalCommenter) {
      const notification = this.createNotification(dto, originalCommenter);
      notification.postId = dto.genericContent?.postId;
      notification.replyId = dto.genericContent?.replyId;

      await this.saveOrUpdateNotification(notification);
    }
  }

  private async handleReaction(dto: BaseCommunityEventDto): Promise<void> {
    const reactor = dto.globalMemberId;
    const contentOwner = dto.genericContent?.globalUserId;

    if (!contentOwner || reactor === contentOwner) {
      return; // Don't notify if reacting to own content
    }

    const notification = this.createNotification(dto, contentOwner);
    notification.postId = dto.genericContent?.postId;
    notification.replyId = dto.genericContent?.replyId;

    await this.saveOrUpdateNotification(notification);
  }

  private async handleDirectMessage(dto: BaseCommunityEventDto): Promise<void> {
    const recipient = dto.globalMemberId;
    const notification = this.createNotification(dto, recipient);

    await this.saveOrUpdateNotification(notification);
  }

  private async notifySubscribers(
    dto: BaseCommunityEventDto,
    excludeUserIds: string[],
  ): Promise<void> {
    const contentKey = `${dto.countryCode}#${dto.contentId}`;

    let lastEvaluatedKey: Record<string, any> | undefined = undefined;

    do {
      const queryParams: any = {
        TableName: process.env.DYNAMO_GLOBAL_SUBSCRIPTION_TABLE!,
        KeyConditionExpression: 'contentKey = :contentKey',
        ExpressionAttributeValues: {
          ':contentKey': contentKey,
        },
        Limit: 200,
        ExclusiveStartKey: lastEvaluatedKey,
      };

      const result: any = await this.dynamoClient.send(new QueryCommand(queryParams));

      if (result.Items) {
        for (const subscription of result.Items as GlobalSubscription[]) {
          const subscriberId = subscription.globalMemberId;

          if (!excludeUserIds.includes(subscriberId)) {
            await this.saveCommentOnPostNotification(dto, subscriberId);
          }
        }
      }

      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);
  }

  private async saveCommentOnPostNotification(
    dto: BaseCommunityEventDto,
    userId: string,
  ): Promise<void> {
    const notification = this.createNotification(dto, userId);
    await this.saveOrUpdateNotification(notification);
  }

  private createNotification(dto: BaseCommunityEventDto, userId: string): Notification {
    const userKey = `${dto.countryCode},${userId}`;
    const contentActionKey = `${dto.contentId}#${dto.eventType}`;

    return {
      id: {
        userKey,
        contentActionKey,
      },
      count: 1,
      read: false,
      createDateTime: new Date(),
      snippet: dto.snippet,
      genericContent: dto.genericContent,
    };
  }

  private async saveOrUpdateNotification(notification: Notification): Promise<void> {
    try {
      // Try to get existing notification
      const getParams = {
        TableName: process.env.DYNAMO_NOTIFICATION_TABLE!,
        Key: {
          UserHashKey: notification.id.userKey,
          ContentRangeKey: notification.id.contentActionKey,
        },
      };

      const existingResult = await this.dynamoClient.send(new GetCommand(getParams));

      if (existingResult.Item) {
        // Update existing notification
        const existing = existingResult.Item as Notification;

        if (existing.read) {
          // If previously read, reset count to 1 and mark as unread
          existing.count = 1;
          existing.read = false;
        } else {
          // If unread, increment count
          existing.count++;
        }

        existing.snippet = notification.snippet;
        existing.genericContent = notification.genericContent;
        existing.createDateTime = new Date();

        const updateParams = {
          TableName: process.env.DYNAMO_NOTIFICATION_TABLE!,
          Key: {
            UserHashKey: notification.id.userKey,
            ContentRangeKey: notification.id.contentActionKey,
          },
          UpdateExpression:
            'SET #count = :count, #read = :read, #snippet = :snippet, #genericContent = :genericContent, #createDateTime = :createDateTime',
          ExpressionAttributeNames: {
            '#count': 'Count',
            '#read': 'Read',
            '#snippet': 'Snippet',
            '#genericContent': 'GenericContent',
            '#createDateTime': 'CreateDateTime',
          },
          ExpressionAttributeValues: {
            ':count': existing.count,
            ':read': existing.read,
            ':snippet': existing.snippet,
            ':genericContent': existing.genericContent,
            ':createDateTime': existing.createDateTime.toISOString(),
          },
        };

        await this.dynamoClient.send(new UpdateCommand(updateParams));
      } else {
        // Create new notification
        const putParams = {
          TableName: process.env.DYNAMO_NOTIFICATION_TABLE!,
          Item: {
            UserHashKey: notification.id.userKey,
            ContentRangeKey: notification.id.contentActionKey,
            Count: notification.count,
            Read: notification.read,
            Snippet: notification.snippet,
            GenericContent: notification.genericContent,
            CreateDateTime: notification.createDateTime.toISOString(),
            PostId: notification.postId,
            ReplyId: notification.replyId,
          },
        };

        await this.dynamoClient.send(new PutCommand(putParams));
      }
    } catch (error) {
      logger.error('Error saving/updating notification', {
        error: error instanceof Error ? error.message : 'Unknown error',
        notification: notification.id,
      });
      throw error;
    }
  }

  private async deleteNotificationBeyondLimit(dto: BaseCommunityEventDto): Promise<void> {
    try {
      const userKey = `${dto.countryCode},${dto.globalMemberId}`;

      // Query all notifications for this user
      const queryParams = {
        TableName: process.env.DYNAMO_NOTIFICATION_TABLE!,
        KeyConditionExpression: 'UserHashKey = :userKey',
        ExpressionAttributeValues: {
          ':userKey': userKey,
        },
      };

      const result = await this.dynamoClient.send(new QueryCommand(queryParams));

      if (result.Items && result.Items.length > this.NOTIFICATION_LIMIT) {
        // Sort by create date (newest first) and keep only the limit
        const notifications = result.Items.map((item: any) => ({
          ...item,
          CreateDateTime: new Date(item.CreateDateTime as string),
        })).sort((a: any, b: any) => b.CreateDateTime.getTime() - a.CreateDateTime.getTime());

        // Delete notifications beyond the limit
        const toDelete = notifications.slice(this.NOTIFICATION_LIMIT);

        for (const notification of toDelete) {
          const deleteParams = {
            TableName: process.env.DYNAMO_NOTIFICATION_TABLE!,
            Key: {
              UserHashKey: (notification as any).UserHashKey,
              ContentRangeKey: (notification as any).ContentRangeKey,
            },
          };

          await this.dynamoClient.send(new DeleteCommand(deleteParams));
        }

        logger.info(`Deleted ${toDelete.length} old notifications for user ${userKey}`);
      }
    } catch (error) {
      logger.error('Error deleting old notifications', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userKey: `${dto.countryCode},${dto.globalMemberId}`,
      });
      // Don't throw - this is cleanup, shouldn't fail the main operation
    }
  }
}
