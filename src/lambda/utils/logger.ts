import winston from 'winston';

// Create a logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
  ),
  defaultMeta: { service: 'notification-lambda' },
  transports: [
    new winston.transports.Console({
      format: winston.format.json(), // Use JSON format for Lambda CloudWatch
    }),
  ],
});
