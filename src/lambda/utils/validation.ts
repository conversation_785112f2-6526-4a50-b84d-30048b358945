import { ValidationResult } from '../types';

export function validateEvent(messageBody: any): ValidationResult {
  const errors: string[] = [];

  // Check required fields for community events
  if (!messageBody.eventType) {
    errors.push('eventType is required');
  }

  if (!messageBody.countryCode) {
    errors.push('countryCode is required');
  }

  if (!messageBody.contentId) {
    errors.push('contentId is required');
  }

  if (!messageBody.globalMemberId) {
    errors.push('globalMemberId is required');
  }

  // Validate event type
  const validEventTypes = ['comment', 'reply', 'reactOnPost', 'reactOnComment', 'directMessage'];
  if (messageBody.eventType && !validEventTypes.includes(messageBody.eventType)) {
    errors.push(`Invalid eventType: ${messageBody.eventType}`);
  }

  // Validate country code format (should be like 'en_US')
  if (messageBody.countryCode && !/^[a-z]{2}_[A-Z]{2}$/.test(messageBody.countryCode)) {
    errors.push(`Invalid countryCode format: ${messageBody.countryCode}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function validateSubscriptionEvent(messageBody: any): ValidationResult {
  const errors: string[] = [];

  // Check required fields for subscription events
  if (!messageBody.countryCode) {
    errors.push('countryCode is required');
  }

  if (!messageBody.globalMemberId) {
    errors.push('globalMemberId is required');
  }

  if (!messageBody.subscriptionId) {
    errors.push('subscriptionId is required');
  }

  if (typeof messageBody.subscribed !== 'boolean') {
    errors.push('subscribed must be a boolean');
  }

  // Validate country code format (should be like 'en_US')
  if (messageBody.countryCode && !/^[a-z]{2}_[A-Z]{2}$/.test(messageBody.countryCode)) {
    errors.push(`Invalid countryCode format: ${messageBody.countryCode}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
