import { SQSEvent, SQSRecord, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { CommunityEventProcessor } from './processors/community-event-processor';
import { logger } from './utils/logger';
import { validateEvent } from './utils/validation';

// Initialize AWS clients
const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));

// Initialize processor
const processor = new CommunityEventProcessor(dynamoClient);

export const handler = async (event: SQSEvent, context: Context): Promise<void> => {
  logger.info(`Processing ${event.Records.length} community event records`, {
    requestId: context.awsRequestId,
    recordCount: event.Records.length,
  });

  const promises = event.Records.map(async (record: SQSRecord) => {
    try {
      const messageBody = JSON.parse(record.body);

      // Validate the message structure
      const validationResult = validateEvent(messageBody);
      if (!validationResult.isValid) {
        logger.warn('Invalid message format', {
          messageId: record.messageId,
          errors: validationResult.errors,
          body: record.body,
        });
        return;
      }

      // Process the community event
      await processor.processCommunityEvent(messageBody);

      logger.info('Successfully processed community event', {
        messageId: record.messageId,
        eventType: messageBody.eventType,
        contentId: messageBody.contentId,
      });
    } catch (error) {
      logger.error('Error processing community event record', {
        messageId: record.messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        body: record.body,
      });

      // Don't send to DLQ manually - let SQS DLQ handle it
      // Re-throw to trigger automatic DLQ behavior
      throw error;
    }
  });

  await Promise.all(promises);

  logger.info('Completed processing all community event records', {
    requestId: context.awsRequestId,
  });
};
