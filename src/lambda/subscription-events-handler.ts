import { SQSEvent, SQSRecord, Context } from 'aws-lambda';
import { Dynamo<PERSON>BClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { SubscriptionEventProcessor } from './processors/subscription-event-processor';
import { logger } from './utils/logger';
import { validateSubscriptionEvent } from './utils/validation';

// Initialize AWS clients
const dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({}));

// Initialize processor
const processor = new SubscriptionEventProcessor(dynamoClient);

export const handler = async (event: SQSEvent, context: Context): Promise<void> => {
  logger.info(`Processing ${event.Records.length} subscription event records`, {
    requestId: context.awsRequestId,
    recordCount: event.Records.length,
  });

  const promises = event.Records.map(async (record: SQSRecord) => {
    try {
      const messageBody = JSON.parse(record.body);

      // Validate the message structure
      const validationResult = validateSubscriptionEvent(messageBody);
      if (!validationResult.isValid) {
        logger.warn('Invalid subscription message format', {
          messageId: record.messageId,
          errors: validationResult.errors,
          body: record.body,
        });
        return;
      }

      // Process the subscription event
      await processor.processSubscriptionEvent(messageBody);

      logger.info('Successfully processed subscription event', {
        messageId: record.messageId,
        subscribed: messageBody.subscribed,
        globalMemberId: messageBody.globalMemberId,
      });
    } catch (error) {
      logger.error('Error processing subscription event record', {
        messageId: record.messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        body: record.body,
      });

      // Don't send to DLQ manually - let SQS DLQ handle it
      // Re-throw to trigger automatic DLQ behavior
      throw error;
    }
  });

  await Promise.all(promises);

  logger.info('Completed processing all subscription event records', {
    requestId: context.awsRequestId,
  });
};
