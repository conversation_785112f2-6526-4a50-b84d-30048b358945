import type { MDXComponents } from 'mdx/types'
import Image, { ImageProps } from 'next/image'

// This file allows you to provide custom React components
// to be used in MDX files. You can import and use any
// React component you want, including inline styles,
// components from other libraries, and more.

export function useMDXComponents(components: MDXComponents): MDXComponents {
    return {
        // Allows customizing built-in components, e.g. to add styling.
        h1: ({ children }) => (
            <h1 style={{ fontSize: '2.5rem', marginBottom: '1rem' }}>{children}</h1>
        ),
        h2: ({ children }) => (
            <h2 style={{ fontSize: '2rem', marginBottom: '0.75rem', marginTop: '1.5rem' }}>{children}</h2>
        ),
        h3: ({ children }) => (
            <h2 style={{ fontSize: '1.5rem', marginBottom: '0.75rem', marginTop: '1.5rem' }}>{children}</h2>
        ),
    p: ({ children }) => (
        <p style={{ lineHeight: 1.6, margin: '0.75rem 0' }}>{children}</p>
    ),
    code: ({ children }) => (
        <code
            style={{
                // background: '#f4f4f4',
                padding: '0.2rem 0.4rem',
                borderRadius: '3px',
                fontFamily: 'monospace',
            }}>{ children }</code>
    ),
    pre: ({ children }) => (
        <pre
            style={{
                // background: '#f4f4f4',
                padding: '1rem',
                overflowX: 'auto',
                borderRadius: '5px',
            }}>{ children }</pre>
    ),
        img: (props) => (
            <Image
                sizes="100vw"
                style={{ width: '100%', height: 'auto' }}
                {...(props as ImageProps)}
            />
        ),
        ...components,
    }
}
