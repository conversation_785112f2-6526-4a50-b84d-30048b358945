import { randomUUID } from 'crypto';
import express from 'express';
import http from 'http';
import configAuth from './auth/config-auth';
import configAwilix from './awilix/config-awilix';
import { AppScopeRegister, RequestScopeRegister } from './awilix/container-scope';
import { ConfigData } from './config/environment';
import logs from './logs/logs';
import { setupRoutes } from './routes';
import BookmarkService from './services/bookmark/bookmark-service';
import ProfileEventService from './services/profile-event/profile-event-service';
import NotificationService from './services/notification/notification-service';
import statsd from './statsd/statsd';
import { databasePool } from './database/database-pool';
import validateAwsConfig from './utils/aws-validator';
import AWS from 'aws-sdk';
import cors from 'cors';
import helmet from 'helmet';

// Application health status tracking
interface AppHealthStatus {
  isHealthy: boolean;
  initializationComplete: boolean;
  errors: string[];
  services: {
    database: boolean;
    auth: boolean;
    notification: boolean;
    bookmark: boolean;
  };
}

const appHealthStatus: AppHealthStatus = {
  isHealthy: false,
  initializationComplete: false,
  errors: [],
  services: {
    database: false,
    auth: false,
    notification: false,
    bookmark: false,
  },
};

// Health check endpoint that reflects actual application status
const healthCheckResponse = (_req: express.Request, res: express.Response): void => {
  if (!appHealthStatus.isHealthy || !appHealthStatus.initializationComplete) {
    res.status(503).json({
      status: 'unhealthy',
      initialization: appHealthStatus.initializationComplete,
      errors: appHealthStatus.errors,
      services: appHealthStatus.services,
    });
    return;
  }

  res.status(200).json({
    status: 'healthy',
    initialization: true,
    services: appHealthStatus.services,
  });
};

export default async function app(
  configs: ConfigData,
): Promise<{ httpServer: http.Server; expressApp: express.Application }> {
  // Initialize Express app and HTTP server
  console.log('Initializing Express app and HTTP server...');
  const expressApp = express();
  const httpServer = http.createServer(expressApp);
  // Configure timeout (prevent hanging requests)
  httpServer.timeout = 60000; // 60 seconds
  httpServer.keepAliveTimeout = 65000; // slightly higher than timeout

  // Validate AWS configuration if not in dev environment
  if (configs.envName !== 'dev') {
    const awsConfigValid = await validateAwsConfig(configs.envName);
    if (!awsConfigValid) {
      console.error('AWS configuration validation failed. Check environment and credentials.');
      appHealthStatus.errors.push('AWS configuration validation failed');
    }
  }

  // Register health check route before any middleware or error-prone code
  // This ensures it's always available even if other parts fail
  expressApp.get('/health', healthCheckResponse as express.RequestHandler);

  // Setup basic middleware
  expressApp.use(express.json());

  // Security headers
  expressApp.use(
    helmet({
      contentSecurityPolicy: false, // Disabled for development, should be enabled in production
      xssFilter: true,
      noSniff: true,
      hidePoweredBy: true,
    }),
  );

  // CORS configuration
  expressApp.use(
    cors({
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps, curl requests)
        if (!origin) return callback(null, true);

        // If we have a wildcard, allow all origins
        if (configs.corsConfig.allowedOrigins.includes('*')) {
          return callback(null, true);
        }

        // Otherwise check against our allowed origins
        if (configs.corsConfig.allowedOrigins.includes(origin)) {
          return callback(null, true);
        }

        // If not found in the allowed list, reject
        return callback(new Error('CORS not allowed'), false);
      },
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }),
  );

  // Request ID and IP tracking middleware
  expressApp.use((req, _res, next) => {
    // Extract client IP address from headers that may contain multiple IPs (e.g., x-forwarded-for)
    function getIp(header: string | string[] | undefined): string | undefined {
      if (!header) return undefined;
      if (typeof header === 'string') return header.split(',')[0].trim();
      if (Array.isArray(header) && header.length > 0) return header[0].split(',')[0].trim();
      return undefined;
    }

    // Try each IP source in order of preference
    const ipCandidates = [
      getIp(req.headers.remoteip),
      getIp(req.headers['true-client-ip']),
      getIp(req.headers['x-forwarded-for']),
      req.ip,
    ];

    // Use the first non-undefined IP
    req.remoteIp = ipCandidates.find((ip) => ip !== undefined);
    req.requestId = randomUUID();
    req.envName = configs.envName;
    req.userAgent = req.headers['user-agent'];
    req.referer = req.headers['referer'];
    next();
  });

  try {
    // Initialize services
    const { logger, mainLogger } = logs(expressApp, configs);
    const { metrics } = statsd(configs);

    mainLogger.info(`Starting bookmark service in ${configs.envName} environment`);

    // Set AWS region based on environment
    const awsRegion = configs.envName === 'qa' ? 'us-east-1' : 'us-west-2';
    AWS.config.update({ region: awsRegion });

    try {
      // Initialize database connection
      const dbPool = databasePool(configs);
      appHealthStatus.services.database = true;
      mainLogger.info('Database connection initialized successfully');

      try {
        // Initialize event and auth services
        const { bcAuthService, user } = await configAuth(configs);
        appHealthStatus.services.auth = true;
        mainLogger.info('Authentication service initialized successfully');

        // Register dependencies for dependency injection
        const appScopeRegister: AppScopeRegister = {
          configs: () => ({ ...configs }), // Create a copy to avoid mutations
          metrics,
        };

        const profileEventService = new ProfileEventService(configs.usMemberEventArn);
        const bookmarkService = new BookmarkService(dbPool, profileEventService);
        appHealthStatus.services.bookmark = true;
        mainLogger.info('Bookmark service initialized successfully');

        const notificationService = new NotificationService(configs.dynamoConfig);
        appHealthStatus.services.notification = true;
        mainLogger.info('Notification service initialized successfully');

        const requestScopeRegister: Omit<RequestScopeRegister, 'request'> = {
          requestId: ({ request }) => request.requestId,
          logger,
          user,
          bcAuthService,
          bookmarkService: () => bookmarkService,
          notificationService: () => notificationService,
          profileEventService: () => profileEventService,
          dbPool: () => dbPool,
        };

        // Register middleware and routes
        configAwilix(expressApp, appScopeRegister, requestScopeRegister);

        // Register API routes - setupRoutes() will not override the health route
        // because it's registered before the router is mounted
        setupRoutes(expressApp);

        appHealthStatus.isHealthy = true;
        appHealthStatus.initializationComplete = true;
        mainLogger.info('Full application initialization successful');
        console.log('Full application initialization successful');
      } catch (error) {
        const errorMessage = `Error during auth or service initialization: ${error}`;
        console.error(errorMessage);
        mainLogger.error('Error during auth or service initialization:', { error });
        appHealthStatus.errors.push(errorMessage);
        appHealthStatus.initializationComplete = true;
      }
    } catch (error) {
      const errorMessage = `Error during database initialization: ${error}`;
      console.error(errorMessage);
      mainLogger.error('Error during database initialization:', { error });
      appHealthStatus.errors.push(errorMessage);
      appHealthStatus.initializationComplete = true;
    }
  } catch (error) {
    const errorMessage = `Error during app initialization: ${error}`;
    console.error(errorMessage);
    console.log('App will start with limited functionality - only health check will work');
    appHealthStatus.errors.push(errorMessage);
    appHealthStatus.initializationComplete = true;
    // Do not rethrow the error - this allows the server to start with just the health check
  }

  return { httpServer, expressApp };
}
