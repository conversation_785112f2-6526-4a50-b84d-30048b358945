import app from './app';
import { getConfigs } from './config/environment';
import server from './server';
import dotenv from 'dotenv';

async function main(): Promise<void> {
  try {
    // Load the .env configurations into 'dotenvStore' local object
    console.log('Loading env configurations...');
    const dotenvStore: Record<string, string> = {};
    dotenv.config({ processEnv: dotenvStore });

    const config = await getConfigs(dotenvStore);
    console.log('Config loaded successfully');

    // Initialize app
    const { httpServer } = await app(config);

    // Start server
    const port = config.port;
    server(httpServer, port);

    console.log(`Server running at http://localhost:${port}`);
    console.log(`Environment: ${process.env.ENVIRONMENT || 'unknown'}`);
    console.log('Health check endpoint available at /health');
  } catch (error) {
    console.error('Critical error during startup:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections and exceptions
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Do not exit process - allow health check to continue working
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Do not exit process - allow health check to continue working
});

main().catch((err) => {
  console.error('Fatal error during startup:', err);
  console.log('Server will not start due to critical error');
});
