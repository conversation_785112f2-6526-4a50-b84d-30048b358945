import StatsdService from './statsd-service';
import { ConfigData } from '../config/environment';
import { StatsD } from 'hot-shots';
import { AppScopeRegister } from '../awilix/container-scope';

/**
 * I know it seems redundant, just in case someone changes that type, the compiler
 * will give a gentle reminder that it's used to compose the metric name.
 */
const ENV_TO_NAME: Record<ConfigData['envName'], string> = {
  dev: 'dev',
  qa: 'qa',
  stag: 'stag',
  prod: 'prod',
};

export default function statsd(config: ConfigData): Pick<AppScopeRegister, 'metrics'> {
  const { statsdHost, envName } = config;
  if (!statsdHost) {
    console.log(`StatsD not configured`);
    return {
      metrics: () => new StatsdService(undefined),
    };
  }

  const env = ENV_TO_NAME[envName];
  const prefix = `${env}.bookmarks.`;

  const collector = new StatsD({
    host: statsdHost,
    prefix,
    errorHandler: function (error): void {
      console.log('StatsD Socket errors caught here: ', error);
    },
    includeDataDogTags: false,
  });

  return {
    metrics: () => new StatsdService(collector),
  };
}
