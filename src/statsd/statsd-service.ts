import { StatsD } from 'hot-shots';

export enum Metric {
  // AUTH - BC
  AUTH_BC_VALID = 'auth.bc.valid',
  AUTH_BC_EXPIRED_TOKEN = 'auth.bc.expiredToken',
  AUTH_BC_MISSING_JWT_PAYLOAD = 'auth.bc.error.missingJwtPayload',
  AUTH_BC_INVALID_AUDIENCE = 'auth.bc.error.invalidAudience',
  AUTH_BC_TOKEN_WITHOUT_SUBJECT = 'auth.bc.error.tokenWithoutSubject',
  AUTH_BC_EXCEPTION_ON_VERIFY = 'auth.bc.error.exception.onVerify',
  AUTH_BC_EXCEPTION_ON_VALIDATE = 'auth.bc.error.exception.onValidate',
}

class StatsdService {
  #collector: StatsD | undefined;

  constructor(collector: StatsD | undefined) {
    this.#collector = collector;
  }

  increment(metric: Metric): void {
    if (!this.#collector) {
      return;
    }
    const name = `${metric}`;
    this.#collector.increment(name);
  }

  incrementOperation(operationName: string): void {
    if (!this.#collector) {
      return;
    }
    const name = `operation.${operationName}`;
    this.#collector.increment(name);
  }
}

export default StatsdService;
