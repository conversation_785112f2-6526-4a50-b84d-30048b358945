package com.babycenter.bookmarkservice.rest;

import com.babycenter.bookmarkservice.auth.RequestToken;
import com.babycenter.bookmarkservice.event.ProfileEventService;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.persistence.BookmarkDataStore;
import com.babycenter.bookmarkservice.persistence.jpa.BookmarkRepository;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Rest API used for batch service to get bookmarked content.
 *
 * Created by nspessot on 5/18/17.
 */
@RestController
@RequestMapping("/bookmark/v1")
public class BookmarkBatchRestController
{
	private static final String CONTENT_URL = "/bookmark/v1/content/{contentSource}/{contentType}/{contentId}?pageIndex={pageIndex}&pageSize={pageSize}";
	private static final String CONTENT_URL_NO_PAGE = "/bookmark/v1/content/{contentSource}/{contentType}/{contentId}";
	private static final String BATCH_URL = "/bookmark/v1/content/{contentSource}/contentType/{contentType}?pageIndex={pageIndex}&pageSize={pageSize}";
	public static final String REQUEST_TOKEN_HEADER = "bsToken";

	private static final Logger log = LoggerFactory.getLogger(BookmarkBatchRestController.class);

	@Autowired
	BookmarkDataStore dataStore;
	@Autowired
	BookmarkRepository bookmarkRepository;
	@Autowired
	ProfileEventService profileEventService;

	String bookmarksServiceSecret;
	/**
	 * read the list of bookmarks for a given piece of content.
	 * This is secured with a shared secret token, rather than a member access token,
	 * because it is not on behalf of a logged-in user.  It is called by a batch process.
	 *
	 * @param bookmarksServiceToken shared secret required to call batch endpoints
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 * @param pageIndex index of page in results (zero based index of pageSize pages) defaults to zero
	 * @param pageSize maximum number of results to return (may return less), defaults to 100;
	 * @return List of Bookmarks (may be empty) and status 200 OK,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token
	 */
	@GetMapping("/content/{contentSource}/{contentType}/{contentId}")
	public ResponseEntity<Iterable<Bookmark>> read(
		@RequestHeader(REQUEST_TOKEN_HEADER) final String bookmarksServiceToken,
		@PathVariable final String contentSource,
		@PathVariable final String contentType,
		@PathVariable final String contentId,
		@RequestParam(required = false) final Integer pageIndex,
		@RequestParam(required = false) final Integer pageSize)
	{
		try
		{
			log.debug("GET handler /bookmark/v1/content/{contentSource}/{contentType}/{contentId}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);

			//
			// The internal access token must match the shared secret to allow access to internal endpoints.
			//
			final String url = CONTENT_URL
				.replace("{contentSource}", contentSource)
				.replace("{contentType}", contentType)
				.replace("{contentId}", contentId)
				.replace("{pageIndex}", String.valueOf(pageIndex))
				.replace("{pageSize}", String.valueOf(pageSize));
			if((null == bookmarksServiceToken) || !RequestToken.isTokenValid(this.bookmarksServiceSecret, url, bookmarksServiceToken))
			{
				log.error("GET handler /bookmark/v1/content/{contentSource}/{contentType}/{contentId} internal access token is missing or invalid."
					.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
					.replace("{contentType}", (null != contentType) ? contentType : "null")
					.replace("{contentId}", (null != contentId) ? contentId : "null")
				);
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			final int workingPageSize = (null != pageSize) ? pageSize : BookmarkDataStore.DEFAULT_PAGE_SIZE;
			final int workingPageOffset = (null != pageIndex) ? pageIndex : 0;

			final Iterable<Bookmark> entities = dataStore.readAllContent(contentSource, contentType, contentId, workingPageOffset, workingPageSize).getResults();

			return new ResponseEntity<>(entities, HttpStatus.OK);
		}
		catch(Throwable e)
		{
			log.error("GET handler /bookmark/v1/content/{contentSource}/{contentType}/{contentId} encountered an unexpected fatal exception."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}


	@GetMapping("/content/{contentSource}/contentType/{contentType}")
	public ResponseEntity<List<Bookmark>> read(
		@RequestHeader(REQUEST_TOKEN_HEADER) final String bookmarksServiceToken,
		@PathVariable final String contentSource,
		@PathVariable final String contentType,
		@RequestParam(required = false) final Integer pageIndex,
		@RequestParam(required = false) final Integer pageSize)
	{
		final String url = BATCH_URL
			.replace("{contentSource}", contentSource)
			.replace("{contentType}", contentType)
			.replace("{pageIndex}", String.valueOf(pageIndex))
			.replace("{pageSize}", String.valueOf(pageSize));

		if((null == bookmarksServiceToken) || !RequestToken.isTokenValid(this.bookmarksServiceSecret, url, bookmarksServiceToken))
		{
			log.error("GET handler /bookmark/v1/content/{contentSource}/contentType/{contentType} internal access token is missing or invalid."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
			);
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}

		Page<Bookmark> bookmarks = bookmarkRepository.findByContentSourceAndContentType(contentSource, contentType, new PageRequest(pageIndex, pageSize, new Sort(Sort.Direction.DESC, "createDate")));


		return new ResponseEntity<>(bookmarks.getContent(), HttpStatus.OK);
	}

	@DeleteMapping("/content/{contentSource}/{contentType}/{contentId}")
	public ResponseEntity<Void> deleteByContentId(
			@RequestHeader(REQUEST_TOKEN_HEADER) final String bookmarksServiceToken,
			@PathVariable final String contentSource,
			@PathVariable final String contentType,
			@PathVariable final String contentId) {
		final String url = CONTENT_URL_NO_PAGE
				.replace("{contentSource}", contentSource)
				.replace("{contentType}", contentType)
				.replace("{contentId}", contentId);

		if((null == bookmarksServiceToken) || !RequestToken.isTokenValid(this.bookmarksServiceSecret, url, bookmarksServiceToken))
		{
			log.error("DELETE handler " + CONTENT_URL_NO_PAGE + " internal access token is missing or invalid."
					.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
					.replace("{contentType}", (null != contentType) ? contentType : "null")
					.replace("{contentId}", (null != contentId) ? contentId : "null")
			);
			return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
		}

		List<Bookmark> bookmarks = bookmarkRepository.findByContentSourceAndContentTypeAndContentId(contentSource, contentType, contentId);

		for (Bookmark bookmark : bookmarks) {
			profileEventService.sendSubscriptionEvent(null, bookmark, false);
		}

		bookmarkRepository.deleteInBatch(bookmarks);

		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	@Value("${bookmarksServiceSecret}")
	public void setBookmarksServiceSecret(final String bookmarksServiceSecret)
	{
		this.bookmarksServiceSecret = bookmarksServiceSecret;
	}
}
