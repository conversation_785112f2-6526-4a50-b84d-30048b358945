package com.babycenter.bookmarkservice.rest;

import com.babycenter.bookmarkservice.auth.AccessTokenRequestValidator;
import com.babycenter.bookmarkservice.business.BookmarkService;
import com.babycenter.bookmarkservice.business.TooManyBookmarksException;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.model.PaginatedResults;
import com.babycenter.bookmarkservice.persistence.BookmarkDataStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * Rest API for bookmarks.
 * - add a single bookmark
 * - remove a single bookmark
 * - get all bookmarks for given member
 * - get all all bookmarks for a given piece of content.
 *
 * Created by emurphy on 3/16/17.
 */
@RestController
@RequestMapping("/bookmark")
public class BookmarkRestController
{
	public static final String AUTH_HEADER_NAME = HttpHeaders.AUTHORIZATION;
	private static final Logger log = LoggerFactory.getLogger(BookmarkBatchRestController.class);

	private BookmarkDataStore dataStore;
	private AccessTokenRequestValidator accessTokenRequestValidator;
	private BookmarkService bookmarkService;

	/**
	 * Determine if the authorization header contains a valid access token for this member.
	 *
	 * @param authorizationHeader contents of the 'authorization' header from http request
	 * @param memberId member's global auth id
	 * @return true if access token is valid for member, false if not
	 */
	private boolean isAccessTokenValid(final String authorizationHeader, final String memberId)
	{
		final String accessToken = authorizationHeader.startsWith("Bearer ") ? authorizationHeader.substring(7) : authorizationHeader;
		return accessTokenRequestValidator.isValid(accessToken, memberId);
	}

	/**
	 * read a single bookmark given it's natural key.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 * @return Bookmark and status 200 OK if found,
	 *         status 404 if not found,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 */
	@GetMapping("/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}")
	public ResponseEntity<Bookmark> read(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
	    @PathVariable final String contentSource,
	    @PathVariable final String contentType,
	    @PathVariable final String contentId)
	{
		try
		{
			log.debug("GET handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);

			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<Bookmark>(HttpStatus.UNAUTHORIZED);
			}

			final Bookmark entity = dataStore.readOne(memberId, contentSource, contentType, contentId);

			return new ResponseEntity<Bookmark>(entity, (null != entity) ? HttpStatus.OK : HttpStatus.NOT_FOUND);
		}
		catch(Throwable e)
		{
			log.error("GET handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId} encountered an unexpected fatal exception."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<Bookmark>(HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * Create or update a bookmark given it's natural key.
	 * A bookmark with a non-null id (primary key) indicates an update.
	 * A bookmark with no id indicates a create.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 * @param requestEntity the bookmark to save.  The natural key fields must match the url.
	 * @return new Bookmark and status 201 if created,
	 *         updated Bookmark and status 200 OK if updated,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 *         status 406 when attempting to create or update a new bookmark with a duplicate natural key
	 */
	@PostMapping("/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}")
	public ResponseEntity<?> save(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@PathVariable final String contentSource,
		@PathVariable final String contentType,
		@PathVariable final String contentId,
		@Valid @RequestBody Bookmark requestEntity)
	{
		try
		{
			log.debug("POST handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);

			//
			// The access token must match the member and site in the url.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			//
			// the request body must match the url
			//
			if (!contentSource.equals(requestEntity.getContentSource()))
			{
				log.warn("Url contentSource does not match request body.");
				return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
			}
			if (!memberId.equals(requestEntity.getMemberId()))
			{
				log.warn("Url memberId does not match request body.");
				return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
			}
			if (!contentType.equals(requestEntity.getContentType()))
			{
				log.warn("Url contentType does not match request body.");
				return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
			}
			if (!contentId.equals(requestEntity.getContentId()))
			{
				log.warn("Url contentId does not match request body.");
				return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
			}

			//
			// return 200 on update, 201 on create
			//
			final Bookmark responseEntity = bookmarkService.saveBookmark(authorizationHeader, requestEntity);

			return new ResponseEntity<Bookmark>(responseEntity, (null != requestEntity.getId()) ? HttpStatus.OK : HttpStatus.CREATED);
		}
		catch(DataIntegrityViolationException e)
		{
			//
			// this constraint violation may be because we tried to write the same natural key twice.
			// if so, let's just return a 200 to say the item was written.  This then makes save()
			// an idempotent function.
			//
			final Bookmark existingBookmark = dataStore.readOne(
				requestEntity.getMemberId(),
				requestEntity.getContentSource(),
				requestEntity.getContentType(),
				requestEntity.getContentId());
			if(null != existingBookmark)
			{
				// saved a dupe, so return the preexisting bookmark
				return new ResponseEntity<Bookmark>(existingBookmark, HttpStatus.OK);
			}

			//
			// constraint violation, some issue with the entity
			//
			log.info("POST handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId} encountered a constraint violation."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<Bookmark>(HttpStatus.NOT_ACCEPTABLE);
		}
		catch(TooManyBookmarksException e)
		{
			log.warn("Member has too many bookmarks. " + memberId + " source:" + requestEntity.getContentSource() + " type:" + requestEntity.getContentType());
			return new ResponseEntity<>("Reached maximum number of bookmarks.", HttpStatus.BAD_REQUEST);
		}
		catch(Throwable e)
		{
			log.error("POST handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId} encountered an unexpected fatal exception."
				.replace("{memberId}", (null != memberId) ? memberId : "null")
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null"),
					e
			);
			log.debug(e.getMessage());
			return new ResponseEntity<Bookmark>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * Create or update a bookmark given it's natural key.
	 * A bookmark with a non-null id (primary key) indicates an update.
	 * A bookmark with no id indicates a create.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param requestEntity the bookmark to save.  The natural key fields must match the url.
	 * @return new Bookmark and status 201 if created,
	 *         updated Bookmark and status 200 OK if updated,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 *         status 406 when attempting to create or update a new bookmark with a duplicate natural key
	 */
	@PostMapping("/v1/member/{memberId}/content")
	public ResponseEntity<?> save(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@Valid @RequestBody Bookmark requestEntity)
	{
		try
		{
			log.debug("POST handler /bookmark/v1/member/{memberId}/content");

			//
			// The access token must match the member and site in the url.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			if (!memberId.equals(requestEntity.getMemberId()))
			{
				log.warn("Url memberId does not match request body.");
				return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
			}

			//
			// return 200 on update, 201 on create
			//
			final Bookmark responseEntity = bookmarkService.saveBookmark(authorizationHeader, requestEntity);

			return new ResponseEntity<Bookmark>(responseEntity, (null != requestEntity.getId()) ? HttpStatus.OK : HttpStatus.CREATED);
		}
		catch(DataIntegrityViolationException e)
		{
			//
			// this constraint violation may be because we tried to write the same natural key twice.
			// if so, let's just return a 200 to say the item was written.  This then makes save()
			// an idempotent function.
			//
			final Bookmark existingBookmark = dataStore.readOne(
				requestEntity.getMemberId(),
				requestEntity.getContentSource(),
				requestEntity.getContentType(),
				requestEntity.getContentId());
			if(null != existingBookmark)
			{
				// saved a dupe, so return the preexisting bookmark
				return new ResponseEntity<>(existingBookmark, HttpStatus.OK);
			}

			//
			// constraint violation, some issue with the entity
			//
			log.info("POST handler /bookmark/v1/member/{memberId}/content encountered a constraint violation.");
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.NOT_ACCEPTABLE);
		}
		catch(TooManyBookmarksException e)
		{
			log.warn("Member has too many bookmarks. " + memberId + " source:" + requestEntity.getContentSource() + " type:" + requestEntity.getContentType());
			return new ResponseEntity<>("Reached maximum number of bookmarks.", HttpStatus.BAD_REQUEST);
		}
		catch(Throwable e)
		{
			log.error("POST handler /bookmark/v1/member/{memberId}/content encountered an unexpected fatal exception.");
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * Delete a single bookmark given it's natural key.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource 'bcsite' for US Content, 'social' for US Community
	 * @param contentType native content type
	 * @param contentId native content id
	 * @return 204 No content on success, 404 if entity is not found.
	 */
	@DeleteMapping("/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}")
	public ResponseEntity<Bookmark> delete(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@PathVariable final String contentSource,
		@PathVariable final String contentType,
		@PathVariable final String contentId)
	{
		try
		{
			log.debug("DELETE handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);

			//
			// The access token must match the member and site in the url.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}


			//
			// return 204 on successful delete, or 404 if not found
			//
			boolean deleted = deleteOne(authorizationHeader, memberId, contentSource, contentType, contentId);
			if (deleted)
			{
				return new ResponseEntity<Bookmark>(HttpStatus.NO_CONTENT);
			}
			return new ResponseEntity<Bookmark>(HttpStatus.NOT_FOUND);
		}
		catch(Throwable e)
		{
			log.error("POST handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId} encountered an unexpected fatal exception."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentId}", (null != contentId) ? contentId : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<Bookmark>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * Delete multiple bookmarks given their natural key.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource 'bcsite' for US Content, 'social' for US Community
	 * @param contentType native content type
	 * @param contentIds comma separated list of content ids
	 * @return 204 No content on success, 404 if entity is not found.
	 */
	@DeleteMapping("/v1/member/{memberId}/content/{contentSource}/{contentType}")
	public ResponseEntity<Bookmark> deleteMulti(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@PathVariable final String contentSource,
		@PathVariable final String contentType,
		@RequestParam(name = "contentIds") final List<String> contentIds)
	{
		try
		{
			log.debug("DELETE handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}?contentIds={contentIds}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentIds}", (null != contentIds) ? contentIds.toString() : "null")
			);

			//
			// The access token must match the member and site in the url.
			//
			if (!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			if (contentIds != null)
			{
				for (String contentId : contentIds)
				{
					deleteOne(authorizationHeader, memberId, contentSource, contentType, contentId);
				}
			}
			return new ResponseEntity<>(HttpStatus.OK);
		}
		catch (Throwable e)
		{
			log.error("DELETE handler /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}?contentIds={contentIds} encountered an unexpected fatal exception."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{contentIds}", (null != contentIds) ? contentIds.toString() : "null")
			);
			log.debug(e.getLocalizedMessage(), e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	private boolean deleteOne(String authorizationHeader, String memberId, String contentSource, String contentType, String contentId)
	{
		final Bookmark entity = dataStore.readOne(memberId, contentSource, contentType, contentId);
		if(null != entity)
		{
			bookmarkService.deleteBookmark(authorizationHeader, entity);
			return true;
		}
		return false;
	}

	/**
	 * Read the list of bookmarks for a given member.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource 'bcsite' for US Content, 'social' for US Community
	 * @param contentType native content type
	 * @return List of Bookmarks (may be empty) and status 200 OK,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 */
	@GetMapping("/v1/member/{memberId}/content/{contentSource}/{contentType}")
	public ResponseEntity<Iterable<Bookmark>> read(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@PathVariable final String contentSource,
		@PathVariable final String contentType)
	{
		try
		{
			log.debug("GET handler /bookmark/v1/member/{memberId}/content/{contentSource}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
			);

			//
			// The access token must match the member and site in the url.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			final Iterable<Bookmark> entities = dataStore.readAll(memberId, contentSource, contentType);

			return new ResponseEntity<>(entities, HttpStatus.OK);
		}
		catch(Throwable e)
		{
			log.error("GET handler /bookmark/v1/member/{memberId}/content/{contentSource} encountered an unexpected fatal exception."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * Read the list of bookmarks for a given member with pagination.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource 'bcsite' for US Content, 'social' for US Community
	 * @param contentType native content type
	 * @return List of Bookmarks (may be empty) and status 200 OK,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 */
	@GetMapping("/v2/member/{memberId}/content/{contentSource}/{contentType}")
	public ResponseEntity<PaginatedResults<Bookmark>> readPaginated(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@PathVariable final String contentSource,
		@PathVariable final String contentType,
		@RequestParam(required = false) final Integer pageIndex,
		@RequestParam(required = false) final Integer pageSize)
	{
		try
		{
			log.debug("GET handler /bookmark/v2/member/{memberId}/content/{contentSource}?pageIndex={pageIndex}&pageSize={pageSize}"
				.replace("{contentSource}",  String.valueOf(contentSource))
				.replace("{contentType}", String.valueOf(contentType))
				.replace("{pageIndex}", String.valueOf(pageIndex))
				.replace("{pageSize}", String.valueOf(pageSize))
			);

			//
			// The access token must match the member and site in the url.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			final int workingPageSize = (null != pageSize) ? pageSize : BookmarkDataStore.DEFAULT_PAGE_SIZE;
			final int workingPageOffset = (null != pageIndex) ? pageIndex : 0;

			final PaginatedResults<Bookmark> entities = dataStore.readAll(memberId, contentSource, contentType, workingPageOffset, workingPageSize);

			return new ResponseEntity<>(entities, HttpStatus.OK);
		}
		catch(Throwable e)
		{
			log.debug("GET handler /bookmark/v2/member/{memberId}/content/{contentSource}?pageIndex={pageIndex}&pageSize={pageSize}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
				.replace("{contentType}", (null != contentType) ? contentType : "null")
				.replace("{pageIndex}", (null != pageIndex) ? pageIndex.toString() : "null")
				.replace("{pageSize}", (null != pageSize) ? pageSize.toString() : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * Read the list of bookmarks for a given member and content source.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param contentSource 'bcsite' for US Content, 'social' for US Community
	 * @return List of Bookmarks (may be empty) and status 200 OK,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 */
	@GetMapping("/member/{memberId}/content/{contentSource}")
	public ResponseEntity<Iterable<Bookmark>> read(
		@RequestHeader(name = AUTH_HEADER_NAME, required = true) final String authorizationHeader,
		@PathVariable final String memberId,
		@PathVariable final String contentSource)
	{
		try
		{
			log.debug("GET handler /bookmark/v1/member/{memberId}/content/{contentSource}"
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
			);

			//
			// The access token must match the member and site in the url.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			final Iterable<Bookmark> entities = dataStore.readAll(memberId, contentSource);

			return new ResponseEntity<>(entities, HttpStatus.OK);
		}
		catch(Throwable e)
		{
			log.error("GET handler /bookmark/v1/member/{memberId}/content/{contentSource} encountered an unexpected fatal exception."
				.replace("{contentSource}", (null != contentSource) ? contentSource : "null")
			);
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * Create one or more bookmarks for the given member based on the passed list of bookmarks.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @param requestEntity the list of bookmarks to save for the member.
	 *                      Note that each entity's memberId must match the url.
	 * @return status 201 and new Bookmarks on success.
	 *         status 400 is any entity's member id does not match the url
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 *         status 406 if any entity already exists, based on natural key.
	 */
	@PostMapping("/member/{memberId}")
	public ResponseEntity<List<Bookmark>> save(
		@RequestHeader(AUTH_HEADER_NAME) final String authorizationHeader,
		@PathVariable final String memberId,
		@Valid @NotNull @Size(min = 1) @RequestBody List<Bookmark> requestEntity)
	{
		try
		{
			log.debug("POST handler /bookmark/v1/member/{memberId}");

			//
			// The access token must match the member.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			//
			// each entity must match the member id
			//
			for(Bookmark bookmark : requestEntity)
			{
				if(!memberId.equals(bookmark.getMemberId()))
				{
					log.warn("POST handler /bookmark/v1/member/{memberId} found mismatched memberId.");
					return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
				}
			}

			return new ResponseEntity<>(this.saveAll(authorizationHeader, requestEntity), HttpStatus.CREATED);
		}
		catch (DataIntegrityViolationException e)
		{
			//
			// constraint violation, probably on the natural key (attempt to bookmark the same thing twice)
			//
			log.info("POST handler /bookmark/v1 encountered a constraint violation.");
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.NOT_ACCEPTABLE);
		}
		catch (Throwable e)
		{
			log.error("POST handler /bookmark/v1");
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * Save those bookmarks that don't already exist.
	 *
	 * @param bookmarks
	 * @return
	 */
	private List<Bookmark> saveAll(String authorizationHeader, final List<Bookmark> bookmarks) throws TooManyBookmarksException {
		final List<Bookmark> toSave = new ArrayList<>(bookmarks.size());
		final List<Bookmark> saved = new ArrayList<>(bookmarks.size());
		//
		// We want to treat saving a duplicate bookmark as an idempotent operation.
		// So when saving a list, we need to remove pre-existing bookmarks
		// from the 'to save' list, but add them into the 'saved' list that is returned.
		// we do this so any error when saving the list is still an atomic all-or nothing transaction.
		//
		for(Bookmark bookmark : bookmarks)
		{
			//
			// read the bookmark by it's natural key
			//
			final Bookmark preexistingBookmark = dataStore.readOne(
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId());
			if(null != preexistingBookmark)
			{
				//
				// if we are writing a record with a primary key, then their should not
				// be a record that already has the natural key, as this would cause
				// a constraint violation
				//
				if((null != bookmark.getId()) && !bookmark.getId().equals(preexistingBookmark.getId()))
				{
					//
					// attempt to write a record with a duplicate natural key
					//
					throw new DataIntegrityViolationException("Attempt to write a record with a duplicate natural key");
				}

				// already saved
				saved.add(preexistingBookmark);
			}
			else
			{
				//
				// to be saved
				//
				toSave.add(bookmark);
			}
		}

		//
		// save the non-pre-existing and add to the result
		//
		if (!toSave.isEmpty()) {
			for (Bookmark bookmark : toSave) {
				Bookmark savedBookmark = bookmarkService.saveBookmark(authorizationHeader, bookmark);
				saved.add(savedBookmark);
			}
		}
		return saved;
	}

	/**
	 * Read the list of bookmarks for a given member.
	 *
	 * @param authorizationHeader 'Authorization' header contains  the access token
	 *                            for this member issued by auth.babycenter.com
	 * @param memberId global (auth service) member id
	 * @return List of Bookmarks (may be empty) and status 200 OK,
	 *         status 400 on malformed request,
	 *         status 401 on invalid access token (client should refresh access token and retry)
	 */
	@GetMapping("/member/{memberId}")
	public ResponseEntity<Iterable<Bookmark>> read(
		@RequestHeader(AUTH_HEADER_NAME) final String authorizationHeader,
		@PathVariable final String memberId)
	{
		try
		{
			log.debug("GET handler /bookmark/v1/member/{memberId}");

			//
			// The access token must match the member.
			//
			if(!isAccessTokenValid(authorizationHeader, memberId))
			{
				return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
			}

			final Iterable<Bookmark> entities = dataStore.readAll(memberId);

			return new ResponseEntity<>(entities, HttpStatus.OK);
		}
		catch(Throwable e)
		{
			log.error("GET handler /bookmark/v1/member/{memberId} encountered an unexpected fatal exception.");
			log.debug(e.getMessage());
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@Autowired
	public void setDataStore(BookmarkDataStore dataStore)
	{
		this.dataStore = dataStore;
	}

	@Autowired
	public void setAccessTokenRequestValidator(AccessTokenRequestValidator accessTokenRequestValidator)
	{
		this.accessTokenRequestValidator = accessTokenRequestValidator;
	}

	@Autowired
	public void setBookmarkService(BookmarkService bookmarkService)
	{
		this.bookmarkService = bookmarkService;
	}

}
