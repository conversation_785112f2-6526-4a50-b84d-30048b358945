package com.babycenter.bookmarkservice.auth.client;

import com.babycenter.bookmarkservice.dto.MemberInfoDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Service
public class MemberInfoClient
{

	private static final Logger log = LoggerFactory.getLogger(MemberInfoClient.class);

	private String authServiceBaseUrl;
	private RestTemplate restTemplate;

	@Value("${authServiceBaseUrl}")
	public void setAuthServiceBaseUrl(String authServiceBaseUrl)
	{
		this.authServiceBaseUrl = authServiceBaseUrl;
	}

	@Autowired
	public void setRestTemplate(RestTemplate restTemplate)
	{
		this.restTemplate = restTemplate;
	}

	public MemberInfoDto getMemberInfo(String authorizationHeader, String globalMemberId)
	{
		String url = authServiceBaseUrl + "/profile/member/" + globalMemberId;
		MultiValueMap<String, String> headers = new HttpHeaders();
		headers.add("Authorization", authorizationHeader);
		HttpEntity<?> httpEntity = new HttpEntity<>(headers);
		ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, Map.class);
		if (response.getStatusCode() == HttpStatus.NOT_FOUND)
		{
			return null;
		}
		Map<?, ?> body = response.getBody();
		List<?> members = (List<?>) body.get("member");
		if (members == null) {
			return null;
		}
		Map<?, ?> member = (Map<?, ?>) members.get(0);
		if (member == null) {
			return null;
		}
		long siteId = ((Number) member.get("id")).longValue();
		String email = (String) member.get("email");
		MemberInfoDto dto = new MemberInfoDto();
		dto.globalId = globalMemberId;
		dto.siteId = siteId;
		dto.email = email;
		return dto;
	}

}
