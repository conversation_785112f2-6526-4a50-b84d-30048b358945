package com.babycenter.bookmarkservice.auth;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoField;
import java.util.Base64;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Class to create a time-limited request token for authorizing
 * requests to microservices on behalf of a member.
 *
 * Usage:
 * - The client creates a request token using the current time and sends it
 *   along with the request.
 * - The server receives the request and create's it's own copy of the token
 *   using it's current time.  It also creates a token for the current time
 *   minus TIME_OUT_SECONDS.
 * - The server compares the token provided by the client to the two server tokens.
 * - The request is authorized if either server token matches the client token.
 *   Otherwise the request is not authorized.
 *
 * Created by emurphy on 5/25/16.
 */
public class RequestToken
{
	public final static int TIME_OUT_SECONDS = 120; // number of seconds in each token time window
	private final static String DELIMITER = ":";

	private static final Logger log = LoggerFactory.getLogger(RequestToken.class);


	/**
	 * Create a time-limited request token.
	 *
	 * @param theSharedSecret   secret shared between client and server
	 * @param itemId       id of member/visitor or any other identifier on who's behalf the request is being made
	 * @return a request token as a string.
	 */
	public static String token(final String theSharedSecret, final String itemId)
	{
		return token(theSharedSecret, itemId, ZonedDateTime.now());
	}


	/**
	 * Create a time-limited request token.
	 *
	 * @param theSharedSecret   secret shared between client and server
	 * @param itemId       id of member/visitor or any other identifier on who's behalf the request is being made
	 * @param theTime           the time of the request.
	 *
	 * @return a request token as a string.
	 */
	/* package private */ static String token(final String theSharedSecret, final String itemId, final ZonedDateTime theTime)
	{
		//
		// 1. convert theTime to UTC truncated to TIME_OUT_SECONDS.
		// 2. concatenate the secret, itemId and the UTC time and tokenize using SHA1
		// 3. create one-way hash using SHA-1
		// 4. return hash as base-36 string
		//

		// 1. convert theTime to UTC truncated to TIME_OUT_SECONDS.
		final ZonedDateTime theUtcTime = ZonedDateTime.ofInstant(theTime.toInstant(), ZoneOffset.UTC);
		final String theTokenTime = String.valueOf((theUtcTime.getLong(ChronoField.INSTANT_SECONDS) / TIME_OUT_SECONDS)  * TIME_OUT_SECONDS);

		log.debug("token secret is " + theSharedSecret);
		log.debug("token id is " + itemId);
		log.debug("token time is " + theTokenTime);

		// 2. concatenate the secret, itemId and the UTC time
		final String theTokenString = theSharedSecret.trim() + DELIMITER + itemId + DELIMITER + theTokenTime;

		log.debug("token is " + theTokenString);

		try
		{
			// 3. create one-way hash using SHA-1
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			byte[] hash = digest.digest(theTokenString.getBytes("UTF-8"));
			final BigInteger theBigInteger = new BigInteger(hash);

			// 4. return hash as base-36 string
			return theBigInteger.toString(36);
		}
		catch (RuntimeException ex)
		{
			throw ex;
		}
		catch (Exception ex)
		{
			throw new RuntimeException(ex);
		}
	}

	/**
	 * The server compares the token provided by the client to the two server tokens. Current Token and current
	 * time minus the TIME_OUT_SECONDS. If the client Token matches any of the server token the Token is valid
	 *
	 * @param theSharedSecret
	 * @param theResourceId
	 * @param token token send by the client
	 * @return true if the  client token is valid, false if not
	 */
	public static boolean isTokenValid(final String theSharedSecret, final String theResourceId, final String token)
	{
		log.debug("validating token");
		ZonedDateTime currentTime = ZonedDateTime.now();
		log.debug("current time: " + currentTime);
		String tokenCurrent = token(theSharedSecret, theResourceId, currentTime);

		ZonedDateTime priorTime = currentTime.minusSeconds(TIME_OUT_SECONDS);
		log.debug("prior time: " + priorTime);
		String tokenPrior = token(theSharedSecret, theResourceId, priorTime);

		String base64Token = nonTimedToken(theSharedSecret, theResourceId);

		if (token.equals(tokenCurrent) || token.equals(tokenPrior) || token.equals(base64Token))
		{
			return true;
		}
		return false;
	}

	/**
	 * Create a non-timed base64 request token.
	 *
	 * @param theSharedSecret   secret shared between client and server
	 * @param itemId       id of member/visitor or any other identifier on who's behalf the request is being made
	 *
	 * @return a request token as a string.
	 */
	/* package private */ static String nonTimedToken(final String theSharedSecret, final String itemId)
	{
		//
		// 1. concatenate the secret, itemId - removed timeout from token
		// 2. create one-way hash using SHA-256
		// 3. return hash as base-64 encoded string
		//

		// 1. concatenate the secret and itemId
		final String theTokenString = theSharedSecret.trim() + DELIMITER + itemId;

		log.debug("token secret is " + theSharedSecret);
		log.debug("token id is " + itemId);
		log.debug("token is " + theTokenString);

		try {
			// 2. create one-way hash using SHA-1
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			byte[] hash = digest.digest(theTokenString.getBytes("UTF-8"));
			String encodedHash = Base64.getEncoder().encodeToString(hash);
			log.debug("base 64 encoded hash: " + encodedHash);
			// 3. return hash a base-64 encoded string
			return encodedHash;
		}
		catch (RuntimeException ex) {
			throw ex;
		}
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
	}

}
