package com.babycenter.bookmarkservice.auth;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.babycenter.bookmarkservice.auth.client.ValidationKeyClient;

import java.util.Date;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created by emurphy on 4/18/17.
 */
@Component
public class AccessTokenRequestValidator
{
	private static final Logger log = LoggerFactory.getLogger(AccessTokenRequestValidator.class);

	private final VerifyAlgorithmProducer verifyAlgorithmProducer;
	private final ValidationKeyProducer validationKeyProducer;
	private final String validationKeyIssuer;
	
	
	public AccessTokenRequestValidator(
		@Autowired final VerifyAlgorithmProducer verifyAlgorithmProducer,
		@Autowired final ValidationKeyProducer validationKeyProducer,
		@Value("${authServiceIssuer}") final String validationKeyIssuer)
	{
		this.verifyAlgorithmProducer = verifyAlgorithmProducer;
		this.validationKeyProducer = validationKeyProducer;
		this.validationKeyIssuer = validationKeyIssuer;
	}
	
	
	/**
	 * Validate an accessToken string for calls the require memberId.
	 * The access token must decode and must
	 * - have a valid expiration date that is in the future
	 * - have a subject that matches the desired userId
	 *
	 * @param accessToken access token as a string
	 * @param userId      global auth id for user
	 * @return true if valid, false if not
	 */
	public boolean isValid(final String accessToken, final String userId)
	{
		final DecodedJWT decodedJWT = decode(accessToken);
		
		if (null == decodedJWT) return false;
		
		//
		// call is made on behalf of a member, so their must match the url path to their resource
		//
		if ((null == decodedJWT.getSubject()) || !decodedJWT.getSubject().equals(userId)) return false;
		
		return true;
	}
	
	private DecodedJWT decode(final String accessToken)
	{
		try
		{
			Algorithm algorithm = verifyAlgorithmProducer.verifyAlgorithm(validationKeyProducer.validationKey());
			JWTVerifier verifier = JWT.require(algorithm)
				.withIssuer(this.validationKeyIssuer)
				.build(); //Reusable verifier instance
			DecodedJWT jwt = verifier.verify(accessToken);
			return jwt;
		}
		catch (JWTVerificationException exception)
		{
			//Invalid signature/claims
			log.error(exception.getMessage());
			return null;
		}
		catch (Exception e)
		{
			log.error(e.getMessage());
			throw e;
		}
		
	}
	
}
