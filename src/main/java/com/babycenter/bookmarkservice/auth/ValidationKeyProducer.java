package com.babycenter.bookmarkservice.auth;

import com.babycenter.bookmarkservice.auth.client.ValidationKeyClient;

import java.util.concurrent.atomic.AtomicReference;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Created by emurphy on 4/19/17.
 */
@Component
public class ValidationKeyProducer
{
	private AtomicReference<String> authServiceValidationKey = new AtomicReference<>();
	private final ValidationKeyClient validationKeyClient;
	
	public ValidationKeyProducer(@Autowired ValidationKeyClient validationKeyClient)
	{
		this.validationKeyClient = validationKeyClient;
	}
	
	public String validationKey()
	{
		//
		// If we have not set the value yet, then do so now.
		// There is a small race condition here, but it is not important
		// since getValidationKey is an idempotent operation.  so it may be
		// set more than once, but always to the same value.
		//
		if (null == this.authServiceValidationKey.get())
		{
			this.authServiceValidationKey.set(validationKeyClient.getValidationKey());
		}
		
		return this.authServiceValidationKey.get();
	}
	
	//
	// set validatation key on startup.
	// if it is not found, then it will set null, which
	// will then force the app to call the auth server to get it.
	//
	@Value("${authServiceValidationKey:null}")
	public void setAuthServiceValidationKey(final String authServiceValidationKey)
	{
		this.authServiceValidationKey.set("null".equals(authServiceValidationKey) ? null : authServiceValidationKey);
	}
	
}
