package com.babycenter.bookmarkservice.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * Class that represents a bookmark.
 *
 * The natural key is;
 *   memberId, the global (auth server) member id
 *   local, en_US for bcsite and Us Community
 *   contentType, the platform-native content type
 *   contentId, the platform-native content id
 *
 * Created by emurphy on 3/15/17.
 */
@Entity
@Table(name = "bookmark")
//@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Bookmark implements Cloneable
{
	@Id
	@GeneratedValue
	@Column(name = "id")
	private Long id;

	@NotNull
	@Length(min = 1, max = 16)
	@Column(name = "member_id")
	private String memberId;   // global (auth service) member id

	@NotNull
	@Length(min = 1, max = 16)
	@Column(name = "contentSource")
	private String contentSource;

	@NotNull
	@Length(min = 1, max = 63)
	@Column(name = "content_type")
	private String contentType; // platform specific content type

	@NotNull
	@Length(min = 1, max = 63)
	@Column(name = "content_id")
	private String contentId;   // platform specific id

	/* ------------ audit fields ---------------*/
	@Column(name = "create_user", updatable = false)
	private String createUser;

	@Column(name = "update_user")
	private String updateUser;

	@Column(name = "create_date", updatable = false)
	private Date createDate;

	@JsonIgnore
	@Column(name = "update_date", insertable = false, updatable = false)
	@Generated(GenerationTime.ALWAYS)
	private Date updateDate;
	/* ------------ audit fields ---------------*/

	public Bookmark() { super(); }  // no-arg constructor necessary for jpa and json serialization

	/**
	 * construct with natural key.
	 *
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 */
	public Bookmark(final String memberId, final String contentSource, final String contentType, final String contentId)
	{
		super();

		if((null == memberId) || memberId.isEmpty()) throw new IllegalArgumentException();
		if((null == contentSource) || contentSource.isEmpty()) throw new IllegalArgumentException();
		if((null == contentType) || contentType.isEmpty()) throw new IllegalArgumentException();
		if((null == contentId) || contentId.isEmpty()) throw new IllegalArgumentException();

		this.memberId = memberId;
		this.contentSource = contentSource;
		this.contentType = contentType;
		this.contentId = contentId;
	}

	/**
	 * complete constructor - requires all fields.
	 *
	 * @param id auto-generated primary key supplied by datastore
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 * @param createUser user supplied by caller
	 * @param updateUser user supplied by caller
	 * @param createDate date supplied by datastore
	 * @param updateDate date supplied by datastore
	 */
	public Bookmark(final Long id, final String memberId, final String contentSource, final String contentType, final String contentId,
	                final String createUser, final String updateUser, final Date createDate, final Date updateDate)
	{
		this(memberId, contentSource, contentType, contentId);

		this.id = id;
		this.createUser = createUser;
		this.updateUser = updateUser;
		this.createDate = createDate;
		this.updateDate = updateDate;
	}

	/**
	 * Copy constructor.
	 *
	 * @param that
	 */
	public Bookmark(final Bookmark that)
	{
		this(that.getId(), that.getMemberId(), that.getContentSource(), that.getContentType(), that.getContentId(),
			that.getCreateUser(), that.getUpdateUser(), that.getCreateDate(), that.getUpdateDate());
	}

	public Long getId()
	{
		return id;
	}

	public void setId(Long id)
	{
		this.id = id;
	}

	public String getMemberId()
	{
		return memberId;
	}

	public void setMemberId(String memberId)
	{
		this.memberId = memberId;
	}

	public String getContentSource()
	{
		return contentSource;
	}

	public void setContentSource(String contentSource)
	{
		this.contentSource = contentSource;
	}

	public String getContentType()
	{
		return contentType;
	}

	public void setContentType(String contentType)
	{
		this.contentType = contentType;
	}

	public String getContentId()
	{
		return contentId;
	}

	public void setContentId(String contentId)
	{
		this.contentId = contentId;
	}

	/* ------------ audit fields ---------------*/
	public String getCreateUser() { return createUser; }
	public void setCreateUser(String createUser) { this.createUser = createUser; }

	public String getUpdateUser() { return updateUser; }
	public void setUpdateUser(String updateUser) { this.updateUser = updateUser; }

	/*
	 * createDate is serialized into json,
	 * but ignored when deserializing json.
	 */
	@JsonProperty public Date getCreateDate() { return createDate; }
	@JsonIgnore public void setCreateDate(Date createDate) { this.createDate = createDate; }


	/*
	 * updateDate is serialized into json,
	 * but ignored when deserializing json.
	 */
	@JsonProperty public Date getUpdateDate() { return updateDate; }
	@JsonIgnore public void setUpdateDate(Date updateDate) { this.updateDate = updateDate; }
	/* ------------ audit fields ---------------*/

	@Override
	public Object clone() throws CloneNotSupportedException
	{
		return super.clone();
	}


	@Override
	public boolean equals(Object o)
	{
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;

		Bookmark bookmark = (Bookmark) o;

		if (getId() != null ? !getId().equals(bookmark.getId()) : bookmark.getId() != null) return false;
		if (getMemberId() != null ? !getMemberId().equals(bookmark.getMemberId()) : bookmark.getMemberId() != null)
			return false;
		if (getContentSource() != null ? !getContentSource().equals(bookmark.getContentSource()) : bookmark.getContentSource() != null)
			return false;
		if (getContentType() != null ? !getContentType().equals(bookmark.getContentType()) : bookmark.getContentType() != null)
			return false;
		if (getContentId() != null ? !getContentId().equals(bookmark.getContentId()) : bookmark.getContentId() != null)
			return false;
		if (getCreateUser() != null ? !getCreateUser().equals(bookmark.getCreateUser()) : bookmark.getCreateUser() != null)
			return false;
		if (getUpdateUser() != null ? !getUpdateUser().equals(bookmark.getUpdateUser()) : bookmark.getUpdateUser() != null)
			return false;
		if (getCreateDate() != null ? !getCreateDate().equals(bookmark.getCreateDate()) : bookmark.getCreateDate() != null)
			return false;
		return getUpdateDate() != null ? getUpdateDate().equals(bookmark.getUpdateDate()) : bookmark.getUpdateDate() == null;

	}

	@Override
	public int hashCode()
	{
		int result = getId() != null ? getId().hashCode() : 0;
		result = 31 * result + (getMemberId() != null ? getMemberId().hashCode() : 0);
		result = 31 * result + (getContentSource() != null ? getContentSource().hashCode() : 0);
		result = 31 * result + (getContentType() != null ? getContentType().hashCode() : 0);
		result = 31 * result + (getContentId() != null ? getContentId().hashCode() : 0);
		result = 31 * result + (getCreateUser() != null ? getCreateUser().hashCode() : 0);
		result = 31 * result + (getUpdateUser() != null ? getUpdateUser().hashCode() : 0);
		result = 31 * result + (getCreateDate() != null ? getCreateDate().hashCode() : 0);
		result = 31 * result + (getUpdateDate() != null ? getUpdateDate().hashCode() : 0);
		return result;
	}
}
