package com.babycenter.bookmarkservice.business;

import com.babycenter.bookmarkservice.model.Bookmark;

/**
 * Created by emurphy on 3/16/17.
 */
public final class BookmarkHelper
{
	private BookmarkHelper(){ throw new RuntimeException();}

	public static Bookmark cloneOf(final Bookmark bookmark)
	{
		try
		{
			return (Bookmark)((null != bookmark) ? bookmark.clone() : null);
		}
		catch (CloneNotSupportedException e)
		{
			throw new RuntimeException(e);
		}
	}

}
