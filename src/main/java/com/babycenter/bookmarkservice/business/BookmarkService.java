package com.babycenter.bookmarkservice.business;

import com.babycenter.bookmarkservice.event.ProfileEventService;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.persistence.BookmarkDataStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BookmarkService
{

	public static final long MAX_BOOKMARK_COUNT = 200L;

	private BookmarkDataStore dataStore;
	private ProfileEventService profileEventService;

	@Autowired
	public void setDataStore(BookmarkDataStore dataStore)
	{
		this.dataStore = dataStore;
	}

	@Autowired
	public void setProfileEventService(ProfileEventService profileEventService)
	{
		this.profileEventService = profileEventService;
	}

	public Bookmark saveBookmark(String authorizationHeader, Bookmark bookmark) throws TooManyBookmarksException {
		String memberId = bookmark.getMemberId();
		String contentSource = bookmark.getContentSource();
		String contentType = bookmark.getContentType();

		long count = dataStore.countByMemberIdAndContentSourceAndContentType(memberId, contentSource, contentType);
		if (count >= MAX_BOOKMARK_COUNT)
		{
			throw new TooManyBookmarksException();
		}

		bookmark = dataStore.saveOne(bookmark);

		if ("bcsite".equals(contentSource) && "question".equals(contentType))
		{
			profileEventService.sendMomAnswersSubscriptionEvent(authorizationHeader, bookmark, true);
		}
		else if (contentSource.startsWith("community") && "post".equals(contentType))
		{
			profileEventService.sendSubscriptionEvent(authorizationHeader, bookmark, true);
		}

		return bookmark;
	}

	public void deleteBookmark(String authorizationHeader, Bookmark bookmark)
	{
		dataStore.deleteOne(bookmark.getId());

		if ("bcsite".equals(bookmark.getContentSource()) && "question".equals(bookmark.getContentType()))
		{
			profileEventService.sendMomAnswersSubscriptionEvent(authorizationHeader, bookmark, false);
		}
		else if (bookmark.getContentSource().startsWith("community") && "post".equals(bookmark.getContentType()))
		{
			profileEventService.sendSubscriptionEvent(authorizationHeader, bookmark, false);
		}
	}

}
