package com.babycenter.bookmarkservice.persistence.jpa;

import com.babycenter.bookmarkservice.model.Bookmark;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

@Transactional
public interface BookmarkRepository extends JpaRepository<Bookmark, Long>
{
	@Query("from Bookmark r where r.memberId=:memberId and r.contentSource=:contentSource and r.contentType=:contentType and r.contentId=:contentId")
	Iterable<Bookmark> readOne(
		@Param("memberId") final String memberId,
		@Param("contentSource") final String contentSource,
		@Param("contentType") final String contentType,
		@Param("contentId") final String contentId);

	@Query("from Bookmark r where r.memberId=:memberId and r.contentSource=:contentSource and r.contentType=:contentType")
	Iterable<Bookmark> readAll(
		@Param("memberId") final String memberId,
		@Param("contentSource") final String contentSource,
		@Param("contentType") final String contentType);

	@Query("from Bookmark r where r.memberId=:memberId and r.contentSource=:contentSource")
	Iterable<Bookmark> readAll(
		@Param("memberId") final String memberId,
		@Param("contentSource") final String contentSource);

	@Query("from Bookmark r where r.memberId=:memberId")
	Iterable<Bookmark> readAll(@Param("memberId") final String memberId);

	@Query("from Bookmark r where r.memberId=:memberId and r.contentSource=:contentSource and r.contentType=:contentType")
	Page<Bookmark> readAll(
		@Param("memberId") final String memberId,
		@Param("contentSource") final String contentSource,
		@Param("contentType") final String contentType,
		Pageable pageable);

	@Query("from Bookmark r where r.contentSource=:contentSource and r.contentType=:contentType and r.contentId=:contentId")
	Page<Bookmark> readAllContent(
		@Param("contentSource") final String contentSource,
		@Param("contentType") final String contentType,
		@Param("contentId") final String contentId,
		Pageable pageable);

	Page<Bookmark> findByContentSourceAndContentType(String contentSource, String contentType, Pageable page);

	List<Bookmark> findByContentSourceAndContentTypeAndContentId(String contentSource, String contentType, String contentId);

	void deleteByMemberIdAndContentSourceAndContentTypeAndContentIdIn(String memberId, String contentSource, String contentType, List<String> ids);

	long countByMemberIdAndContentSourceAndContentType(String memberId, String contentSource, String contentType);
}
