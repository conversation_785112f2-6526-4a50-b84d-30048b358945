package com.babycenter.bookmarkservice.persistence.jpa;

import com.babycenter.bookmarkservice.business.BookmarkHelper;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.model.PaginatedResults;
import com.babycenter.bookmarkservice.persistence.BookmarkDataStore;

import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

/**
 * Created by emurphy on 3/16/17.
 */
@Component
public class BookmarkDataStoreJpa implements BookmarkDataStore
{
	private BookmarkRepository repository;

	@Override
	public Bookmark saveOne(Bookmark bookmark)
	{
		//Prevent to update createDate
		if(bookmark.getId() != null){
			setCreateDate(bookmark);
		}
		return repository.save(bookmark);
	}

	@Override
	public List<Bookmark> saveAll(List<Bookmark> bookmarks)
	{
		bookmarks.stream().map(bookmark ->
		{
			if (bookmark.getId() != null) throw new DataIntegrityViolationException("Can't update, the natural key already exists.");
			return bookmark;
		});
		return repository.save(bookmarks);
	}


	private void setCreateDate(Bookmark bookmark)
	{
		Bookmark existing = readOne(bookmark.getId());
		bookmark.setCreateDate(existing.getCreateDate());
	}

	@Override
	public Bookmark readOne(Long id)
	{
		return repository.findOne(id);
	}


	@Override
	public Bookmark readOne(String memberId, String contentSource, String contentType, String contentId)
	{
		final Iterator<Bookmark> entity = repository.readOne(memberId, contentSource, contentType, contentId).iterator();
		return entity.hasNext() ? entity.next() : null;
	}

	@Override
	public Iterable<Bookmark> readAll(String memberId, String contentSource, String contentType)
	{
		return repository.readAll(memberId, contentSource, contentType);
	}

	@Override
	public Iterable<Bookmark> readAll(String memberId, String contentSource)
	{
		return repository.readAll(memberId, contentSource);
	}

	@Override
	public Iterable<Bookmark> readAll(String memberId)
	{
		return repository.readAll(memberId);
	}

	@Override
	public PaginatedResults<Bookmark> readAll(
		final String memberId,
		final String contentType,
		final String contentId,
		final Integer pageIndex,
		final Integer pageSize)
	{
		final int workingPageSize = (null != pageSize) ? pageSize : BookmarkDataStore.DEFAULT_PAGE_SIZE;
		final int workingPageOffset = (null != pageIndex) ? pageIndex : 0;

		final PageRequest pageRequest = new PageRequest(workingPageOffset, workingPageSize);

		final Page<Bookmark> page = repository.readAll(memberId, contentType, contentId, pageRequest);
		return new PaginatedResults(page.getContent(), page.getTotalElements());
	}

	@Override
	public PaginatedResults<Bookmark> readAllContent(
		final String contentSource,
		final String contentType,
		final String contentId,
		final Integer pageIndex,
		final Integer pageSize)
	{
		final int workingPageSize = (null != pageSize) ? pageSize : BookmarkDataStore.DEFAULT_PAGE_SIZE;
		final int workingPageOffset = (null != pageIndex) ? pageIndex : 0;

		final PageRequest pageRequest = new PageRequest(workingPageOffset, workingPageSize);

		final Page<Bookmark> page = repository.readAllContent(contentSource, contentType, contentId, pageRequest);
		return new PaginatedResults(page.getContent(), page.getTotalElements());
	}

	@Override
	public void deleteOne(Long id)
	{
		repository.delete(id);
	}

	@Override
	public void deleteAll(String memberId, String contentSource, String contentType, List<String> ids)
	{
		repository.deleteByMemberIdAndContentSourceAndContentTypeAndContentIdIn(memberId, contentSource, contentType, ids);
	}

	@Override
	public long countByMemberIdAndContentSourceAndContentType(String memberId, String contentSource, String contentType) {
		return repository.countByMemberIdAndContentSourceAndContentType(memberId, contentSource, contentType);
	}

	@Autowired
	public void setRepository(BookmarkRepository repository)
	{
		this.repository = repository;
	}
}
