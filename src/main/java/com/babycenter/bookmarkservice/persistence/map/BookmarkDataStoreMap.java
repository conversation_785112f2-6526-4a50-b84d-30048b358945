package com.babycenter.bookmarkservice.persistence.map;

import com.babycenter.bookmarkservice.business.BookmarkHelper;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.model.PaginatedResults;
import com.babycenter.bookmarkservice.persistence.BookmarkDataStore;
import com.babycenter.bookmarkservice.util.TimestampDate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import org.springframework.dao.DataIntegrityViolationException;

/**
 * Created by emurphy on 3/16/17.
 */
public final class BookmarkDataStoreMap implements BookmarkDataStore
{
	/**
	 * map the entities by natural key
	 */
	private final Map<String, Bookmark> map = new ConcurrentHashMap<>();
	private final AtomicLong idGenerator = new AtomicLong(0);

	@Override
	public Bookmark saveOne(final Bookmark bookmark)
	{
		//
		// this will validate the natural key fields
		//
		final String naturalKey = naturalKey(bookmark);

		//
		// validate that we don't violate unique natural key constraint
		//
		if(null == bookmark.getId())
		{
			//
			// it's an insert, there should not be an entity with the same natural key
			//
			if(null != map.get(naturalKey)) throw new DataIntegrityViolationException("Can't insert: the natural key already exists");
		}
		else
		{
			//
			// it's an update or insert with id, make sure natural key is already associated with this primary key
			//
			final Bookmark entity = map.get(naturalKey);
			if((null != entity)) {
				if(!bookmark.getId().equals(entity.getId())) throw new DataIntegrityViolationException("Can't update, the natural key already exists with a different id.");

				//Create Date can not be updated
				bookmark.setCreateDate(entity.getCreateDate());
			}
		}

		//
		// create defensive copy with persistent id, so changes to original don't affect datastore
		// set the update date and create date (if this is a create)
		//
		final Bookmark clone = BookmarkHelper.cloneOf(bookmark);
		clone.setUpdateDate(new TimestampDate());
		if(null == clone.getId())
		{
			clone.setId(idGenerator.incrementAndGet());
			clone.setCreateDate(clone.getUpdateDate());
		}

		map.put(naturalKey, clone);
		return BookmarkHelper.cloneOf(clone); // return defensive copy so changes don't affect datastore
	}

	@Override
	public List<Bookmark> saveAll(List<Bookmark> bookmarks)
	{
		return bookmarks.stream().map(this::saveOne).collect(Collectors.toList());
	}

	public Bookmark readOne(Long id)
	{
		final List<Bookmark> entity = map.values().stream()
			.filter(item -> id.equals(item.getId()))
			.limit(1)
			.collect(Collectors.toList());
		return entity.isEmpty() ? null : BookmarkHelper.cloneOf(entity.get(0));
	}

	@Override
	public Bookmark readOne(String memberId, String contentSource, String contentType, String contentId)
	{
		return BookmarkHelper.cloneOf(map.get(naturalKey(memberId, contentSource, contentType, contentId)));
	}

	@Override
	public Iterable<Bookmark> readAll(
		final String memberId,
		final String contentSource,
		final String contentType
	)
	{
		return map.values().stream()
			.filter(item -> memberId.equals(item.getMemberId()) && contentSource.equals(item.getContentSource()) && contentType.equals(item.getContentType()))
			.map(BookmarkHelper::cloneOf)
			.collect(Collectors.toList());
	}

	@Override
	public Iterable<Bookmark> readAll(String memberId, String contentSource)
	{
		return map.values().stream()
			.filter(item -> memberId.equals(item.getMemberId()) && contentSource.equals(item.getContentSource()))
			.map(BookmarkHelper::cloneOf)
			.collect(Collectors.toList());
	}

	@Override
	public Iterable<Bookmark> readAll(String memberId)
	{
		return map.values().stream()
			.filter(item -> memberId.equals(item.getMemberId()))
			.map(BookmarkHelper::cloneOf)
			.collect(Collectors.toList());
	}

	@Override
	public PaginatedResults<Bookmark> readAll(
		final String memberId,
		final String contentSource,
		final String contentType,
		final Integer pageIndex,
		final Integer pageSize)
	{
		final int workingPageSize = (null != pageSize) ? pageSize : BookmarkDataStore.DEFAULT_PAGE_SIZE;
		final int workingPageOffset = (null != pageIndex) ? pageIndex : 0;

		List<Bookmark> bookmarks = map.values().stream()
			.filter(item ->
					memberId.equals(item.getMemberId())
					&& contentSource.equals(item.getContentSource())
					&& contentType.equals(item.getContentType())
					)
			.collect(Collectors.toList());

		long totalElements = bookmarks.size();

		bookmarks = bookmarks.stream()
			.skip(workingPageOffset * workingPageSize)
			.limit(workingPageSize)
			.map(BookmarkHelper::cloneOf)
			.collect(Collectors.toList());

		return new PaginatedResults(bookmarks, totalElements);
	}

	@Override
	public PaginatedResults<Bookmark> readAllContent(
		final String contentSource,
		final String contentType,
		final String contentId,
		final Integer pageIndex,
		final Integer pageSize)
	{
		final int workingPageSize = (null != pageSize) ? pageSize : BookmarkDataStore.DEFAULT_PAGE_SIZE;
		final int workingPageOffset = (null != pageIndex) ? pageIndex : 0;

		List<Bookmark> bookmarks = map.values().stream()
			.filter(item ->
				contentSource.equals(item.getContentSource())
					&& contentType.equals(item.getContentType())
					&& contentId.equals(item.getContentId()))
			.collect(Collectors.toList());

		long totalElements = bookmarks.size();

		bookmarks = bookmarks.stream()
			.skip(workingPageOffset * workingPageSize)
			.limit(workingPageSize)
			.map(BookmarkHelper::cloneOf)
			.collect(Collectors.toList());

		return new PaginatedResults(bookmarks, totalElements);
	}

	@Override
	public void deleteOne(Long id)
	{
		final List<Bookmark> entity = map.values().stream()
			.filter(item -> id.equals(item.getId()))
			.collect(Collectors.toList());
		if(!entity.isEmpty())
		{
			map.remove(naturalKey(entity.get(0)));
		}
	}

	@Override
	public void deleteAll(String memberId, String contentSource, String contentType, List<String> ids)
	{
		final List<Bookmark> entity = map.values().stream()
			.filter(item -> item.getMemberId().equals(memberId)
				&& item.getContentSource().equals(contentSource)
				&& item.getContentType().equals(contentType)
				&& ids.contains(item.getContentId()))
			.collect(Collectors.toList());
		if(!entity.isEmpty())
		{
			entity.forEach(e -> map.remove(naturalKey(e)));
		}
	}

	@Override
	public long countByMemberIdAndContentSourceAndContentType(String memberId, String contentSource, String contentType) {
		return map.values().stream().filter(bookmark ->
				Objects.equals(bookmark.getMemberId(), memberId) &&
				Objects.equals(bookmark.getContentSource(), contentSource) &&
				Objects.equals(bookmark.getContentType(), contentType)
		).count();
	}

	/**
	 * validate and format the natural key for an entity.
	 *
	 * @param memberId global (auth service) member id
	 * @param contentSource 'bcsite' for US Content, 'social' for US Community
	 * @param contentType native content type
	 * @param contentId native content id
	 * @return the natural key
	 * @throws IllegalArgumentException if any part of key is null or empty
	 */
	private static final String naturalKey(String memberId, String contentSource, String contentType, String contentId)
	{
		if((null == memberId) || memberId.isEmpty()) throw new IllegalArgumentException();
		if((null == contentSource) || contentSource.isEmpty()) throw new IllegalArgumentException();
		if((null == contentType) || contentType.isEmpty()) throw new IllegalArgumentException();
		if((null == contentId) || contentId.isEmpty()) throw new IllegalArgumentException();

		final StringBuilder builder = new StringBuilder();
		builder.append(memberId)
			.append('+')
			.append(contentSource)
			.append('+')
			.append(contentType)
			.append('+')
			.append(contentId);

		return builder.toString();
	}

	/**
	 * validate and format the natural key for a bookmark.
	 *
	 * @param bookmark
	 * @return the natural key
	 * @throws IllegalArgumentException if any part of key is null or empty
	 */
	public static final String naturalKey(final Bookmark bookmark)
	{
		if(null == bookmark) throw new IllegalArgumentException();

		return naturalKey(bookmark.getMemberId(), bookmark.getContentSource(), bookmark.getContentType(), bookmark.getContentId());
	}


}
