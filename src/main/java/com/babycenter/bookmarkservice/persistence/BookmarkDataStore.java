package com.babycenter.bookmarkservice.persistence;

import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.model.PaginatedResults;

import java.util.List;

/**
 * Created by emurphy on 3/16/17.
 */
public interface BookmarkDataStore
{
	public static final int DEFAULT_PAGE_SIZE = 100;

	/**
	 * Create or update a bookmark given it's natural key.
	 *
	 * @param bookmark the bookmark to save, must have a full natural key
	 * @return the saved bookmark with an auto-generated primary key
	 */
	Bookmark saveOne(final Bookmark bookmark);

	/**
	 * Create or update a bookmark given it's natural key.
	 *
	 * @param bookmarks bookmarks to save, must have a full natural key
	 * @return saved bookmarks with an auto-generated primary key
	 */
	List<Bookmark> saveAll(final List<Bookmark> bookmarks);

	/**
	 * Read a single bookmark given it's primary key.
	 *
	 * @param id non-empty primary key
	 * @return a single bookmark or null if the bookmark does not exists
	 */
	Bookmark readOne(final Long id);

	/**
	 * Read one bookmark given it's natural key.
	 *
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 * @return a single bookmark or null if the bookmark does not exists
	 */
	Bookmark readOne(final String memberId, final String contentSource, final String contentType, final String contentId);

	/**
	 * Read all the bookmarks for a given user in a given source with the given content type.
	 *
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @return the set of bookmarks.  This may be empty, but will not be null.
	 */
	Iterable<Bookmark> readAll(final String memberId, final String contentSource, final String contentType);


	/**
	 * Read all the bookmarks for a given user in a given source.
	 *
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @return the set of bookmarks.  This may be empty, but will not be null.
	 */
	Iterable<Bookmark> readAll(final String memberId, final String contentSource);

	/**
	 * Read all the bookmarks for a given user.
	 *
	 * @param memberId global (auth service) member id
	 * @return the set of bookmarks.  This may be empty, but will not be null.
	 */
	Iterable<Bookmark> readAll(final String memberId);

	/**
	 * Read all the bookmarks for a given user in a given source with the given content type.
	 *
	 * @param memberId global (auth service) member id
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @return the set of bookmarks.  This may be empty, but will not be null.
	 * @param pageIndex index of page in results (zero based index of pageSize pages) defaults to zero
	 * @param pageSize maximum number of results to return (may return less), defaults to 100;
	 * @return the set of bookmarks.  This may be empty, but will not be null.
	 */
	PaginatedResults<Bookmark> readAll(
		final String memberId,
	    final String contentSource,
	    final String contentType,
		final Integer pageIndex,
		final Integer pageSize);

	/**
	 * Read all the bookmarks for a given piece of content.
	 * This essentially provides all the members that have
	 * bookmarked a piece of content.  This can be used to
	 * notify those members of changes to the content.
	 *
	 * @param contentSource "bcsite" OR "community:{country_code}"
	 * @param contentType native content type
	 * @param contentId native content id
	 * @param pageIndex index of page in results (zero based index of pageSize pages) defaults to zero
	 * @param pageSize maximum number of results to return (may return less), defaults to 100;
	 * @return the set of bookmarks.  This may be empty, but will not be null.
	 */
	PaginatedResults<Bookmark> readAllContent(
		final String contentSource,
		final String contentType,
		final String contentId,
		final Integer pageIndex,
		final Integer pageSize);

	/**
	 * Delete a single bookmark given it's primary key.
	 *
	 * @param id primary key of entity
	 */
	void deleteOne(final Long id);

	/**
	 * Delete multiple bookmarks given a list of bookmark ids.
	 *
	 * @param memberId
	 * @param contentSource
	 * @param contentType
	 * @param ids
	 */
	void deleteAll(String memberId, String contentSource, String contentType, List<String> ids);

	/**
	 * Returns the quantity of bookmarks saved for a specific member, content source and type.
	 *
	 * @param memberId
	 * @param contentSource
	 * @param contentType
	 * @return
	 */
	long countByMemberIdAndContentSourceAndContentType(String memberId, String contentSource, String contentType);

}
