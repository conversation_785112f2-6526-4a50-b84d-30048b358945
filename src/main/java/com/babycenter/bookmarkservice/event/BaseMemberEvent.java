package com.babycenter.bookmarkservice.event;

public abstract class BaseMemberEvent
{

	private String email;
	private Long siteMemberId;
	private String globalMemberId;
	private String hub;
	private String countryCode;
	private String eventType;
	private String eventVersion;
	private Long timestamp;

	public String getEmail()
	{
		return email;
	}

	public void setEmail(String email)
	{
		this.email = email;
	}

	public Long getSiteMemberId()
	{
		return siteMemberId;
	}

	public void setSiteMemberId(Long siteMemberId)
	{
		this.siteMemberId = siteMemberId;
	}

	public String getGlobalMemberId()
	{
		return globalMemberId;
	}

	public void setGlobalMemberId(String globalMemberId)
	{
		this.globalMemberId = globalMemberId;
	}

	public String getHub()
	{
		return hub;
	}

	public void setHub(String hub)
	{
		this.hub = hub;
	}

	public String getCountryCode()
	{
		return countryCode;
	}

	public void setCountryCode(String countryCode)
	{
		this.countryCode = countryCode;
	}

	public String getEventType()
	{
		return eventType;
	}

	public void setEventType(String eventType)
	{
		this.eventType = eventType;
	}

	public String getEventVersion()
	{
		return eventVersion;
	}

	public void setEventVersion(String eventVersion)
	{
		this.eventVersion = eventVersion;
	}

	public Long getTimestamp()
	{
		return timestamp;
	}

	public void setTimestamp(Long timestamp)
	{
		this.timestamp = timestamp;
	}

}
