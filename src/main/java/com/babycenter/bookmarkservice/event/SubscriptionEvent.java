package com.babycenter.bookmarkservice.event;

import com.babycenter.bookmarkservice.dto.MemberInfoDto;

public class SubscriptionEvent extends BaseMemberEvent
{
	private String subscriptionType;
	private Boolean isSubscribed;
	private String subscriptionId;
	private String subscriptionMethod;

	public SubscriptionEvent() {

	}

	public SubscriptionEvent(MemberInfoDto memberInfo, String contentSource) {
		if (memberInfo != null) {
			setEmail(memberInfo.email);
			setSiteMemberId(memberInfo.siteId);
			setGlobalMemberId(memberInfo.globalId);
		}
		setEventVersion("0.1");
		setTimestamp(System.currentTimeMillis());
		// contentSource is a string like "community:{country_code}"
		if (contentSource.startsWith("community")) {
			String[] parts = contentSource.split(":");
			String countryCode = parts[1];
			setCountryCode(countryCode);
			switch (countryCode) {
				case "en_GB":
				case "en_AU":
				case "en_IN":
				case "en_CA":
					setHub("gb");
					break;

				case "de_DE":
					setHub("de");
					break;

				case "ar_SA":
					setHub("ae");
					break;

				case "pt_BR":
					setHub("br");
					break;

				case "es_US":
					setHub("es");
					break;

				case "en_US":
				default:
					setHub("us");
					break;
			}
		} else {
			setHub("us");
			setCountryCode("en_US");
		}
	}

	public String getSubscriptionType()
	{
		return subscriptionType;
	}

	public void setSubscriptionType(String subscriptionType)
	{
		this.subscriptionType = subscriptionType;
	}

	public Boolean getSubscribed()
	{
		return isSubscribed;
	}

	public void setSubscribed(Boolean subscribed)
	{
		isSubscribed = subscribed;
	}

	public String getSubscriptionId()
	{
		return subscriptionId;
	}

	public void setSubscriptionId(String subscriptionId)
	{
		this.subscriptionId = subscriptionId;
	}

	public String getSubscriptionMethod()
	{
		return subscriptionMethod;
	}

	public void setSubscriptionMethod(String subscriptionMethod)
	{
		this.subscriptionMethod = subscriptionMethod;
	}

	public static SubscriptionEvent createSubscriptionEvent(MemberInfoDto memberInfo, String globalMemberId, String contentSource, String eventType, String subscriptionType, String subscriptionMethod, String subscriptionId, boolean isSubscribed) {
		SubscriptionEvent event = new SubscriptionEvent(memberInfo, contentSource);
		event.setGlobalMemberId(globalMemberId);
		event.setEventType(eventType);
		event.setSubscriptionType(subscriptionType);
		event.setSubscriptionMethod(subscriptionMethod);
		event.setSubscriptionId(subscriptionId);
		event.setSubscribed(isSubscribed);
		return event;
	}

}
