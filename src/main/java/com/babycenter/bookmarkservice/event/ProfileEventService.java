package com.babycenter.bookmarkservice.event;

import com.babycenter.bookmarkservice.auth.client.MemberInfoClient;
import com.babycenter.bookmarkservice.dto.MemberInfoDto;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.MessageAttributeValue;
import software.amazon.awssdk.services.sns.model.PublishRequest;
import software.amazon.awssdk.services.sns.model.PublishResponse;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Service
public class ProfileEventService
{

	private static final Logger log = LoggerFactory.getLogger(ProfileEventService.class);

	private MemberInfoClient memberInfoClient;
	private SnsClient snsClientUsMemberEvents;
	private String usMemberEventsTopicArn;
	private SnsClient snsClientSubscriptionEvents;
	private String subscriptionEventsTopicArn;

	@Autowired
	public void setSnsClientUsMemberEvents(SnsClient snsClientUsMemberEvents)
	{
		this.snsClientUsMemberEvents = snsClientUsMemberEvents;
	}

	@Autowired
	public void setSnsClientSubscriptionEvents(SnsClient snsClientSubscriptionEvents)
	{
		this.snsClientSubscriptionEvents = snsClientSubscriptionEvents;
	}

	@Autowired
	public void setMemberInfoClient(MemberInfoClient memberInfoClient)
	{
		this.memberInfoClient = memberInfoClient;
	}

	@Value("${profileEventService.usMemberEvents.topicArn}")
	public void setUsMemberEventsTopicArn(String usMemberEventsTopicArn)
	{
		this.usMemberEventsTopicArn = usMemberEventsTopicArn;
	}

	@Value("${profileEventService.subscriptionEvents.topicArn}")
	public void setSubscriptionEventsTopicArn(String subscriptionEventsTopicArn)
	{
		this.subscriptionEventsTopicArn = subscriptionEventsTopicArn;
	}

	@PostConstruct
	public void postConstruct()
	{
		if (usMemberEventsTopicArn == null || usMemberEventsTopicArn.isEmpty())
		{
			logMissingTopicArn("usMemberEventsTopicArn");
		}
		if (subscriptionEventsTopicArn == null || subscriptionEventsTopicArn.isEmpty())
		{
			logMissingTopicArn("subscriptionEventsTopicArn");
		}
	}

	public void sendMomAnswersSubscriptionEvent(String authorizationHeader, Bookmark bookmark, boolean isSubscribed)
	{
		sendEvent("usMemberEventsTopicArn", snsClientUsMemberEvents, authorizationHeader, ProfileEventType.MOM_ANSWERS_SUBSCRIPTION, usMemberEventsTopicArn, bookmark, isSubscribed);
	}

	public void sendSubscriptionEvent(String authorizationHeader, Bookmark bookmark, boolean isSubscribed)
	{
		sendEvent("subscriptionEventsTopicArn", snsClientSubscriptionEvents, authorizationHeader, ProfileEventType.SUBSCRIPTION, subscriptionEventsTopicArn, bookmark, isSubscribed);
	}

	private void sendEvent(String topicName, SnsClient snsClient, String authorizationHeader, ProfileEventType eventType, String topicArn, Bookmark bookmark, boolean isSubscribed)
	{
		if (topicArn == null || topicArn.isEmpty())
		{
			logMissingTopicArn(topicName);
			return;
		}


		MemberInfoDto memberInfo = authorizationHeader != null ? memberInfoClient.getMemberInfo(authorizationHeader, bookmark.getMemberId()) : null;

		try
		{
			SubscriptionEvent event = SubscriptionEvent.createSubscriptionEvent(memberInfo, bookmark.getMemberId(), bookmark.getContentSource(), eventType.getName(), eventType.getName(), bookmark.getContentType(), bookmark.getContentId(), isSubscribed);
			sendEvent(snsClient, topicArn, event);
		}
		catch (Throwable t)
		{
			log.error("Could not send event for member " + bookmark.getMemberId(), t);
		}
	}

	private void logMissingTopicArn(String name)
	{
		// There is an error in configuration.
		log.error("ProfileEventService." + name + " is not configured. Won't send SNS message.");
	}

	private static void sendEvent(SnsClient snsClient, String topicArn, BaseMemberEvent event)
	{
		if(snsClient == null) {
			log.warn("Sns client is null - skipping sending event");
		}

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);

		String message;
		try
		{
			message = objectMapper.writeValueAsString(event);
		}
		catch (JsonProcessingException e)
		{
			// Shouldn't happen, if it fails to serialize as JSON it's something in the code that is incorrect, nothing
			// the caller could fix.
			throw new RuntimeException(e);
		}

		MessageAttributeValue eventTypeAttribute = MessageAttributeValue.builder()
			.dataType("String")
			.stringValue(event.getEventType())
			.build();

		MessageAttributeValue eventVersionAttribute = MessageAttributeValue.builder()
			.dataType("Number")
			.stringValue(event.getEventVersion())
			.build();

		MessageAttributeValue localeAttribute = MessageAttributeValue.builder()
			.dataType("String")
			.stringValue(event.getCountryCode())
			.build();

		Map<String, MessageAttributeValue> messageAttributes = new HashMap<>();
		messageAttributes.put("EventType", eventTypeAttribute);
		messageAttributes.put("EventVersion", eventVersionAttribute);
		messageAttributes.put("Locale", localeAttribute);

		PublishRequest request = PublishRequest.builder()
			.topicArn(topicArn)
			.messageAttributes(messageAttributes)
			.message(message)
			.build();

		PublishResponse response = snsClient.publish(request);

		log.debug("Sent " + event.getEventType() + " to SNS. Response.messageId: " + response.messageId() + " ... Message: " + message);
	}

}
