package com.babycenter.bookmarkservice.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.DataSourceHealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * Created by emurphy on 1/17/17.
 */
@Configuration
public class HealthIndicatorsConfiguration
{
	private DataSource dataSource;
	
	@Bean
	@Primary
	public DataSourceHealthIndicator dataSourceHealthIndicator() {
		//
		// make sure we can read from the secure profile schema
		//
		// NOTE: this requires at least ONE row in the the bookmarks.bookmark table in order to succeed!!!
		//
		return new DataSourceHealthIndicator(dataSource, "SELECT id FROM bookmark LIMIT 1");
	}
	
	@Autowired
	public void setDataSource(final DataSource dataSource)
	{
		this.dataSource = dataSource;
	}
}
