package com.babycenter.bookmarkservice.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Created by emurphy on 5/18/16.
 */
@Configuration
@EntityScan(basePackages = {"com.babycenter.bookmarkservice.model"})
@EnableJpaRepositories(basePackages = {"com.babycenter.bookmarkservice.persistence.jpa"})
@EnableTransactionManagement
@EnableCaching
@EnableAutoConfiguration
public class JpaConfiguration
{
    // set command line -Dspring.profiles.active=local for local development environment
    @Value("${spring.profiles.active}")
    private String activeProfile;
}
