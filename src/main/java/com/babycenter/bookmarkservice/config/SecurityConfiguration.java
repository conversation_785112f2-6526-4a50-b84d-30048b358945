package com.babycenter.bookmarkservice.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * @EnableWebSecurity turns on spring security, which sets some headers on
 * each request.  This quiets some of the Zap warnings.
 *
 * Created by emurphy on 5/20/16.
 */
@Configuration
public class SecurityConfiguration extends WebSecurityConfigurerAdapter
{
    private static final String REST_V1 = "/v1/**";

    protected void configure(final HttpSecurity httpSecurity) throws Exception
    {
        // NOTE: application.properties for other security settings

        //
        // require no authorization for REST requests (we will use security token)
        // require basic auth for everything else
        //
        httpSecurity.authorizeRequests()
                    .antMatchers(REST_V1).permitAll();  // no authentication of endpoints (we use security token)
        httpSecurity.authorizeRequests()
                    .anyRequest().authenticated()       // everything else uses basic authentication
                        .and().formLogin()
                        .and().httpBasic();

        //
        // require https for all REST requests
        //
        // httpSecurity.requiresChannel().antMatchers(REST_V1).requiresSecure();

        //
        // configure REST endpoints
        //
        final HttpSecurity restSecurity = httpSecurity.antMatcher(REST_V1);
        restSecurity.csrf().disable();                          // no csrf token
        restSecurity.headers().cacheControl();                  // no caching
        restSecurity.headers().frameOptions();                  // X-Frame-Options: DENY
        restSecurity.headers().xssProtection();                 // X-XSS-Protection: 1; mode=block
        restSecurity.headers().contentTypeOptions();            // X-Content-Type-Options: nosniff
        restSecurity.headers().httpStrictTransportSecurity();   // Strict-Transport-Security: max-age=31536000 ; includeSubDomains
    }
}
