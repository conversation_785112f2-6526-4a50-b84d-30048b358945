package com.babycenter.bookmarkservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * Cors configuration to allow any domain to
 * call non-production bookmark service from a browser.
 * We do this to make development easy.
 * <p>
 * Created by emurphy on 5/15/17.
 */
@Configuration
@Profile("!prod")
public class WebConfigurationQa
{
	@Bean
	public CorsFilter corsFilter() {
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

		CorsConfiguration config = new CorsConfiguration();
		config.addAllowedOrigin("*");

		config.addAllowedMethod("OPTIONS");
		config.addAllowedMethod("HEAD");
		config.addAllowedMethod("GET");
		config.addAllowedMethod("POST");
		config.addAllowedMethod("DELETE");
		config.addAllowedHeader("Authorization");
		config.addAllowedHeader("Content-Type");
		config.setMaxAge(3600L);

		// we only need cors for methods made on behalf of a member; those send from browser
		source.registerCorsConfiguration("/bookmark/v1/member/**", config);
		return new CorsFilter(source);
	}
}
