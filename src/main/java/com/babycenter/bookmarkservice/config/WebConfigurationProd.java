package com.babycenter.bookmarkservice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * Cors configuration to allow bcsite and social to
 * call the production bookmark service from a browser.
 * <p>
 * Created by emurphy on 5/15/17.
 */
@Configuration
@Profile("prod")
public class WebConfigurationProd
{
	@Bean
	public CorsFilter corsFilter() {
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

		CorsConfiguration config = new CorsConfiguration();

		// main sites
		config.addAllowedOrigin("https://www.babycenter.com");
		config.addAllowedOrigin("https://www.babycentre.co.uk");
		config.addAllowedOrigin("https://www.babycenter.com.au");
		config.addAllowedOrigin("https://www.babycenter.in");
		config.addAllowedOrigin("https://www.babycenter.ca");
		config.addAllowedOrigin("https://www.babycenter.de");
		config.addAllowedOrigin("https://brasil.babycenter.com");
		config.addAllowedOrigin("https://espanol.babycenter.com");

		// community sub-domains
		config.addAllowedOrigin("https://community.babycenter.com");
		config.addAllowedOrigin("https://community.babycentre.co.uk");

		// main shrub sites
		config.addAllowedOrigin("https://shrub.babycenter.com");
		config.addAllowedOrigin("https://shrub.babycentre.co.uk");
		config.addAllowedOrigin("https://shrub.babycenter.com.au");
		config.addAllowedOrigin("https://shrub.babycenter.in");
		config.addAllowedOrigin("https://shrub.babycenter.ca");
		config.addAllowedOrigin("https://shrub.babycenter.de");
		config.addAllowedOrigin("https://shrub-brasil.babycenter.com");
		config.addAllowedOrigin("https://shrub-espanol.babycenter.com");

		// shrub sub-domains
		config.addAllowedOrigin("https://shrub-react.babycenter.com");
		config.addAllowedOrigin("https://shrub-community.babycenter.com");
		config.addAllowedOrigin("https://shrub-react-brasil.babycenter.com");
		config.addAllowedOrigin("https://shrub-react-espanol.babycenter.com");
		config.addAllowedOrigin("https://shrub-react.babycenter.ca");
		config.addAllowedOrigin("https://shrub-react.babycenter.com.au");
		config.addAllowedOrigin("https://shrub-react.babycentre.co.uk");
		config.addAllowedOrigin("https://shrub-community.babycentre.co.uk");
		config.addAllowedOrigin("https://shrub-react.babycenter.in");
		config.addAllowedOrigin("https://shrub-react.babycenter.de");

		config.addAllowedMethod("OPTIONS");
		config.addAllowedMethod("HEAD");
		config.addAllowedMethod("GET");
		config.addAllowedMethod("POST");
		config.addAllowedMethod("DELETE");
		config.addAllowedHeader("Authorization");
		config.addAllowedHeader("Content-Type");
		config.setMaxAge(3600L);

		// we only need cors for methods made on behalf of a member; those send from browser
		source.registerCorsConfiguration("/bookmark/v1/member/**", config);
		return new CorsFilter(source);
	}
}
