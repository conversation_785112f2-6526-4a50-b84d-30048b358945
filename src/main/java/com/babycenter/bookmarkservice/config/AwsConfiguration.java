package com.babycenter.bookmarkservice.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.SnsClientBuilder;

@Configuration
public class AwsConfiguration {
    private static Logger logger = LoggerFactory.getLogger(AwsConfiguration.class);

    @Bean("snsClientUsMemberEvents")
    public SnsClient getSnsClientUsMemberEvents(@Value("${profileEventService.accessKeyId}") String accessKeyId,
                                                @Value("${profileEventService.secretAccessKey}") String secretAccessKey,
                                                @Value("${profileEventService.sessionToken:}") String sessionToken,
                                                @Value("${profileEventService.usMemberEvents.awsRegion}") String awsRegion) {
        return buildSnsClient("usMemberEvents", accessKeyId, secretAccessKey, sessionToken, awsRegion);
    }
    @Bean("snsClientSubscriptionEvents")
    public SnsClient getSnsClientSubscriptionEvents(@Value("${profileEventService.accessKeyId}") String accessKeyId,
                                                    @Value("${profileEventService.secretAccessKey}") String secretAccessKey,
                                                    @Value("${profileEventService.sessionToken:}") String sessionToken,
                                                    @Value("${profileEventService.subscriptionEvents.awsRegion}") String awsRegion) {
        return buildSnsClient("subscriptionEvents", accessKeyId, secretAccessKey, sessionToken, awsRegion);
    }

    private static SnsClient buildSnsClient(String name, String accessKeyId, String secretAccessKey, String sessionToken, String awsRegion) {
        SnsClientBuilder builder = SnsClient.builder();

        if (accessKeyId != null && !accessKeyId.isEmpty() && secretAccessKey != null && !secretAccessKey.isEmpty()) {
            if (sessionToken != null && !sessionToken.isEmpty())
            {
                builder = builder.credentialsProvider(() -> AwsSessionCredentials.create(accessKeyId, secretAccessKey, sessionToken));
            }
            else {
                builder = builder.credentialsProvider(() -> AwsBasicCredentials.create(accessKeyId, secretAccessKey));
            }

            if (awsRegion != null && !awsRegion.isEmpty()) {
                builder = builder.region(Region.of(awsRegion));
            }
            logger.debug("sns client created - " + name);
            return builder.build();
        }

        logger.warn("sns client is null - access key and/or secret is empty - " + name);
        return null;
    }


}
