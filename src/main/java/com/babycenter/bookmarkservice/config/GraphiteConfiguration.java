package com.babycenter.bookmarkservice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.ExportMetricWriter;
import org.springframework.boot.actuate.metrics.statsd.StatsdMetricWriter;
import org.springframework.boot.actuate.metrics.writer.MetricWriter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * Created by emurphy on 4/13/17.
 */
@Configuration
@Profile({"prod", "stag", "qa"})
public class GraphiteConfiguration
{
	@Bean
	@ExportMetricWriter
	@ConditionalOnExpression("${babycenter.statsd.enabled:false}")
	MetricWriter metricWriter(@Value("${babycenter.statsd.stat.prefix}") String prefix,
	                          @Value("${babycenter.statsd.host}") String statsdHost,
	                          @Value("${babycenter.statsd.port}") int statsdPort) {
		return new StatsdMetricWriter(prefix, statsdHost, statsdPort);
	}
}
