package com.babycenter.bookmarkservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;


@ComponentScan({"com.babycenter.bookmarkservice", "com.babycenter.bookmarkservice.config", "com.babycenter.bookmarkservice.health", "com.babycenter.bookmarkservice.rest", "com.babycenter.bookmark.persistence"})
@SpringBootApplication
@EnableCaching
public class Application
{
	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}
}
