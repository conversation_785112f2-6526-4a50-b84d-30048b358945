package com.babycenter.bookmarkservice.health;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;

/**
 * Created by ksharma on 3/27/18.
 * Configured to run at the service startup, This will notify the load balancer that the service is up and running and
 * ready to take the traffic. We will use this for the Orange/Black deployment strategy to make Orange/Black pool available
 * depending on if the file exists or not. It is setup so that if the flag is enabled, then only execute the feature
 */
@Component
@ConditionalOnProperty(prefix = "management.health.serving", value = "isEnabled")
public class ReadyToServeTrafficHealthIndicator extends AbstractHealthIndicator
	implements InitializingBean
{
	
	private static final Logger log = LoggerFactory.getLogger(ReadyToServeTrafficHealthIndicator.class);
	
	private static final String DEFAULT_SERVING_FILE_LOCATION = "serving.txt";
	
	private static final SimpleDateFormat FILE_DATE_FORMAT = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
	
	/**
	 * Location where we'll place the file to indicate whether the service is accepting any traffic at the moment of not
	 */
	@Value("${management.health.serving.fileLocation}")
	private String servingFileLocation;
	
	
	public ReadyToServeTrafficHealthIndicator()
	{
		log.warn("Initializing the ReadyToServeTrafficHealthIndicator bean for health check");
		
	}
	
	
	/**
	 * Actual health check logic.
	 *
	 * @param builder the {@link Health.Builder} to report health status and details
	 * @throws Exception any {@link Exception} that should create a {@link org.springframework.boot.actuate.health.Status#DOWN}
	 *                   system status.
	 */
	@Override
	protected void doHealthCheck(Health.Builder builder) throws Exception
	{
		File servingFile = null;
		if (StringUtils.isEmpty(servingFileLocation))
		{
			servingFile = new File(DEFAULT_SERVING_FILE_LOCATION);
		}
		else
		{
			servingFile = new File(servingFileLocation);
		}
		try
		{
			builder.up().withDetail("servingFile", servingFile.getAbsolutePath());
			if (!servingFile.exists())
			{
				throw new FileNotFoundException(servingFile.getAbsolutePath() + " doesn't exist, make sure the file exists in order for this service to start serving the requests");
			}
			builder.withDetail("hello", "Serving File exists, Last modified: " + (servingFile.lastModified() == 0 ? servingFile.lastModified() : FILE_DATE_FORMAT.format(servingFile.lastModified())));
		}
		catch (Exception ex)
		{
			log.error("Error while checking the system health, Serving file doesn't exist", ex);
			builder.down(ex);
		}
		
	}
	
	
	/**
	 * Invoked by a BeanFactory after it has set all bean properties supplied
	 * (and satisfied BeanFactoryAware and ApplicationContextAware).
	 * <p>This method allows the bean instance to perform initialization only
	 * possible when all bean properties have been set and to throw an
	 * exception in the event of misconfiguration.
	 *
	 * @throws Exception in the event of misconfiguration (such
	 *                   as failure to set an essential property) or if initialization fails.
	 */
	@Override
	public void afterPropertiesSet() throws Exception
	{
		Assert.state(this.servingFileLocation != null,
			"servingFileLocation for ReadyToServeTrafficHealthIndicator must be specified");
	}
	
	public void setServingFileLocation(String servingFileLocation)
	{
		this.servingFileLocation = servingFileLocation;
	}
	
}
