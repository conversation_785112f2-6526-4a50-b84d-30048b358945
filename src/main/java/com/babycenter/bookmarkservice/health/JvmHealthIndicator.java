package com.babycenter.bookmarkservice.health;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.endpoint.SystemPublicMetrics;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.metrics.Metric;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;


/**
 * Created by ksharma on 3/30/18.
 * JvmHealthCheck only gets initialized if management.health.jvm.isEnabled is true in the properties file. It checks if the service has exceeded the set Memory Threshold
 */
@Component
@ConditionalOnProperty(prefix = "management.health.jvm", value = "isEnabled")
public class JvmHealthIndicator extends AbstractHealthIndicator {

    private static final Logger log = LoggerFactory.getLogger(JvmHealthIndicator.class);
    // default threshold ratio for used memory vs total allocated memory in jvm
    private static final int DEFAULT_JVM_MEMORY_THRESHOLD = 95;
    // the default threshold ratio for number of threads active threads vs peak number of threads
    private static final int DEFAULT_NUMBER_OF_THREADS_THRESHOLD = 95;
    /**
     * Refer to the {@link org.springframework.boot.actuate.endpoint.SystemPublicMetrics} for these keys
     */
    private static final String JVM_TOTAL_MEMORY_KEY = "mem";
    private static final String JVM_FREE_MEMORY_KEY = "mem.free";
    private static final String JVM_USED_MEMORY_KEY = "mem.used";

    @Value("${management.health.jvm.memory.threshold}")
    private int jvmMemoryThreshold;

    @Autowired
    private SystemPublicMetrics systemMetric;

    /**
     * Actual health check logic.
     *
     * @param builder the {@link org.springframework.boot.actuate.health.Health.Builder} to report health status and details
     * @throws Exception any {@link Exception} that should create a {@link org.springframework.boot.actuate.health.Status#DOWN}
     *                   system status.
     */
    @Override
    protected void doHealthCheck(Health.Builder builder) throws Exception {
        try {
            {
                /** Spring uses {@link SystemPublicMetrics} class to store the various metrics, we want to get Total memory and free memory in this case
                 */
                Metric totalMemoryMetric = systemMetric.metrics().stream().filter(data -> data.getName().equals(JVM_TOTAL_MEMORY_KEY)).findFirst().get();
                Metric freeMemoryMetric = systemMetric.metrics().stream().filter(data -> data.getName().equals(JVM_FREE_MEMORY_KEY)).findFirst().get();
                long totalMemory = totalMemoryMetric.getValue().longValue();
                long freeMemory = freeMemoryMetric.getValue().longValue();
                long usedMemory = totalMemory - freeMemory;
                double usedMemoryPercentage = ((double) usedMemory / totalMemory) * 100;

                // if the usedMemory percentage is less than the set threshold (default or from the property file), means system is up
                int threshold = jvmMemoryThreshold <= 0 ? DEFAULT_JVM_MEMORY_THRESHOLD : jvmMemoryThreshold;
                builder.withDetail(JVM_TOTAL_MEMORY_KEY, totalMemory)
                        .withDetail(JVM_USED_MEMORY_KEY, usedMemory)
                        .withDetail("threshold", threshold)
                        .withDetail("current_memory_threshold", usedMemoryPercentage);
                if (usedMemoryPercentage < threshold) {
                    builder.up();
                } else {
                    log.warn(
                            "Used memory above threshold. "
                                    + "Available: {}% threshold: {}%",
                            usedMemoryPercentage, threshold);
                    builder.down();
                }

            }
        } catch (RuntimeException ex) {
            log.error("System maybe unstable, not able to get the jvm metrics", ex);
            builder.down(ex);
        }

    }

    public void setJvmMemoryThreshold(int jvmMemoryThreshold) {
        this.jvmMemoryThreshold = jvmMemoryThreshold;
    }

    public void setSystemMetric(SystemPublicMetrics systemMetric) {
        this.systemMetric = systemMetric;
    }

}
