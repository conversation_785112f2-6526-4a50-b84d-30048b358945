package notification.jsonrestclient

import groovy.transform.PackageScope

import java.util.concurrent.TimeUnit

import org.apache.commons.logging.Log
import org.apache.commons.logging.LogFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Scope
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.HttpStatusCodeException
import org.springframework.web.client.RestClientException
import org.springframework.web.client.RestTemplate

/**
 * Base class for json rest client.
 * To use this, subclass then write your own public method(s) that
 * call get(), put(), or post() methods.
 * Using "prototype" scope here to provide separate copy for each calling component.
 * This is to ensure that custom configuration of the rest template properties are set properly for each use case.
 */
@Component
@Scope("prototype")
class JsonRestClient {
	private static final Log log = LogFactory.getLog(JsonRestClient.class)
	private static final int MAX_RETRY = 3
	private static final long WAIT_MILLISECONDS_RETRY = 20L
	
	RestTemplate restTemplate
	RestTemplate postRestTemplate
	
	/**
	 * HTTP POST request to given urls with given payload and Headers if expected
	 * Throws a RuntimeException if call can't be completed
	 *
	 * @param <T>                      type of payload
	 * @param <U>                      type of response
	 * @param url url of the endpoint to call
	 * @param thePayload the payload of type T to convert to json and send
	 * @param responseType the class of type U that represents the response
	 * @param httpHeaders custom headers to add (may be empty or null for no headers)
	 * @return Response entity or null if a 404 response was received.
	 * @throws RetryableRestException    if call does not succeed, but can be retried.
	 * @throws NonretryableRestException is call does not succeed and should NOT be retried.
	 */
	@PackageScope
	<T, U> ResponseEntity<U> postRetry(final String url, T thePayload, Class<U> responseType, HttpHeaders httpHeaders) {
		int retryCount = 0
		while (retryCount < MAX_RETRY) {
			try {
				log.debug("post: " + url)   // url may include a member_id
				return post(url, thePayload, responseType, httpHeaders)
			} catch (RetryableRestException ex) {
				retryCount = incrementRetryCount(retryCount, ex)
			}
		}
		throw nonretryable(url, retryCount)
	}
	
	@PackageScope
	<T, U> ResponseEntity<U> post(String url, T thePayload, Class<U> responseType, HttpHeaders httpHeaders) {
		try {
			HttpEntity<T> theRequestEntity = buildHttpHeaders(thePayload, httpHeaders)
			
			ResponseEntity<U> response = postRestTemplate.exchange(
				url,
				HttpMethod.POST,
				theRequestEntity,
				responseType)
			
			if (response.getStatusCode().is2xxSuccessful()) {
				return response
			}
			throw new HttpClientErrorException(response.getStatusCode())
		} catch (HttpStatusCodeException e) {
			throw toRetryException(url, e)
		} catch (RestClientException e) {
			//
			// presume this is a connection error, treat it as a request timeout
			//
			log.warn(e.getMessage(), e)
			throw new RetryableRestException(url, new HttpClientErrorException(HttpStatus.REQUEST_TIMEOUT))
		}
	}
	
	/**
	 * Checks how many times a request is made, if MAX_RETRY is reached it throws the original exception
	 *
	 * @param retryCount
	 * @param ex
	 */
	private int incrementRetryCount(int retryCount, RetryableRestException ex) {
		if (retryCount < MAX_RETRY) {
			retryCount += 1
			log.warn("Service unavailable, retry attempt " + retryCount + " :" + ex.getMessage())
			try {
				TimeUnit.MILLISECONDS.sleep(WAIT_MILLISECONDS_RETRY * retryCount)
				// wait, with backoff, before trying again
				return retryCount
			} catch (InterruptedException e) {
				log.error("GET retry interrupted :" + e.getMessage())
				Thread.currentThread().interrupt()
				throw new NonretryableRestException(ex.url, HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage())
			}
		}
		
		log.warn("GET giving after " + retryCount + " attempts: " + ex.getMessage())
		throw ex // throw the underlying HttpStatusCodeException
	}
	
	/**
	 * Create a Put or Post request entity with the given payload and headers.
	 *
	 * @param thePayload
	 * @param httpHeaders
	 * @param <T>
	 * @return
	 */
	private final <T> HttpEntity<T> buildHttpHeaders(T thePayload, HttpHeaders httpHeaders) {
		if (null == httpHeaders) {
			httpHeaders = new HttpHeaders()
		}
		
		//
		// json content type headers
		//
		final List<MediaType> mediaTypeList = new ArrayList<MediaType>()
		mediaTypeList.add(MediaType.APPLICATION_JSON)
		httpHeaders.setAccept(mediaTypeList)
		httpHeaders.setContentType(MediaType.APPLICATION_JSON)
		
		//
		// create request entity with custom headers
		//
		HttpEntity theHttpEntity = new HttpEntity(thePayload, httpHeaders)
		return theHttpEntity
	}
	
	/**
	 * An HttpStatusCodeException that is retryable.
	 */
	static class RetryableRestException extends HttpStatusCodeException {
		final String url
		
		RetryableRestException(final String url, final HttpStatusCodeException exception) {
			super(exception.getStatusCode(), exception.getStatusText(), exception.getResponseHeaders(), exception.getResponseBodyAsByteArray(), null)
			this.url = url
		}
		
		RetryableRestException(final String url, final HttpStatus status, final String message) {
			super(status, message)
			this.url = url
		}
	}
	
	/**
	 * An HttpStatusCodeException that is not retryable
	 */
	static class NonretryableRestException extends HttpStatusCodeException {
		public final String url
		
		NonretryableRestException(final String url, final HttpStatusCodeException exception) {
			super(exception.getStatusCode(), exception.getStatusText(), exception.getResponseHeaders(), exception.getResponseBodyAsByteArray(), null)
			this.url = url
		}
		
		NonretryableRestException(final String url, final HttpStatus status, final String message) {
			super(status, message)
			this.url = url
		}
	}
	
	/**
	 * convert an HttpStatusCodeException to RetryableException or NoneretryableException
	 *
	 * @param e
	 * @throws RetryableRestException for retryable status codes,
	 *                                NonretryableRestException for non-retryable status codes.
	 */
	private HttpStatusCodeException toRetryException(final String url, final HttpStatusCodeException e) {
		if (e instanceof RetryableRestException) return e       // no transform necessary
		if (e instanceof NonretryableRestException) return e    // no transform necessary
		switch (e.getStatusCode()) {
			case HttpStatus.REQUEST_TIMEOUT:
			case HttpStatus.GATEWAY_TIMEOUT:
				log.warn(e.getMessage())
				return new RetryableRestException(url, e)
			default:
				log.warn(e.getMessage())
				return new NonretryableRestException(url, e)
		}
	}
	
	
	/**
	 * construct a NonretryableRestException with a decent message.
	 *
	 * @param url
	 * @param retryCount
	 * @return NonretryableRestException
	 */
	private NonretryableRestException nonretryable(final String url, final int retryCount) {
		return new NonretryableRestException(
			url,
			HttpStatus.SERVICE_UNAVAILABLE,
			"Could not connect with service after {retryCount} attempts: {url}"
				.replace("{retryCount}", String.valueOf(retryCount))
				.replace("{url}", url))
	}
	
	
	@Autowired
	@Qualifier("strictPooledRestTemplate")
	void setRestTemplate(RestTemplate restTemplate) {
		this.restTemplate = restTemplate
	}
	
	//
	// rest template for post has a longer timeout, since the server may be writing to the db
	//
	@Autowired
	@Qualifier("mediumPooledRestTemplate")
	void setPostRestTemplate(RestTemplate lcRestTemplate) {
		this.postRestTemplate = lcRestTemplate
	}
	
}

