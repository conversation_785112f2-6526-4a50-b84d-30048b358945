package notification;

import org.springframework.web.cors.CorsConfiguration;

public class CustomCorsConfiguration extends CorsConfiguration {

    public static String URL_REGEX = "^((.*)\\.babycent(er|re)\\.(com.au|com.my|com|co.uk|de|ca|fr|in)(?:\\.local)?(:[0-9]*)?)";

    /**
     * Check the origin of the request against the URL_REGEX allowed origins.
     * @param requestOrigin the origin to check
     * @return the origin to use for the response, possibly {@code null} which
     * means the request origin is not allowed
     */
    @Override
    public String checkOrigin(String requestOrigin) {
        if (null == requestOrigin)
        {
            return null;
        }
        if (requestOrigin.matches(URL_REGEX)) {
            return requestOrigin;
        }
        return null;
    }
}
