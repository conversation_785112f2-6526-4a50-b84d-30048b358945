package notification.dtos;

import notification.pojos.Notification;

import java.util.List;

public class ListNotificationsResult {

    private Long unreadCount;

    private List<Notification> notifications;

    public Long getUnreadCount() {
        return unreadCount;
    }

    public void setUnreadCount(Long unreadCount) {
        this.unreadCount = unreadCount;
    }

    public List<Notification> getNotifications() {
        return notifications;
    }

    public void setNotifications(List<Notification> notifications) {
        this.notifications = notifications;
    }

}
