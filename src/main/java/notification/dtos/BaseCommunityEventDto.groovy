package notification.dtos

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import groovy.transform.ToString
import notification.data.NotificationGroup
import notification.data.ReplyNotificationGroup
import notification.pojos.GsonUTCDateAdapter
import notification.pojos.Notification
import org.hibernate.validator.constraints.NotBlank

import javax.validation.constraints.Min
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

@ToString(includeFields=true, includeNames=true, ignoreNulls=true)
class BaseCommunityEventDto<T> {

	@NotBlank
	String globalMemberId

	@NotNull
	@Min(1L)
	Long siteMemberId

	@NotNull
	@NotBlank
	String countryCode

	@NotNull
	@Min(value=1L, groups=ReplyNotificationGroup.class)
	Long contentId

	@NotNull
	@NotBlank(groups=NotificationGroup.class)
	String eventType

	@NotNull
	String eventVersion

	@NotNull
	Long timestamp
	
	@Size(max=200)
	String snippet

	@NotNull
	Object genericContent

	Notification createNotification(int notificationLifetime) {
		if (!eventType) return null
		def notification = new Notification(countryCode, siteMemberId, contentId, timestamp, notificationLifetime)
		notification.snippet = snippet
		return notification
	}

	static BaseCommunityEventDto toDto(message) {
		Gson gson =	new GsonBuilder()
				.registerTypeAdapter(Date.class, new GsonUTCDateAdapter())
				.create()

		String messageJson = new JsonParser().parse(message).getAsJsonObject().get("Message").getAsString();
		return gson.fromJson(messageJson, BaseCommunityEventDto.class)
	}
}
