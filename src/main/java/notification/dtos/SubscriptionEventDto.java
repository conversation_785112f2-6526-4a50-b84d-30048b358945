package notification.dtos;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParser;

public class SubscriptionEventDto {

    public String email;
    public Long siteMemberId;
    public String globalMemberId;
    public String hub;
    public String countryCode;
    public String eventType;
    public String eventVersion;
    public Long timestamp;
    public String subscriptionType;
    public String subscriptionId;
    public String subscriptionMethod;
    public Boolean subscribed;

    public static SubscriptionEventDto toDto(String message) {
        Gson gson = new GsonBuilder().create();
        String messageJson = new JsonParser().parse(message).getAsJsonObject().get("Message").getAsString();
        return gson.fromJson(messageJson, SubscriptionEventDto.class);
    }

    @Override
    public String toString() {
        return "SubscriptionEventDto{" +
                "email='" + email + '\'' +
                ", siteMemberId=" + siteMemberId +
                ", globalMemberId='" + globalMemberId + '\'' +
                ", hub='" + hub + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventVersion='" + eventVersion + '\'' +
                ", timestamp=" + timestamp +
                ", subscriptionType='" + subscriptionType + '\'' +
                ", subscriptionId='" + subscriptionId + '\'' +
                ", subscriptionMethod='" + subscriptionMethod + '\'' +
                ", subscribed=" + subscribed +
                '}';
    }

}
