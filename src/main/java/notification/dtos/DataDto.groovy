package notification.dtos

import groovy.transform.ToString

import javax.validation.constraints.Min
import javax.validation.constraints.NotNull
import javax.validation.constraints.Size

import notification.data.NotificationGroup
import notification.data.ReplyNotificationGroup
import notification.pojos.Notification
import notification.pojos.Subscription

import org.hibernate.validator.constraints.NotBlank

@ToString(includeFields=true, includeNames=true, ignoreNulls=true)
class DataDto<T> {
	@NotBlank
	String countryCode

	@NotNull
	@Min(1L)
	Long userId

	String globalAuthId

	@NotNull
	@Min(1L)
	Long contentId

	@NotBlank(groups=NotificationGroup.class)
	String action

	@NotNull(groups=ReplyNotificationGroup.class)
	@Min(value=1L, groups=ReplyNotificationGroup.class)
	Long originalCommentUserId

	@Min(value=1L, groups=ReplyNotificationGroup.class)
	Long postId

	@NotNull(groups=ReplyNotificationGroup.class)
	@Min(value=1L, groups=ReplyNotificationGroup.class)
	Long replyId

	@Size(max=200)
	String snippet

	Date actionTime

	Subscription createSubscription() {
		new Subscription(countryCode, userId, contentId)
	}

	Notification createNotification(int notificationLifetime) {
		if (!action) return null
		def notification = new Notification(countryCode, userId, contentId, action, notificationLifetime)
		notification.snippet = snippet
		return notification
	}

	Notification createNotification(String globalUserId, int notificationLifetime) {
		if (!action) return null
		def notification = new Notification(countryCode, globalUserId, contentId, action, notificationLifetime)
		notification.snippet = snippet
		return notification
	}

}
