package notification.controller;

import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import notification.data.SQSEventProcessor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

@Component
public class SQSConsumer {

    private final Log logger = LogFactory.getLog(getClass());

    @Autowired
    private Environment env;

    @Autowired
    private SQSEventProcessor sqsEventProcessor;

    @JmsListener(destination = "${amazon.sqs.queues.bc-us-community-events}")
    public void onMessage(SQSTextMessage message)
    {
        try
        {
            logger.debug("Starting process sqs message on " + env.getProperty("amazon.sqs.queues.bc-us-community-events"));
            sqsEventProcessor.addCommunityEvent(message);
        }
        catch (Exception e)
        {
            sqsEventProcessor.requeueMessage(message, e);
        }
    }
}
