package notification.controller

import notification.auth.AccessTokenDecoder
import notification.data.InvalidData
import notification.data.KeyEncrypter
import notification.dtos.ListNotificationsResult
import notification.pojos.Notification
import notification.service.NotificationHelper
import org.apache.commons.logging.Log
import org.apache.commons.logging.LogFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.web.bind.annotation.*
import sun.misc.BASE64Decoder

import java.security.GeneralSecurityException

@RestController
class HomeController {
	private final Log logger = LogFactory.getLog(getClass())

	@Autowired
	private NotificationHelper notificationHelper

	@Autowired
	private AccessTokenDecoder accessTokenDecoder

	@RequestMapping('/robots.txt')
	robots() {
		'''User-agent: *
Disallow: /'''
	}

	@RequestMapping('/')
	home() {
		logger.debug 'hitting root service endpoint'
		return 'Welcome to babycenter notification services'
	}

	@RequestMapping('/v1/user/notifications')
	notifications(
		@RequestParam('userkey') String encodedKey)
	{
		logger.debug 'hitting user notification endpoint'
		try {
			String userKey = KeyEncrypter.decode(encodedKey)
			ListNotificationsResult notificationListMap = notificationHelper.listNotifications(userKey);
			return [unreadCount: notificationListMap.unreadCount, notifications: notificationHelper.convertDirectMessagesOutputs(notificationListMap.notifications)]

		} catch (GeneralSecurityException e) {
			logger.warn('list notifications: problem decoding encoded key param[' + encodedKey + "]. Exception: " + e.getMessage())
			return new InvalidData(message: 'Invalid user key: ' + encodedKey)
		}
	}

	@RequestMapping('/v1/user/acknowledge')
	acknowledge(
		@RequestParam('userkey') String encodedKey)
	{
		logger.debug 'hitting mark notification endpoint'
		try {
			String userKey = KeyEncrypter.decode(encodedKey)
			notificationHelper.acknowledgeNotifications(userKey)
		} catch (GeneralSecurityException e) {
			logger.warn('acknowledgeNotifications: problem decoding encoded key param[' + encodedKey + "]. Exception: " + e.getMessage())
			return new InvalidData(message: 'Invalid user key: ' + encodedKey)
		}
	}

	private base64Decode(final String data) {
		return new String(new BASE64Decoder().decodeBuffer(data));
	}

	@RequestMapping('/v2/user/notifications')
	notificationsWithAccessToken(
		@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
		@RequestParam(name = 'userkey', required = true) String userKey)
	{
		logger.debug 'hitting v2 user notification endpoint'
		try {

			ListNotificationsResult notificationListMap = notificationHelper.listNotifications(base64Decode(userKey));
			return [unreadCount: notificationListMap.unreadCount, notifications: notificationHelper.convertDirectMessagesOutputs(notificationListMap.notifications)]

		} catch (GeneralSecurityException e) {
			logger.warn('/v2/user/notifications: problem decoding encoded key param[' + userKey + "].", e)
			return new InvalidData(message: 'Invalid user key: ' + userKey)
		}
	}

	@RequestMapping('/v2/user/acknowledge')
	acknowledgeWithAccessToken(
		@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
		@RequestParam(name = 'userkey', required = true) String userKey)
	{
		logger.debug 'hitting v2 mark notification endpoint'
		try {
			notificationHelper.acknowledgeNotifications(base64Decode(userKey), true)
		} catch (GeneralSecurityException e) {
			logger.warn('/v2/user/acknowledge: problem decoding encoded key param[' + userKey + "].", e)
			return new InvalidData(message: 'Invalid user key: ' + userKey)
		}
	}

	@RequestMapping(path = '/v2/user/read', method = RequestMethod.POST)
	readWithAccessToken(
		@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
		@RequestParam(name = 'userkey', required = true) String userKey,
		@RequestBody(required = true) String body)
	{
		logger.debug 'hitting v2 mark as read endpoint'
		try {
			notificationHelper.readNotification(base64Decode(userKey), body, null)
		} catch (GeneralSecurityException e) {
			logger.warn('/v2/user/read: problem decoding encoded key param[' + userKey + "]", e)
			return new InvalidData(message: 'Invalid user key: ' + userKey)
		}
	}

	@RequestMapping(path = '/v2/user/readall', method = RequestMethod.POST)
	readAllWithAccessTokenV2(
			@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
			@RequestParam(name = 'userkey', required = true) String userKey)
	{
		logger.debug 'hitting v2 mark all notifications as read endpoint'
		try {
			notificationHelper.readAllNotifications(base64Decode(userKey))
		} catch (GeneralSecurityException e) {
			logger.warn('/v2/user/readall: problem decoding encoded key param[' + userKey + "]", e)
			return new InvalidData(message: 'Invalid user key: ' + userKey)
		}
	}

	@RequestMapping('/v3/user/notifications')
	notificationsWithAccessTokenV3(
			@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
			@RequestParam(name = 'userkey', required = true) String encodedUserKey)
	{
		logger.debug 'hitting v3 user notification endpoint'
		try {
			// Read notifications to user legacy ID
			def userKey = base64Decode(encodedUserKey)
			def oldNotifications = notificationHelper.listNotifications(userKey)
			if (oldNotifications instanceof InvalidData)
			{
				return oldNotifications
			}

			// Read notifications to user global auth ID
			String globalUserKey = getGlobalUserKey(authorizationHeader, userKey)
			def newNotifications = notificationHelper.listNotifications(globalUserKey)
			if (newNotifications instanceof InvalidData)
			{
				return newNotifications
			}

			// Join and return both notification lists
			return joinNotificationLists(oldNotifications, newNotifications)
		} catch (GeneralSecurityException e) {
			logger.warn('/v3/user/notifications: problem decoding encoded key param[' + encodedUserKey + "].", e)
			return new InvalidData(message: 'Invalid user key: ' + encodedUserKey)
		}
	}

	private ListNotificationsResult joinNotificationLists(ListNotificationsResult oldNotifications, ListNotificationsResult newNotifications) {
		def unreadCount = oldNotifications.unreadCount + newNotifications.unreadCount

		def notifications = new ArrayList<Notification>()
		notifications.addAll(oldNotifications.notifications)
		notifications.addAll(newNotifications.notifications)

		notifications.sort(new Comparator<Notification>() {

			@Override
			int compare(Notification o1, Notification o2) {
				return o2.createDateTime <=> o1.createDateTime
			}
		})

		def resultNotifications = [
				unreadCount  : unreadCount,
				notifications: notifications
		] as ListNotificationsResult

		// Return
		return resultNotifications
	}

	private String getGlobalUserKey(String authorizationHeader, String userKey) {
		def globalMemberId = getGlobalMemberId(authorizationHeader)
		def countryCode = userKey.split(",")[0]
		def globalUserKey = countryCode + "," + globalMemberId
		globalUserKey
	}

	private String getGlobalMemberId(String authorizationHeader) {
		def accessToken = authorizationHeader.startsWith("Bearer ") ? authorizationHeader.substring(7) : authorizationHeader;
		def decodedToken = accessTokenDecoder.decode(accessToken)
		def globalMemberId = decodedToken.getSubject()
		return globalMemberId
	}

	@RequestMapping('/v3/user/acknowledge')
	acknowledgeWithAccessTokenV3(
			@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
			@RequestParam(name = 'userkey', required = true) String encodedUserKey)
	{
		logger.debug 'hitting v3 mark notification endpoint'
		try {
			def userKey = base64Decode(encodedUserKey)
			String globalUserKey = getGlobalUserKey(authorizationHeader, userKey)

			// Acknowledge both old and new notifications
			def oldNotifications = notificationHelper.acknowledgeNotifications(userKey, true)
			def newNotifications = notificationHelper.acknowledgeNotifications(globalUserKey, true)

			joinNotificationLists(oldNotifications, newNotifications)
		} catch (GeneralSecurityException e) {
			logger.warn('/v3/user/acknowledge: problem decoding encoded key param[' + encodedUserKey + "].", e)
			return new InvalidData(message: 'Invalid user key: ' + encodedUserKey)
		}
	}

	@RequestMapping(path = '/v3/user/read', method = RequestMethod.POST)
	readWithAccessTokenV3(
			@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
			@RequestParam(name = 'userkey', required = true) String encodedUserKey,
			@RequestBody(required = true) String body)
	{
		logger.debug 'hitting v3 mark as read endpoint'
		try {
			def userKey = base64Decode(encodedUserKey)
			String globalMemberId = getGlobalMemberId(authorizationHeader)
			String globalUserKey = getGlobalUserKey(authorizationHeader, userKey)
			def oldNotifications = notificationHelper.readNotification(userKey, body, globalMemberId)
			def newNotifications = notificationHelper.listNotifications(globalUserKey)
			return joinNotificationLists(oldNotifications, newNotifications)
		} catch (GeneralSecurityException e) {
			logger.warn('/v3/user/read: problem decoding encoded key param[' + encodedUserKey + "]", e)
			return new InvalidData(message: 'Invalid user key: ' + encodedUserKey)
		}
	}

	@RequestMapping(path = '/v3/user/readall', method = RequestMethod.POST)
	readAllWithAccessTokenV3(
			@RequestHeader(name = HttpHeaders.AUTHORIZATION, required = true) final String authorizationHeader,
			@RequestParam(name = 'userkey', required = true) String encodedUserKey)
	{
		logger.debug 'hitting v3 mark all notifications as read endpoint'
		try {
			def userKey = base64Decode(encodedUserKey)
			String globalUserKey = getGlobalUserKey(authorizationHeader, userKey)
			// Mark both old and new as read
			def oldNotifications = notificationHelper.readAllNotifications(userKey)
			def newNotifications = notificationHelper.readAllNotifications(globalUserKey)
			return joinNotificationLists(oldNotifications, newNotifications)
		} catch (GeneralSecurityException e) {
			logger.warn('/v3/user/readall: problem decoding encoded key param[' + encodedUserKey + "]", e)
			return new InvalidData(message: 'Invalid user key: ' + encodedUserKey)
		}
	}
}
