package notification.controller;

import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import notification.data.SQSSubscriptionEventProcessor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

@Component
public class SQSSubscriptionConsumer {

    private final Log logger = LogFactory.getLog(getClass());

    @Autowired
    private Environment env;

    @Autowired
    private SQSSubscriptionEventProcessor sqsSubscriptionEventProcessor;

    @JmsListener(destination = "${amazon.sqs.queues.bc-subscription-notification-svc}")
    public void onMessage(SQSTextMessage message)
    {
        try
        {
            logger.info("Starting process sqs message on " + env.getProperty("amazon.sqs.queues.bc-subscription-notification-svc"));
            sqsSubscriptionEventProcessor.addSubscriptionEvent(message);
        }
        catch (Exception e)
        {
            sqsSubscriptionEventProcessor.requeueMessage(message, e);
        }
    }
}
