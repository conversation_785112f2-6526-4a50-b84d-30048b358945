package notification.controller

import notification.data.KeyEncrypter
import notification.service.NotificationHelper

import org.apache.commons.logging.Log
import org.apache.commons.logging.LogFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.actuate.endpoint.mvc.EndpointMvcAdapter
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody

@Component
class InternalAdminMvcEndpoint extends EndpointMvcAdapter {
	
	private final Log logger = LogFactory.getLog(getClass())
	private final InternalAdminEndpoint delegate
	
	@Autowired
	private NotificationHelper notificationHelper

	@Autowired
	InternalAdminMvcEndpoint(InternalAdminEndpoint delegate) {
		super(delegate)
		this.delegate = delegate
	}
	
	@RequestMapping('userkey/{userKey}')
	@ResponseBody
	userKey(@PathVariable String userKey) {
		logger.debug 'fetching userKey hash for ' + userKey
		KeyEncrypter.encode(userKey)
	}
	
	/**
	 * Convenience interface for internal users, such as load tests, to load a user's
	 * notifications using an unencrypted key value
	 * 
	 * Maps to same path in HomeController.
	 * @param userKey countryCode,userId pair
	 * @return json-encoded list of notifications for a user
	 */
	@RequestMapping('v1/user/notifications')
	@ResponseBody
	notifications(@RequestParam('userkey') String userKey) {
		logger.debug 'hitting admin user notification endpoint'
		notificationHelper.listNotifications(userKey)
	}
	
}