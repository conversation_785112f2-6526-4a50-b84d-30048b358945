package notification.auth;

import com.auth0.jwt.algorithms.Algorithm;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.security.spec.X509EncodedKeySpec;

import org.bouncycastle.util.io.pem.PemObject;
import org.bouncycastle.util.io.pem.PemReader;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * Created by emurphy on 4/19/17.
 */
@Component
public class VerifyAlgorithmProducer
{
	/**
	 * The jwt verification algorithm
	 *
	 * @param signingKey
	 * @return
	 */
	@Cacheable("verifyAlgorithm")
	public Algorithm verifyAlgorithm(final String signingKey)
	{
		return producePublicAlgorithm(
			new X509EncodedKeySpec(readPemResource(signingKey).getContent())
		);
	}
	
	/**
	 * Takes a keyspec for the public key and returns the signing algorithm
	 *
	 * @param keySpec
	 * @return
	 */
	private Algorithm producePublicAlgorithm(KeySpec keySpec)
	{
		return produceAlgorithm(() -> (RSAKey) KeyFactory.getInstance("RSA").generatePublic(keySpec));
	}
	
	/**
	 * Given an RsaKeySupplier, returns an algorithm
	 *
	 * @param keySupplier
	 * @return
	 */
	private Algorithm produceAlgorithm(RsaKeySupplier keySupplier)
	{
		try
		{
			return Algorithm.RSA256(keySupplier.get());
		}
		catch (NoSuchAlgorithmException | InvalidKeySpecException e)
		{
			throw new RuntimeException("Failed to create algorithm", e);
		}
	}
	
	private interface RsaKeySupplier
	{
		RSAKey get() throws InvalidKeySpecException, NoSuchAlgorithmException;
	}
	
	/**
	 * Reads a pem file represented as a spring resource
	 *
	 * @param pemResource
	 * @return PemObject
	 */
	private PemObject readPemResource(String pemResource)
	{
		try
		{
			return new PemReader(new BufferedReader(new InputStreamReader(new ByteArrayInputStream(pemResource.getBytes())))).readPemObject();
		}
		catch (IOException e)
		{
			throw new RuntimeException("Failed to read validation key from pem.", e);
		}
	}
	
}
