package notification.auth.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

/**
 * Created by emurphy on 4/19/17.
 */
@Service
public class ValidationKeyClient
{
	private static final Logger log = LoggerFactory.getLogger(ValidationKeyClient.class);
	private static final String VALIDATION_KEY_PATH = "/token/validation-key";
	
	private final String authServiceBaseUrl;
	private final RestTemplate restTemplate;
	
	public ValidationKeyClient(@Autowired RestTemplate restTemplate, @Value("${authServiceBaseUrl}") final String authServiceBaseUrl)
	{
		this.authServiceBaseUrl = authServiceBaseUrl;
		this.restTemplate = restTemplate;
	}
	
	//
	// TODO: wrap in circuit breaker
	//
	
	/**
	 * Call auth service to get validation key
	 *
	 * @return the validation key
	 */
	public String getValidationKey()
	{
		try
		{
			return restTemplate.getForObject(url(), String.class);
		}
		catch (HttpStatusCodeException e)
		{
			log.error(url() + " returned " + e.getStatusCode().getReasonPhrase());
			throw e;
		}
		catch (RuntimeException e)
		{
			//
			// presume this is a connection error, treat it as a request timeout
			//
			log.error(url() + " exception " + e.getMessage());
			throw e;
		}
	}
	
	private String url()
	{
		return authServiceBaseUrl + VALIDATION_KEY_PATH;
	}
}
