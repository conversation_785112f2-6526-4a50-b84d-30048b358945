package notification.auth;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by emurphy on 4/18/17.
 */
@Component
public class AccessTokenRequestValidator
{

	private final Log logger = LogFactory.getLog(getClass());

	private final AccessTokenDecoder accessTokenDecoder;

	public AccessTokenRequestValidator(
			@Autowired AccessTokenDecoder accessTokenDecoder
	)
	{
		this.accessTokenDecoder = accessTokenDecoder;
	}

	/**
	 * Validate an accessToken string for calls the require memberId.
	 * The access token must decode and must
	 * - have a valid expiration date that is in the future
	 * - have a siteUser that matches the desired userId
	 *
	 * @param accessToken access token as a string
	 * @param userKey     userKey as "site,member_id"
	 * @param verifyAudience     verifyAudience contains "notification-service"
	 * @return true if valid, false if not
	 */
	public boolean isValid(final String accessToken, final String userKey, final boolean verifyAudience)
	{
		if(null == accessToken) return false;
		if(null == userKey) return false;

		// decode the JWT so we can get at the claims
		final DecodedJWT decodedJWT = accessTokenDecoder.decode(accessToken);

		if (null == decodedJWT) return false;
		if (null == decodedJWT.getSubject()) return false;

		Claim siteUserClaim = decodedJWT.getClaim("site_user");
		if (null == siteUserClaim) return false;

		List<String> audience = decodedJWT.getAudience();
		boolean isIntl = audience.stream().anyMatch(e -> e.startsWith("intl-"));
		String siteUser = siteUserClaim.asString();

		if (verifyAudience && !audience.contains("notification-service")) return false;

		//
		// call is made on behalf of a member, so their must match the url path to their resource
		//
		final SiteUser urlSiteUser = userKeyToSiteUser(userKey);
		if (isIntl) {
			Long memberId = Long.parseLong(siteUser.split(",")[1]);
			if(!urlSiteUser.getSite().startsWith("intl")) return false;
			if(!urlSiteUser.getSiteUid().equals(memberId)) return false;
		} else {
			final SiteUser tokenSiteUser = new SiteUser(siteUser);
			if(!urlSiteUser.equals(tokenSiteUser)) return false;
		}

		return true;
	}

	private SiteUser userKeyToSiteUser(final String userKey)
	{
		// user key has locale,siteUid, SiteUser has site,siteUid
		final SiteUser urlSiteUser = new SiteUser(userKey);
		switch(urlSiteUser.getSite())
		{
			case "en_US": return new SiteUser("bcsite", urlSiteUser.getSiteUid());
			case "en_GB": return new SiteUser("intl-gb", urlSiteUser.getSiteUid());
			case "en_CA": return new SiteUser("intl-ca", urlSiteUser.getSiteUid());
			case "en_IN": return new SiteUser("intl-in", urlSiteUser.getSiteUid());
			case "de_DE": return new SiteUser("intl-de", urlSiteUser.getSiteUid());
			case "es_US": return new SiteUser("intl-es", urlSiteUser.getSiteUid());
			case "ar_SA": return new SiteUser("intl-sa", urlSiteUser.getSiteUid());
			case "pt_BR": return new SiteUser("intl-br", urlSiteUser.getSiteUid());
			case "en_AU": return new SiteUser("intl-au", urlSiteUser.getSiteUid());
			default: return urlSiteUser;    // Intl site use local to encode site
		}
	}

}
