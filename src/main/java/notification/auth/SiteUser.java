package notification.auth;

/**
 * Represents the site_user claim in the JWT
 *
 * Created by emurphy on 2/28/18.
 */
public final class SiteUser
{
	public static final String SITE_USER = "site_user";
	
	private final String site;
	private final Long siteUid;
	
	public SiteUser(final String site, final Long siteUid)
	{
		if((null == site) || site.isEmpty()) throw new IllegalArgumentException("SiteUser site must not be null or empty.");
		if(null == siteUid) throw new IllegalArgumentException("SiteUser siteUid must not be null.");
		
		this.site = site;
		this.siteUid = siteUid;
	}
	
	public SiteUser(final SiteUser that)
	{
		this(that.site, that.siteUid);
	}
	
	/**
	 * construction from string representation; "site,siteUid"
	 */
	public SiteUser(final String siteUserString)
	{
		if(null == siteUserString)
		{
			throw new IllegalArgumentException("SiteUser siteUserString must not be null.");
		}
		if(siteUserString.isEmpty())
		{
			throw new IllegalArgumentException("SiteUser siteUserString must not be empty.");
		}
		
		final String[] parts = siteUserString.split(",");
		if(2 != parts.length)
		{
			throw new IllegalArgumentException("SiteUser siteUserString must be in form 'site,siteUid'");
		}
		
		final String site = parts[0].trim();
		final Long siteUid = Long.valueOf(parts[1].trim());
		
		if(site.isEmpty()) throw new IllegalArgumentException("SiteUser site must not be null or empty.");
		if(null == siteUid) throw new IllegalArgumentException("SiteUser siteUid must not be null.");
		
		this.site = site;
		this.siteUid = siteUid;
	}
	
	public String getSite()
	{
		return site;
	}
	
	public Long getSiteUid()
	{
		return siteUid;
	}
	
	@Override
	public String toString()
	{
		final StringBuffer sb = new StringBuffer();
		sb.append(site).append(',').append(siteUid);
		return sb.toString();
	}
	
	@Override
	public boolean equals(Object o)
	{
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		
		final SiteUser siteUser = (SiteUser) o;
		
		if (site != null ? !site.equals(siteUser.site) : siteUser.site != null) return false;
		return siteUid != null ? siteUid.equals(siteUser.siteUid) : siteUser.siteUid == null;
	}
	
	@Override
	public int hashCode()
	{
		int result = site != null ? site.hashCode() : 0;
		result = 31 * result + (siteUid != null ? siteUid.hashCode() : 0);
		return result;
	}
}
