package notification.auth;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AccessTokenDecoder
{

    private static final Logger log = LoggerFactory.getLogger(AccessTokenRequestValidator.class);

    private final VerifyAlgorithmProducer verifyAlgorithmProducer;
    private final ValidationKeyProducer validationKeyProducer;
    private final String validationKeyIssuer;

    public AccessTokenDecoder(
            @Autowired final VerifyAlgorithmProducer verifyAlgorithmProducer,
            @Autowired final ValidationKeyProducer validationKeyProducer,
            @Value("${authServiceIssuer}") final String validationKeyIssuer
    )
    {
        this.verifyAlgorithmProducer = verifyAlgorithmProducer;
        this.validationKeyProducer = validationKeyProducer;
        this.validationKeyIssuer = validationKeyIssuer;
    }

    public DecodedJWT decode(final String accessToken)
    {
        try
        {
            Algorithm algorithm = verifyAlgorithmProducer.verifyAlgorithm(validationKeyProducer.validationKey());
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(this.validationKeyIssuer)
                    .build(); //Reusable verifier instance
            DecodedJWT jwt = verifier.verify(accessToken);
            return jwt;
        }
        catch (JWTVerificationException exception)
        {
            //Invalid signature/claims
            log.error(exception.getMessage());
            return null;
        }
        catch (Exception e)
        {
            log.error(e.getMessage());
            throw e;
        }
    }

}
