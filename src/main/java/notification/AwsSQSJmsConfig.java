package notification;

import com.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.internal.StaticCredentialsProvider;
import com.amazonaws.regions.RegionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.support.destination.DynamicDestinationResolver;

import javax.jms.Session;

@Configuration
@EnableJms
public class AwsSQSJmsConfig {

    @Value("${amazon.sqs.credentials.accesskey}")
    private String accessKey;

    @Value("${amazon.sqs.credentials.secretkey}")
    private String secretKey;

    @Value("${amazon.sqs.credentials.session-token-key}")
    private String sessionToken;

    @Value("${amazon.sqs.region}")
    private String region;

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private SQSConnectionFactory connectionFactory;

    @Bean
    public SQSConnectionFactory createConnectionFactory() {
        return SQSConnectionFactory.builder()
                .withRegion(RegionUtils.getRegion(region))
                .withAWSCredentialsProvider(new StaticCredentialsProvider(env.equalsIgnoreCase("dev") ?
                        new BasicSessionCredentials(accessKey, secretKey, sessionToken) : //Develop environment must have a sessionToken, due to 'onelogin-aws-assume-role' credential generations
                        new BasicAWSCredentials(accessKey,secretKey)))
                .build();
    }

    @Bean
    public DefaultJmsListenerContainerFactory jmsListenerContainerFactory() {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(this.connectionFactory);
        factory.setDestinationResolver(new DynamicDestinationResolver());
        factory.setSessionAcknowledgeMode(Session.AUTO_ACKNOWLEDGE);

        return factory;
    }

    @Bean
    public JmsTemplate defaultJmsTemplate() {
        return new JmsTemplate(this.connectionFactory);
    }
}
