package notification.service

import notification.data.EventProcessor
import notification.data.InvalidData
import notification.dtos.DataDto
import notification.dtos.ListNotificationsResult
import notification.pojos.MemberActivityStatus
import notification.pojos.Notification
import notification.pojos.NotificationTypeEnum
import notification.pojos.UserHashKey
import notification.pojos.UserHashKeyMarshaller
import notification.repositories.MemberActivityStatusRepository
import notification.repositories.NotificationRepository

import com.bc.monitoring.graphite.GraphiteClient

import org.apache.commons.logging.Log
import org.apache.commons.logging.LogFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.util.StringUtils

@Component
class NotificationHelper {

	public static int NOTIFICATION_LIMIT = 500;

	private static final String LIST_REQUEST = 'notification.user.'

	private final Log logger = LogFactory.getLog(getClass())

	private UserHashKeyMarshaller marshaller = new UserHashKeyMarshaller()

	@Autowired
	private NotificationRepository notificationRepo

	@Autowired
	private MemberActivityStatusRepository memberActivityStatusRepo

	@Autowired
	private GraphiteClient graphiteClient

	@Autowired
	private EventProcessor eventProcessor

	@Value('${notification.lifetime}')
	private int notificationLifetime = 7 // life time in days specified in application{-profile}.yml

	/**
	 * Generate count and list of notifications for user
	 * @param userKey unencrypted countryCode,userId pair
	 * @return json-encoded list of notifications for a user
	 */
	Object listNotifications(String userKey) {
		def (countryCode, userId) = userKey.split(',')
		// Validate that user key contains a country code of the expected length
		// (avoids sending garbage data to Graphite)
		if (!countryCode || countryCode.length() != 5) {
			logger.warn('Problem generating user notification JSON response. Invalid countryCode in userKey: ' + userKey)
			return new InvalidData(message: 'invalid userKey: ' + userKey)
		}
		graphiteClient.increment(LIST_REQUEST + countryCode)

		try {
			MemberActivityStatus activityStatus = memberActivityStatusRepo.findOne(marshaller.unmarshall(UserHashKey.class, userKey))
			List<Notification> notifications = notificationRepo.findByUserKeyHash(userKey)

			def unreadCount = 0;
			//Filter out Old Php Notifications from unreadCount, for US and UK only
			//See more: https://everydayhealth.atlassian.net/browse/BCS-13963
			if (countryCode.toUpperCase().equals('EN_US') || countryCode.toUpperCase().equals('EN_GB'))
			{
				unreadCount = notifications.inject(0) { result, it ->
					result += (!it.read &&
							!isNotificationFromPhp(it) &&
							it.createDateTime > activityStatus?.timestamp) ? 1 : 0
				}
			}
			else
			{
				unreadCount = notifications.inject(0) { result, it ->
					result += (!it.read && it.createDateTime > activityStatus?.timestamp) ? 1 : 0
				}
			}

			return [unreadCount: unreadCount, notifications: notifications] as ListNotificationsResult
		}
		catch (all) {
			logger.warn('Problem generating user notification JSON response: ' + all.message, all)
			return new InvalidData(message: all.message)
		}
	}

	/**
	 * Converts all Direct Messages notifications to the old v1 and v2 style.
	 * This was necessary for moltres/community compatibility.
	 * See more: https://everydayhealth.atlassian.net/browse/BCS-13224
	 *
	 * @param list of notifications for a user
	 * @returnlist list of notifications for a user
	 */
	Object convertDirectMessagesOutputs(List<Notification> notifications) {
		try {
			for (Notification notification : notifications) {
				if (notification.contentActionKey.action.equalsIgnoreCase(NotificationTypeEnum.DIRECT_MESSAGE.toString())) {

					notification.genericContent = [
							"type": "InboxMessage",
							"imgClass": "notificationPostCardIcon",
							"href": notification.getGenericContent().href,
							"message": notification.getGenericContent().message
							];
					notification.contentActionKey.action = 'generic';
				}
			}
			return notifications
		}
		catch (all) {
			logger.warn('Problem converting DirectMessages notifications response: ' + all.message, all)
			return new InvalidData(message: all.message)
		}
		return null;
	}

	Object acknowledgeNotifications(String userKey, boolean respondWithList = false) {
		// Validate that user key contains a country code of the expected length
		// (avoids sending garbage data to Graphite)
		def (countryCode, userId) = userKey.split(',')
		if (!countryCode || countryCode.length() != 5) {
			logger.warn('Problem generating user notification JSON response. Invalid countryCode in userKey: ' + userKey)
			return new InvalidData(message: 'invalid userKey: ' + userKey)
		}
		if (StringUtils.isEmpty(userId)) {
			logger.warn('Problem generating user notification JSON response. Empty userKey')
			return new InvalidData(message: 'empty userKey')
		}

		MemberActivityStatus activityStatus = memberActivityStatusRepo.findOne(marshaller.unmarshall(UserHashKey.class, userKey))
		if (!activityStatus) {
			activityStatus = new MemberActivityStatus(countryCode, userId, notificationLifetime)
		} else {
			activityStatus.reset(notificationLifetime)
		}
		Object retval = memberActivityStatusRepo.save(activityStatus)
		if (respondWithList)
		{
			retval = listNotifications(userKey)
		}
		return retval
	}

	/**
	 * Mark the notification passed in the payload a read,
	 * the return the updated list of notifications.
	 *
	 * @param userKey 'countryCode,memberId'
	 * @param msg json payload for the notification to mark as read.
	 * @return Object with count of unread notifications and optional list of notifications
	 */
	Object readNotification(String userKey, msg, String globalUserId) {
		try {
			validateUserKey(userKey)
			def (countryCode, userId) = userKey.split(',')

			// validate that the payload matches the user
			List<DataDto> dtos = eventProcessor.toDtos(msg)
			for (DataDto dto : dtos) {
				if (userId != dto.userId.toString() || countryCode != dto.countryCode) {
					logger.warn('Problem generating user notification JSON response. Payload does not match userKey')
					return new InvalidData(message: 'payload userKey mismatch')
				}
				eventProcessor.markDtoRead(dto, globalUserId)
			}
		} catch (Exception ex) {
			return new InvalidData(message: ex.getMessage())
		}
		return listNotifications(userKey)
	}

	/**
	 * Mark all notifications from userKey read,
	 * return the updated list of notifications.
	 *
	 * @param userKey 'countryCode,memberId'
	 * @return Object with count of unread notifications and optional list of notifications
	 */
	Object readAllNotifications(String userKey) {
		try {
			validateUserKey(userKey)
			eventProcessor.markAllNotificationRead(userKey);
		} catch (Exception ex) {
			return new InvalidData(message: ex.getMessage())
		}
		return listNotifications(userKey)
	}

	/**
	 * @return lifetime of a notification in days
	 */
	public int getNotificationLifetime() {
		return this.notificationLifetime
	}

	/**
	 * Validate that user key contains a country code of the expected length
	 * (avoids sending garbage data to Graphite)
	 * @param userKey 'countryCode,memberId'
	 */
	private void validateUserKey(String userKey) throws Exception{
		def (String countryCode, String userId) = userKey.split(',')
		if (!countryCode || countryCode.length() != 5) {
			logger.warn('Problem generating user notification JSON response. Invalid countryCode in userKey: ' + userKey)
			throw new Exception("invalid userKey: " + userKey);
		}
		if (StringUtils.isEmpty(userId)) {
			logger.warn('Problem generating user notification JSON response. Empty userKey')
			throw new Exception("empty userKey")
		}
	}

	/**
	 * Checking if notification is in the old structure.
	 * Php Community notification doesn't include genericContent fields.
	 *
	 * @param notification
	 */
	private boolean isNotificationFromPhp (Notification notification) {
		return !(notification?.genericContent?.href &&
				notification?.genericContent?.message &&
				notification?.genericContent?.userScreenName);
	}
}
