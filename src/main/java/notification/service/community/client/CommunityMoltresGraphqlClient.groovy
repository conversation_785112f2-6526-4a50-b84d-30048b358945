package notification.service.community.client

import clover.org.apache.commons.lang.time.StopWatch
import notification.jsonrestclient.JsonRestClient
import notification.service.community.model.blockedusers.BlockedUsersQuery
import notification.service.community.model.request.GraphQLQueryRequest
import notification.service.community.model.response.BlockedUsersResponseData
import notification.service.community.model.response.GraphQLResponse
import org.apache.commons.logging.Log
import org.apache.commons.logging.LogFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Component
import org.springframework.web.client.HttpStatusCodeException

@Component
class CommunityMoltresGraphqlClient implements ICommunityGraphqlClient {

    private final Log log = LogFactory.getLog(this.getClass())

    @Autowired
    private JsonRestClient jsonRestClient = new JsonRestClient()

    @Value('${communityMoltres.us.baseUrl}')
    private String communityMoltresBaseUrlUs

    @Value('${communityMoltres.gb.baseUrl}')
    private String communityMoltresBaseUrlGb

    @Value('${communityMoltres.au.baseUrl}')
    private String communityMoltresBaseUrlAu

    @Value('${communityMoltres.br.baseUrl}')
    private String communityMoltresBaseUrlBr

    @Value('${communityMoltres.ca.baseUrl}')
    private String communityMoltresBaseUrlCa

    @Value('${communityMoltres.de.baseUrl}')
    private String communityMoltresBaseUrlDe

    @Value('${communityMoltres.es.baseUrl}')
    private String communityMoltresBaseUrlEs

    @Value('${communityMoltres.in.baseUrl}')
    private String communityMoltresBaseUrlIn

    @Value('${spring.profiles.active}')
    private String env

    private final String COMMUNITY_MOLTRES_GRAPHQL_ENDPOINT = "/graphql"

    @Override
    BlockedUsersResponseData blockedUsers(String countryCode, BlockedUsersQuery query) {
        GraphQLQueryRequest request = new GraphQLQueryRequest(query.getQueryString())
        ResponseEntity<GraphQLResponse> responseEntity = callMoltresGraphQLAPI(countryCode, request)

        return responseEntity.getBody().getData()
    }

    /**
     * Calls Community Moltres GraphQL API.
     * @param request
     * @return
     */
    private ResponseEntity<GraphQLResponse> callMoltresGraphQLAPI(String countryCode, GraphQLQueryRequest request) {
        HttpHeaders httpHeaders
        ResponseEntity<GraphQLResponse> responseEntity = null
        String baseUrl = getBaseUrl(countryCode)
        try {
            log.debug("Initializing call to Moltres GraphQL API: " + baseUrl + COMMUNITY_MOLTRES_GRAPHQL_ENDPOINT)
            httpHeaders = addHeaders()
            //make a get request and if it fails, retry up to 3 times
            final StopWatch stopWatch = new StopWatch()
            stopWatch.start()

            try {
                responseEntity = jsonRestClient.postRetry(baseUrl + COMMUNITY_MOLTRES_GRAPHQL_ENDPOINT, request, GraphQLResponse.class, httpHeaders)
            }
            finally {
                stopWatch.stop()
            }
            log.debug("Response in " + stopWatch.getTime())
        }
        catch (HttpStatusCodeException e) {
            log.error("Couldn't search posts on GraphQL Moltres: ", e)
            log.error(e.getMessage())
            throw e
        }
        return responseEntity
    }

    private String getBaseUrl(String countryCode) {
        switch (countryCode) {
            case "en_GB": return communityMoltresBaseUrlGb;
            case "en_AU": return communityMoltresBaseUrlAu;
            case "pt_BR": return communityMoltresBaseUrlBr;
            case "en_CA": return communityMoltresBaseUrlCa;
            case "de_DE": return communityMoltresBaseUrlDe;
            case "es_US": return communityMoltresBaseUrlEs;
            case "en_IN": return communityMoltresBaseUrlIn;
            case "en_US": default: return communityMoltresBaseUrlUs;
        }
    }

    private HttpHeaders addHeaders() {
        HttpHeaders headers = new HttpHeaders()

        //Set krusty-Cl0wn cookie for stagings calls
        if (null != env && env.equalsIgnoreCase("stag")) {
            headers.add("Cookie", "bcstag=a3J1c3R5OkNsMHdu")
        }
        return headers
    }
}
