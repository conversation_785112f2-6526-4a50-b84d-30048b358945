package notification.service.community.model.blockedusers

import notification.service.community.model.request.GraphQLQuery

class BlockedUsersQuery implements GraphQLQuery {

	private String globalUserId = ""

	BlockedUsersQuery(String globalUserId) {
		if (globalUserId != null)
			this.globalUserId = globalUserId
	}

	String getGlobalUserId() {
		return globalUserId
	}

	void setGlobalUserId(String globalUserId) {
		this.globalUserId = globalUserId
	}

	@Override
	String getQueryString() {
		StringBuilder query = new StringBuilder("query {blockedUsers(")
		query.append(" globalUserId: \"" + globalUserId + "\"")
		query.append("){")
		query.append(BlockedUsersResult.getQueryString())
		query.append("}}")

		return query.toString()
	}
}
