package notification.service.community.model.blockedusers

class BlockedUsersResult implements Serializable {
    private CommunityUser user
    private String type

    static String getQueryString() {
        return " type user { " + CommunityUser.getQueryString() + " }"
    }

    CommunityUser getUser() {
        return user
    }

    void setUser(CommunityUser user) {
        this.user = user
    }

    String getType() {
        return type
    }

    void setType(String type) {
        this.type = type
    }
}
