package notification

import com.amazon.sqs.javamessaging.SQSConnectionFactory
import com.amazonaws.auth.BasicSessionCredentials
import com.amazonaws.internal.StaticCredentialsProvider
import com.amazonaws.regions.RegionUtils
import org.socialsignin.spring.data.dynamodb.core.DynamoDBTemplate
import org.socialsignin.spring.data.dynamodb.repository.config.EnableDynamoDBRepositories
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClient
import com.amazonaws.services.dynamodbv2.document.DynamoDB

@Configuration
@EnableDynamoDBRepositories(basePackages = "notification.repositories", dynamoDBOperationsRef="dynamoDBOperations")
class DynamoDBConfig {
	@Value('${amazon.aws.accesskey}') 
	private String accesskey
	
	@Value('${amazon.aws.secretkey}') 
	private String secretkey

	@Value('${amazon.aws.session-token-key}')
	private String sessionToken
	
	@Value('${amazon.dynamodb.endpoint}') 
	private String endpoint
	
	@Value('${amazon.aws.region}')
	private String region

	@Value('${spring.profiles.active}')
	private String env;

	@Bean
	amazonDynamoDB() {
		AmazonDynamoDBClient client = 
			accesskey ? new AmazonDynamoDBClient(env.equalsIgnoreCase("dev") ?
					new BasicSessionCredentials(accesskey, secretkey, sessionToken) :
					new BasicAWSCredentials(accesskey, secretkey)) : new AmazonDynamoDBClient()
		client.endpoint = endpoint
		client.setSignerRegionOverride(region)
		client
	}
	
	@Bean
	dynamoDBOperations() {
		return new DynamoDBTemplate(amazonDynamoDB());
	}
	
	@Bean
	dynamoDB() {
		new DynamoDB(amazonDynamoDB())
	}
}
