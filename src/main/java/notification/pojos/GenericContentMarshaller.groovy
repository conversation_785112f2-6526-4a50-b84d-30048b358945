package notification.pojos

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshaller
import com.google.gson.Gson
import com.google.gson.GsonBuilder

class GenericContentMarshaller implements DynamoDBMarshaller<Object> {
	private static Gson gson = new GsonBuilder().create()

	@Override
	String marshall(Object obj) {
		gson.toJson(obj)
	}

	@Override
	Object unmarshall(Class<Object> clazz, String obj) {
		if (!obj) return null
		
		gson.fromJson(obj, clazz)
	}

}
