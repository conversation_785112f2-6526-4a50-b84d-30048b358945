package notification.pojos

import com.amazonaws.services.dynamodbv2.datamodeling.*
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import org.springframework.data.annotation.Id

@DynamoDBTable(tableName = "GlobalSubscription")
@DynamoDBDocument
@EqualsAndHashCode(includes=['id'])
@ToString(includeFields=true, includeNames=true, includePackage=false)
class GlobalSubscription {
	@Id
	private GlobalSubscriptionId id = new GlobalSubscriptionId()

    GlobalSubscription() {}

    GlobalSubscription(String countryCode, String globalMemberId, Long contentId) {
		id = GlobalSubscriptionId.newId(countryCode, globalMemberId, contentId)
	}

	@DynamoDBIgnore
	GlobalSubscriptionId getId() {
		id
	}

	@DynamoDBHashKey(attributeName = "ContentHashKey")
	@DynamoDBMarshalling(marshallerClass=ContentHashKeyMarshaller.class)
	ContentHashKey getContentKey() {
		id.contentKey
	}

	void setContentKey(ContentHashKey contentKey) {
		id.contentKey = contentKey
	}


	@DynamoDBRangeKey(attributeName = "UserRangeKey")
	String getGlobalMemberId() {
		id.globalMemberId
	}

	void setGlobalMemberId(String globalMemberId) {
		id.globalMemberId = globalMemberId
	}

	@DynamoDBAttribute(attributeName = "CreateDate")
	@DynamoDBMarshalling(marshallerClass=CurrentDateTimeMarshaller.class)
	Date createDateTime = new Date()
}
