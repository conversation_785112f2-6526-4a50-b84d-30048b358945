package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import groovy.transform.TupleConstructor

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey

@ToString(includePackage=false, includeNames=true)
@TupleConstructor(includes=['contentKey', 'userId'])
@EqualsAndHashCode(includes=['contentKey', 'userId'])
class SubscriptionId implements Serializable {
	@DynamoDBHashKey
	ContentHashKey contentKey
	
	@DynamoDBRangeKey
	Long userId
	
	static SubscriptionId newId(String countryCode, Long userId, Long id) {
		new SubscriptionId(new ContentHashKey(countryCode, id), userId)
	}
}
