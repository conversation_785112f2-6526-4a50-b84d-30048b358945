package notification.pojos

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshaller

class ContentHashKeyMarshaller implements DynamoDBMarshaller<ContentHashKey> {

	@Override
	String marshall(ContentHashKey contentKey) {
		"$contentKey.countryCode,$contentKey.contentId"
	}

	@Override
	ContentHashKey unmarshall(Class<ContentHashKey> clazz, String obj) {
		if (!obj) return null
		
		String[] parts = obj.split(',')
		new ContentHashKey(parts[0], parts[1] as long)
	}

}
