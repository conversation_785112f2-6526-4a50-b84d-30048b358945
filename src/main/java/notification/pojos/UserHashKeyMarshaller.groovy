package notification.pojos

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshaller

class UserHashKeyMarshaller implements DynamoDBMarshaller<UserHashKey> {

	@Override
	String marshall(UserHashKey userKey) {
		"$userKey.countryCode,$userKey.userId"
	}

	@Override
	UserHashKey unmarshall(Class<UserHashKey> clazz, String obj) {
		if (!obj) return null

		String[] parts = obj.split(',')

		new UserHashKey(parts[0], parts[1])
	}

}
