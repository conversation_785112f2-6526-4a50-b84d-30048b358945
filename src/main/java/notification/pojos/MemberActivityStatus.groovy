package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import notification.data.EventProcessor

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIgnore
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshalling
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.fasterxml.jackson.annotation.JsonIgnore

import java.time.Instant
import java.time.format.DateTimeFormatter

@DynamoDBTable(tableName = "MemberActivityStatus")
@DynamoDBDocument
@EqualsAndHashCode(includes=['id'])
@ToString(includeFields=true, includeNames=true, includePackage=false)
class MemberActivityStatus {
	private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern('uMMdd')

	MemberActivityStatus() {}

	MemberActivityStatus(String countryCode, Long userId, int lifetimeDays) {
		userKey = new UserHashKey(countryCode, userId)
		ttl = Instant.now().getEpochSecond() + lifetimeDays * 24 * 60 * 60
	}

	MemberActivityStatus(String countryCode, String globalUserId, int lifetimeDays) {
		userKey = new UserHashKey(countryCode, globalUserId)
		ttl = Instant.now().getEpochSecond() + lifetimeDays * 24 * 60 * 60
	}

	@DynamoDBIgnore
	@JsonIgnore
	UserHashKey getId() {
		userKey
	}

	@DynamoDBHashKey(attributeName = "UserHashKey")
	@DynamoDBMarshalling(marshallerClass=UserHashKeyMarshaller.class)
	@JsonIgnore
	UserHashKey userKey

	@DynamoDBAttribute(attributeName = "Timestamp")
	@DynamoDBMarshalling(marshallerClass=CurrentDateTimeMarshaller.class)
	Date timestamp = new Date()

	//
	// time-to-live value for this record.
	// This is specified as a Epoch value in seconds.
	// Once the current time becomes greater than ttl, the record may be automatically deleted by dynamodb.
	//
	@DynamoDBAttribute(attributeName = "ttl")
	@JsonIgnore
	Long ttl

	/**
	 * Reset the notification's create date and time to live.
	 *
	 * @param lifetimeDays number of days notification is valid
	 */
	void reset(int lifetimeDays) {
		timestamp = new Date()
		ttl = Instant.now().getEpochSecond() + lifetimeDays * 24 * 60 * 60
	}
}
