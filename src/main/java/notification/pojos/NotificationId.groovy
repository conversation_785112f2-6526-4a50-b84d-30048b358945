package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import groovy.transform.TupleConstructor

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey

@ToString(includePackage=false, includeNames=true)
@TupleConstructor(includes=['userKey', 'contentActionKey'])
@EqualsAndHashCode(includes=['userKey', 'contentActionKey'])
class NotificationId implements Serializable {
	@DynamoDBHashKey
	UserHashKey userKey

	@DynamoDBRangeKey
	ContentActionKey contentActionKey

	static NotificationId newId(String countryCode, Long userId, Long id, String action) {
		new NotificationId(new UserHashKey(countryCode, userId), new ContentActionKey(id, action))
	}

	static NotificationId newId(String countryCode, String globalUserId, Long id, String action) {
		new NotificationId(new UserHash<PERSON>ey(countryCode, globalUserId), new ContentActionKey(id, action))
	}

}
