package notification.pojos;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.TimeZone;

import org.socialsignin.spring.data.dynamodb.mapping.AbstractDynamoDBDateMarshaller;


public class CurrentDateTimeMarshaller extends AbstractDynamoDBDateMarshaller {
	
	/**
	 * Create a date formatter for UTC time in ISO 8601 format
	 */
	public CurrentDateTimeMarshaller() {
		super(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") {
        private static final long serialVersionUID = 1L;
        {
            setTimeZone(TimeZone.getTimeZone(ZoneId.of("UTC")));
        }
    });
	}
}
