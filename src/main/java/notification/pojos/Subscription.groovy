package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

import org.springframework.data.annotation.Id

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIgnore
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshalling
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable

@DynamoDBTable(tableName = "Subscription")
@DynamoDBDocument
@EqualsAndHashCode(includes=['id'])
@ToString(includeFields=true, includeNames=true, includePackage=false)
class Subscription {
	@Id
	private SubscriptionId id = new SubscriptionId()
	
	Subscription() {}
	
	Subscription(String countryCode, Long userId, Long contentId) {
		id = SubscriptionId.newId(countryCode, userId, contentId)
	}

	@DynamoDBIgnore
	SubscriptionId getId() {
		id
	}

	@DynamoDBHashKey(attributeName = "ContentHashKey")
	@DynamoDBMarshalling(marshallerClass=ContentHashKeyMarshaller.class)
	ContentHashKey getContentKey() {
		id.contentKey
	}
	
	void setContentKey(ContentHashKey contentKey) {
		id.contentKey = contentKey
	}
	

	@DynamoDBRangeKey(attributeName = "UserRangeKey")
	Long getUserId() {
		id.userId
	}
	
	void setUserId(Long userId) {
		id.userId = userId
	}
	
	@DynamoDBAttribute(attributeName = "CreateDate")
	@DynamoDBMarshalling(marshallerClass=CurrentDateTimeMarshaller.class)
	Date createDateTime = new Date()
}
