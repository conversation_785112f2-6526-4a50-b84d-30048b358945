package notification.pojos

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import groovy.transform.TupleConstructor

@ToString(includePackage=false, includeNames=true)
@TupleConstructor(includes=['contentKey', 'globalMemberId'])
@EqualsAndHashCode(includes=['contentKey', 'globalMemberId'])
class GlobalSubscriptionId implements Serializable {

	@DynamoDBHashKey
	ContentHashKey contentKey

	@DynamoDBRangeKey
	String globalMemberId

	static GlobalSubscriptionId newId(String countryCode, String globalMemberId, Long id) {
		new GlobalSubscriptionId(new ContentHashKey(countryCode, id), globalMemberId)
	}

}
