package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

@ToString(includePackage=false, includeNames=true, includeSuper=true)
@EqualsAndHashCode(includes=['countryCode', 'userId'])
class UserHash<PERSON>ey extends Hash<PERSON>ey implements Serializable {
	String userId

	UserHashKey(String countryCode, Long userId) {
		this.countryCode = countryCode
		this.userId = Long.toString(userId)
	}

	UserHashKey(String countryCode, String globalUserId) {
		this.countryCode = countryCode
		this.userId = globalUserId
	}

}
