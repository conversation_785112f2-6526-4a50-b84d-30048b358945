package notification.pojos;

import java.io.Serializable;

public enum NotificationTypeEnum implements Serializable {

    COMMENT, REPLY, REACT_ON_POST, REACT_ON_COMMENT, DIRECT_MESSAGE;


    public String toString(){
        switch(this){
            case COMMENT :
                return "comment";
            case REPLY :
                return "reply";
            case REACT_ON_POST :
                return "reactOnPost";
            case REACT_ON_COMMENT :
                return "reactOnComment";
            case DIRECT_MESSAGE :
                return "DirectMessage";
        }
        return null;
    }
}
