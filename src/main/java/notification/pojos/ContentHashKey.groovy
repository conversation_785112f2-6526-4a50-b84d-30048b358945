package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

@ToString(includePackage=false, includeNames=true, includeSuper=true)
@EqualsAndHashCode(includes=['countryCode', 'contentId'])
class ContentHashKey extends HashKey {
	Long contentId
	
	ContentHashKey(String countryCode, Long contentId) {
		this.countryCode = countryCode
		this.contentId = contentId
	}
}
