package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIgnore
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshalling
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.annotation.JsonUnwrapped

import java.time.Instant
import java.time.format.DateTimeFormatter

import org.springframework.data.annotation.Id

@DynamoDBTable(tableName = "Notification")
@DynamoDBDocument
@EqualsAndHashCode(includes=['id'])
@JsonInclude(Include.NON_NULL)
@ToString(includeFields=true, includeNames=true, includePackage=false)
class Notification {
	private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern('uMMdd')

	@Id
	@JsonIgnore
	private NotificationId id = new NotificationId()

	Notification() {}

	Notification(String countryCode, String globalUserId, Long contentId, String action, int lifetimeDays) {
		id = NotificationId.newId(countryCode, globalUserId, contentId, action)
		ttl = Instant.now().getEpochSecond() + lifetimeDays * 24 * 60 * 60
	}

	Notification(String countryCode, Long userId, Long contentId, String action, int lifetimeDays) {
		id = NotificationId.newId(countryCode, userId, contentId, action)
		ttl = Instant.now().getEpochSecond() + lifetimeDays * 24 * 60 * 60
	}

	@DynamoDBIgnore
	NotificationId getId() {
		id
	}

	@DynamoDBHashKey(attributeName = "UserHashKey")
	@DynamoDBMarshalling(marshallerClass=UserHashKeyMarshaller.class)
	@JsonIgnore
	UserHashKey getUserKey() {
		id?.userKey
	}

	void setUserKey(UserHashKey userKey) {
		id.userKey = userKey
	}


	@DynamoDBRangeKey(attributeName = "ContentRangeKey")
	@DynamoDBMarshalling(marshallerClass=ContentActionKeyMarshaller.class)
	@JsonUnwrapped
	ContentActionKey getContentActionKey() {
		id?.contentActionKey
	}

	void setContentActionKey(ContentActionKey contentActionKey) {
		id.contentActionKey = contentActionKey
	}

	@DynamoDBAttribute(attributeName = "PostId")
	Long postId

	@DynamoDBAttribute(attributeName = "ReplyId")
	Long replyId

	@DynamoDBAttribute(attributeName = "Snippet")
	String snippet

	@DynamoDBAttribute(attributeName = "GenericContent")
	@DynamoDBMarshalling(marshallerClass=GenericContentMarshaller.class)
	Object genericContent

	@DynamoDBIgnore
	Boolean isRead() {
		!count
	}

	@DynamoDBAttribute(attributeName = "Count")
	Integer count = 1

	@DynamoDBAttribute(attributeName = "CreateDate")
	@DynamoDBMarshalling(marshallerClass=CurrentDateTimeMarshaller.class)
	@JsonFormat(pattern='yyyy-MM-dd HH:mm:ss')
	Date createDateTime = new Date()

	//
	// time-to-live value for this record.
	// This is specified as a Epoch value in seconds.
	// Once the current time becomes greater than ttl, the record may be automatically deleted by dynamodb.
	//
	@DynamoDBAttribute(attributeName = "ttl")
	@JsonIgnore
	Long ttl

	/**
	 * Reset the notification's create date and time to live.
	 *
	 * @param lifetimeDays number of days notification is valid
	 */
	void reset(int lifetimeDays) {
		createDateTime = new Date()
		ttl = Instant.now().getEpochSecond() + lifetimeDays * 24 * 60 * 60
	}
}
