package notification.pojos

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import groovy.transform.TupleConstructor

@ToString(includePackage=false, includeNames=true)
@TupleConstructor(includes=['contentId', 'action'])
@EqualsAndHashCode(includes=['contentId', 'action'])
class ContentActionKey implements Serializable {
	Long contentId
	// TODO consider making this an enum ... enforcing strict set of values
	// action can be: comment, reply, like
	String action
}
