package notification.pojos

import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMarshaller

class ContentActionKeyMarshaller implements DynamoDBMarshaller<ContentActionKey> {

	@Override
	String marshall(ContentActionKey contentActionKey) {
		"$contentActionKey.contentId,$contentActionKey.action"
	}

	@Override
	ContentActionKey unmarshall(Class<ContentActionKey> clazz, String obj) {
		if (!obj) return null
		
		String[] parts = obj.split(',')
		new ContentActionKey(parts[0] as long, parts[1])
	}

}
