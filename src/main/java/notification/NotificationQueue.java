package notification;

/**
 * Created by emurphy on 7/11/18.
 */
public enum NotificationQueue
{
	ADD_SUBSCRIBER_QUEUE("addSubscriberQueue"),
	REMOVE_SUBSCRIBER_QUEUE("removeSubscriberQueue"),
	ADD_COMMUNITY_EVENT_QUEUE("addCommunityEventQueue"),
	UNDO_COMMUNITY_EVENT_QUEUE("undoCommunityEventQueue"),
	READ_COMMUNITY_EVENT_QUEUE("readCommunityEventQueue"),
	ADD_GENERIC_EVENT_QUEUE("addGenericEventQueue"),
	BAD_PAYLOAD_QUEUE("badPayloadQueue");
	
	public final String name;
	
	NotificationQueue(final String name)
	{
		this.name = name;
	}
}
