package notification.repositories;

import notification.pojos.ContentHashKey;
import notification.pojos.GlobalSubscription;
import notification.pojos.GlobalSubscriptionId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

public interface GlobalSubscriptionRepository extends PagingAndSortingRepository<GlobalSubscription, GlobalSubscriptionId> {
	Page<GlobalSubscription> findByContentKey(ContentHashKey contentKey, Pageable pageable);
}
