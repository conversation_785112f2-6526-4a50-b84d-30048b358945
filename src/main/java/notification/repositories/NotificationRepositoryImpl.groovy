package notification.repositories

import notification.pojos.Notification
import notification.pojos.UserHashKey
import notification.pojos.UserHashKeyMarshaller
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable

class NotificationRepositoryImpl implements NotificationRepositoryCustom {

	@Autowired
	private NotificationRepository notificationRepo
	
	private UserHashKeyMarshaller marshaller = new UserHashKeyMarshaller()
	
	private Pageable pageable = new PageRequest(0, 1000)
	
	@Override
	List<Notification> findByUserKeyHash(String userKeyHash) {

		Page<Notification> page = notificationRepo.findByUserKey(marshaller.unmarshall(UserHashKey.class, userKeyHash), pageable)

		def res = page.content.collect{it}.sort{-it.createDateTime.time}
		res.size() > 50 ? res[0..49] : res
	}
}
