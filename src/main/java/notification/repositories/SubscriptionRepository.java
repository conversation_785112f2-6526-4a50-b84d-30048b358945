package notification.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import notification.pojos.ContentHashKey;
import notification.pojos.Subscription;
import notification.pojos.SubscriptionId;

public interface SubscriptionRepository extends PagingAndSortingRepository<Subscription, SubscriptionId> {
	Page<Subscription> findByContentKey(ContentHashKey contentKey, Pageable pageable);
}
