package notification.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import notification.pojos.Notification;
import notification.pojos.NotificationId;
import notification.pojos.UserHashKey;

import java.util.List;

public interface NotificationRepository extends PagingAndSortingRepository<Notification, NotificationId>, NotificationRepositoryCustom {
	Page<Notification> findByUserKey(UserHashKey userKey, Pageable pageable);

	List<Notification> findByUserKey(UserHashKey userKey);
}
