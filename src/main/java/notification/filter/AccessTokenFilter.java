package notification.filter;

import notification.auth.AccessTokenRequestValidator;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Validate the access token in the authorization header
 * against the memberId in the uri.
 *
 * Created by emurphy on 10/24/17.
 */
@Component
@Order(Ordered.LOWEST_PRECEDENCE)
public class AccessTokenFilter extends OncePerRequestFilter
{
	private final String[] paths = {"/v2/user/**","/v3/user/**"};
	private final String userKeyParameter = "userkey";
	
	private AccessTokenRequestValidator accessTokenRequestValidator;
	
	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException
	{
		
		//
		// If this is an authenticated route, check authorization header agains member id
		//
		final Map<String, String> pathVariables = new HashMap<>();
		if(matchPath(paths, request.getRequestURI(), pathVariables))
		{
			final String authorizationHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
			if (null == authorizationHeader)
			{
				response.setStatus(HttpStatus.UNAUTHORIZED.value());
				return; // do not go onto next field in the chain;
			}
			
			//
			// The access token must match the member and site in the url.
			//
			final String userKey =
				(null != request.getParameter(userKeyParameter))
					? new String(new BASE64Decoder().decodeBuffer(request.getParameter(userKeyParameter)))
					: null;
			if (!isAccessTokenValid(authorizationHeader, userKey, request.getRequestURI().contains("/v3/")))
			{
				response.setStatus(HttpStatus.FORBIDDEN.value());
				return; // do not go onto next filter in the chain.
			}
		}
		
		//
		// go to next filter in the chain
		//
		filterChain.doFilter(request, response);
	}
	
	private String localeToSite(final String locale)
	{
		if((null == locale) || locale.isEmpty()) throw new IllegalArgumentException();
		switch(locale)
		{
			case "en_US": return "bcsite";
			default: return locale;
		}
	}
	
	/**
	 * Determine if the authorization header contains a valid access token for this member.
	 *
	 * @param authorizationHeader contents of the 'authorization' header from http request
	 * @param userKey site,siteUid
	 * @param verifyAudience     verifyAudience contains "notification-service
	 * @return true if access token is valid for member, false if not
	 */
	private boolean isAccessTokenValid(final String authorizationHeader, final String userKey, final boolean verifyAudience)
	{
		final String accessToken = authorizationHeader.startsWith("Bearer ") ? authorizationHeader.substring(7) : authorizationHeader;
		return accessTokenRequestValidator.isValid(accessToken, userKey, verifyAudience);
	}
	
	/**
	 * Find a matching path and pull out the path variable value
	 *
	 * @param uriTemplates uri templates like /foo/member/{memberId}/*, where
	 *                    {memberId} is a path variable for memberId and
	 *                    * matches anything at the end of the template.
	 * @param uri like /foo/member/123/content/bcsite/question/456
	 * @param pathVariables gets values of {variable} in the template
	 * @return true if a path matches the template, in which case pathVariables are updated.
	 */
	private boolean matchPath(final String[] uriTemplates, final String uri, Map<String, String> pathVariables)
	{
		for(String uriTemplate : uriTemplates)
		{
			if(matchPath(uriTemplate, uri, pathVariables))
			{
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Get the value of a pathVariable from a uri template.
	 * Variable is a templae path field surrounded by brackets,
	 * with the name of the variable inside.
	 * Like {memberId} will match memberId.
	 *
	 * @param uriTemplate uri template like /foo/member/{memberId}/*, where
	 *                    {memberId} is a path variable for memberId and
	 *                    * matches anything at the end of the template.
	 * @param uri like /foo/member/123/content/bcsite/question/456
	 * @param pathVariables gets values of {variable} in the template
	 * @return true if path matches the template, in which case pathVariables are updated.
	 */
	boolean matchPath(final String uriTemplate, final String uri, Map<String, String> pathVariables)
	{
		if(null == uriTemplate) throw new IllegalArgumentException();
		if(null == pathVariables) throw new IllegalArgumentException();
		if(null == uri) throw new IllegalArgumentException();
		
		final String[] uriTemplateFields = uriTemplate.split("/");
		final String[] uriFields = uri.split("/");
		
		final Map<String, String> variables = new HashMap<>();
		
		// remember if we should match the end of the template
		
		for (int i = 0; i < uriTemplateFields.length; i += 1)
		{
			final String uriTemplateField = uriTemplateFields[i];
			
			//
			// handle special case of wildcard at end of template
			//
			if((i == uriTemplateFields.length - 1) && "**".equals(uriTemplateFields[i]))
			{
				break;
			}
			
			if(i == uriFields.length )
			{
				return false;    // uri is too short; template does not match
			}
			
			if (uriTemplateField.startsWith("{") && uriTemplateField.endsWith("}"))
			{
				//
				// its a variable field, see if it is our variable
				// if it is, set the value
				// if it is not, just skip it
				//
				final String fieldName = uriTemplateField.substring(1, uriTemplateField.length() - 1);
				if((null != fieldName) && (!fieldName.isEmpty()))
				{
					// extract value from uri field
					variables.put(fieldName, uriFields[i]);
				}
			}
			else
			{
				//
				// it is a constant field; it MUST match the template
				//
				if(!"*".equals(uriTemplateField))
				{
					if ((!uriFields[i].equals(uriTemplateField)))
					{
						return false;    // template does not match
					}
				}
			}
		}
		
		//
		// add in whatever variables we found
		//
		pathVariables.putAll(variables);
		return true;
	}
	
	
	@Autowired
	public void setAccessTokenRequestValidator(AccessTokenRequestValidator accessTokenRequestValidator)
	{
		this.accessTokenRequestValidator = accessTokenRequestValidator;
	}
	
}

