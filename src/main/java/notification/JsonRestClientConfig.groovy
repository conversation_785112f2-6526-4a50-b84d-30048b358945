package notification

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.http.converter.FormHttpMessageConverter
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.web.client.RestTemplate

@Configuration
class JsonRestClientConfig {

    @Bean
    RestTemplate strictPooledRestTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory()
        requestFactory.readTimeout = 1000
        requestFactory.connectTimeout = 1000

        RestTemplate restTemplate = new RestTemplate(requestFactory)
        restTemplate.setMessageConverters(
                Arrays.asList(
                        new MappingJackson2HttpMessageConverter(),
                        new FormHttpMessageConverter()
                )
        )

        restTemplate
    }

    @Bean
    RestTemplate mediumPooledRestTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory()
        requestFactory.readTimeout = 2000
        requestFactory.connectTimeout = 2000

        RestTemplate restTemplate = new RestTemplate(requestFactory)
        restTemplate.setMessageConverters(
                Arrays.asList(
                        new MappingJackson2HttpMessageConverter(),
                        new FormHttpMessageConverter()
                )
        )

        restTemplate
    }
}
