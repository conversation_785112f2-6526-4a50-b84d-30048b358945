#
# create dbdeploy changelog table
#
# NOTE: run the create_local_bookmarks_db.sql first!!!
#
# to create changelog for local db; from the project root folder run this command;
#    mysql -uroot -pn3wb4by bookmarks < ./src/main/sql/dbdeploy/changeLog/createChangeLogTable.sql
#
# For sandbox, you will need to create the dbdeploy changelog using the dbdeploy user created by the DBA.
# To do this, connect to the RDS instance by running the mysql client from your project's root directory like this;
#     mysql --host={host} --port=3306 --user={user} --password={password}
# where {host} is the aws RDS connection string.  In this case; rds-01-sandbox.c8fbqlqitau5.us-east-1.rds.amazonaws.com'
# where {user} is the dbdeploy user name, in this case 'bookmarks-dp'
# where {password} is the dedeploy user's password, which is supplied to you by the DBA
#
# so the full command line (minus password) for the bookmarks sandbox is:
#     mysql --host=rds-01-sandbox.c8fbqlqitau5.us-east-1.rds.amazonaws.com --port=3306 --user='bookmarks-rw' --password='xxxxxxx'
#
# that will bring up the mysql client command prompt ('mysql>').  From there we want to source the sql script that creates the dbdeploy changelog table.
# If you have started the mysql client in your project's root folder, then you can run this command at the 'mysql>' prompt
#     source ./src/main/sql/dbdeploy/changeLog/createChangeLogTable.sql
#
# if you did not start mysql in the root folder of you project, you will need to specify the full path to the sql file.
#
#
CREATE TABLE bookmarks.changelog (
  change_number BIGINT NOT NULL,
  complete_dt TIMESTAMP NOT NULL,
  applied_by VARCHAR(100) NOT NULL,
  description VARCHAR(500) NOT NULL
);

ALTER TABLE bookmarks.changelog
  ADD CONSTRAINT Pkchangelog PRIMARY KEY (change_number);
