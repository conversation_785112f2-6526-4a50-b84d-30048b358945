#
# create the bookmarks table
# the natural key must be unique: contentSource, member_id, content_type, content_id
#
CREATE TABLE IF NOT EXISTS bookmark (
  id           BIGINT PRIMARY KEY AUTO_INCREMENT,
  member_id    VARCHAR(127) NOT NULL, # global auth service id
  locale       CHAR(5) NOT NULL,      # like en_US for bcsite/US community
  content_type VARCHAR(63) NOT NULL,  # platform-native content type
  content_id   VARCHAR(63) NOT NULL,  # platform-native content type

  # audit fields
  create_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  create_user VARCHAR(255) DEFAULT NULL,
  update_user VARCHAR(255) DEFAULT NULL
);

ALTER TABLE bookmark
    ADD CONSTRAINT NK_natural_key UNIQUE (member_id, locale, content_type, content_id);

