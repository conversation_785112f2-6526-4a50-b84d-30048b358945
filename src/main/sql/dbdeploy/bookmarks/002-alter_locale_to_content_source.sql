#
# remove contentSource column and change to audience column
#
ALTER TABLE bookmark
  ADD COLUMN content_source VARCHAR(16) NOT NULL AFTER locale;
UPDATE bookmark
  SET content_source = 'bcsite'
  WHERE locale = 'en_US';

#
# remove the contentSource column
#
ALTER TABLE bookmark
  DROP INDEX NK_natural_key;
ALTER TABLE bookmark
  DROP COLUMN locale;

#
# add constraint for the new natural key - allows fast lookup by member_id
#
ALTER TABLE bookmark
    ADD CONSTRAINT NK_natural_key UNIQUE (member_id, content_source, content_type, content_id);

#
# create index for fast select of content type source, type and id (regardless of member)
#
CREATE INDEX IDX_content ON bookmark (content_source, content_type, content_id);
