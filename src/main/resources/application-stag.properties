
#
# datastore credentials are setup in secure/secure.properties
#
#spring.datasource.url=*********************************************************************************************************************************************
#spring.datasource.username = bookmarks-rw
#spring.datasource.password = ????????

#
# local user server port 8080
#
server.port=8080

# secret to secure internal api calls
bookmarksServiceSecret=stag-bookmark-secret

# Auth server properties - authServiceValidationKey retrieved from https://stag-auth-service-internal.babycenter.com/token/validation-key
authServiceBaseUrl=https://stag-auth-service-internal.babycenter.com
authServiceIssuer=stag-auth.babycenter.com
authServiceValidationKey=-----BEGIN PUBLIC KEY-----\n\
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz3+XGbBu/CcVFAfEyrVu\n\
  O3/WzcjR/EH3if5j9ZqMLmQZ/uOiRYaXX7emuyFnFTK9vwypLnTwG2AAtYGiJKVf\n\
  ljNiDuTEjdpoaI5Ip3cevoePar5MKl6QA2RyLgZQ4E/k3506QKqPvS8fDuJhQMS1\n\
  rxRGd+QFDrP0Mk8Q5FVj9IWETPYdcMzvo0KVFvNo02e9HQukI9c3elzBTNpXH7S+\n\
  ZMaQq9yXWn9UZ/iFegDPF/6lT/sHEmFwHEDMe63EzwZ1owRvW2PqcqDS10Gp7MS5\n\
  WHrYC0VVynkaChGTDLnZvtIp3uecEz84Qid/x/j+OFRbVia5pTtM1QZVXyeUZZ46\n\
  5wIDAQAB\n\
  -----END PUBLIC KEY-----

# Profile Event Service
profileEventService.usMemberEvents.awsRegion=us-east-1
profileEventService.usMemberEvents.topicArn=arn:aws:sns:us-east-1:762359486493:bc-us-member-events-stag
profileEventService.subscriptionEvents.awsRegion=us-west-2
profileEventService.subscriptionEvents.topicArn=arn:aws:sns:us-west-2:762359486493:bc-subscription-events-stag
#profileEventService.accessKeyId=
#profileEventService.secretAccessKey=

#
# graphite config
#
babycenter.statsd.enabled = true
babycenter.statsd.host = statsd-dev-app.babycenter.com
babycenter.statsd.port = 8125
babycenter.statsd.stat.prefix: stag.bookmarkservice

# enable the health management to start using the Serving pattern for orange/black configuration
management.health.serving.isEnabled=false
# default value is serving.txt if nothing specified
management.health.serving.fileLocation=serving.txt
# enable the health management for jvm (e.g. number of threads ratio, memory threshold)
management.health.jvm.isEnabled=false
# used memory vs available memory ratio
management.health.jvm.memory.threshold=95
