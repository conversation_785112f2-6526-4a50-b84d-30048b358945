
# DataSource settings: set here your own configurations for the database
spring.datasource.url = ***********************************************************************************
spring.datasource.username = bookmarks-rw
spring.datasource.password = @rtr43!fu

#
# local user server port 8080
#
server.port=8080

# secret to secure internal api calls
bookmarksServiceSecret=qa secret

# Auth server properties
authServiceBaseUrl=https://qa-auth-internal.babycenter.com
authServiceIssuer=qa-auth.babycenter.com

# Profile Event Service
profileEventService.usMemberEvents.awsRegion=us-east-1
profileEventService.usMemberEvents.topicArn=foo
profileEventService.subscriptionEvents.awsRegion=us-west-2
profileEventService.subscriptionEvents.topicArn=arn:aws:sns:us-east-1:802112674144:bc-subscription-events-dev
profileEventService.accessKeyId=bar
profileEventService.secretAccessKey=spam

#
# graphite config
#
babycenter.statsd.enabled = true
babycenter.statsd.host = statsd-dev-app.babycenter.com
babycenter.statsd.port = 8125
babycenter.statsd.stat.prefix: sandbox.bookmarkservice

# enable the health management to start using the Serving pattern for orange/black configuration
management.health.serving.isEnabled=false
# default value is serving.txt if nothing specified
management.health.serving.fileLocation=serving.txt
# enable the health management for jvm (e.g. number of threads ratio, memory threshold)
management.health.jvm.isEnabled=false
# used memory vs available memory ratio
management.health.jvm.memory.threshold=95

# logging
debug = true