# DataSource settings: set here your own configurations for the database
# connection. In this example we have "netgloo_blog" as database name and
# "root" as username and password.
# CREATE SCHEMA `bookmarks` DEFAULT CHARACTER SET utf8 ;
spring.datasource.url = *************************************
spring.datasource.username = root
spring.datasource.password = n3wb4by

# Show or not log for each sql query
spring.jpa.show-sql = true

#
# local user server port 8083 to avoid BcSite
#
server.port=8083

# secret to secure internal api calls
bookmarksServiceSecret=local-secret

# Auth server properties
#authServiceBaseUrl=http://localhost:9292
#authServiceIssuer=auth.babycenter.com
#authServiceValidationKey=null
# Auth server properties
authServiceBaseUrl=https://qa-auth-service-internal.babycenter.com
authServiceIssuer=qa-auth.babycenter.com
authServiceValidationKey=-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n\
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd\n\
  oLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw\n\
  lf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux\n\
  xw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl\n\
  9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq\n\
  yb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe\n\
  +QIDAQAB\n\
  -----END PUBLIC KEY-----

# Profile Event Service
profileEventService.usMemberEvents.awsRegion=us-east-1
profileEventService.usMemberEvents.topicArn=foo
profileEventService.subscriptionEvents.awsRegion=us-east-1
profileEventService.subscriptionEvents.topicArn=arn:aws:sns:us-east-1:802112674144:bc-subscription-events-dev
profileEventService.accessKeyId=********************
profileEventService.secretAccessKey=7XrJanapsED774BzJesZ/YWyE6CDVNOBVIz0o9os
profileEventService.sessionToken=FwoGZXIvYXdzEDAaDGiwg5Z6qyUn6lo7JyLyAQAE5j48AL0OpZf6JYbtmhZ6oqh5X7yS8ARir5ZIIU6aQxGv65v0W9Fg22FMvG9hrkYWWrSoZjf/eP1mv+k4+Fas33uL3IV+zNAlah9lU78a/f6U+WKPQ1lMVcmnEdev9LbGlFoIPrsM9Yitvf8rcasWgXOjRiF3Kktos4xeAcUTjduSte+yX2zRiYxZie/+1RZmYz2lRQMcVzU8lbFpUydXgVZNaOQarzTAUizT3Fqb8vpubCuwmPsPGCoTM/6CMArj1LBZiAmyJzAxRmaRv9O2WecE22OhCAiKeizhUq49ZrJFWR+aLfBDchPa2Nh2HiK8KOW16oQGMjJ5SEBldW+NmtlOzTR2ZtW9hnXGd9icVpJBlk3bt4zPmh4SKW/XXlzto6hH5El4K0jrmw==

#
# graphite config
#
babycenter.statsd.enabled = true
babycenter.statsd.host = statsd-dev-app.babycenter.com
babycenter.statsd.port = 8125
babycenter.statsd.stat.prefix: local.bookmarkservice

management.port = 8383

# enable the health management to start using the Serving pattern for orange/black configuration
management.health.serving.isEnabled=true
# default value is serving.txt if nothing specified
management.health.serving.fileLocation=serving.txt
# enable the health management for jvm (e.g. number of threads ratio, memory threshold)
management.health.jvm.isEnabled=true
# used memory vs available memory ratio
management.health.jvm.memory.threshold=95

# logging
debug = true
