spring:
  profiles:
    active: sandbox-ext
amazon:
  aws:
    accesskey: ********************
    secretkey: a7n8gcTvHQm5ls5UAlnJF4CBWN53A82a8w3yM45/
    region: us-west-2
  dynamodb:
    endpoint: dynamodb.us-west-2.amazonaws.com
  sqs:
    credentials:
      accesskey:
      secretkey:
    region: us-east-1
    queues:
      bc-us-community-events: bc-community-notification-svc-sandbox-ext
      bc-us-community-events-dead-letter: bc-community-notification-svc-failure-sandbox-ext
      bc-subscription-notification-svc: bc-subscription-notification-svc-dev
      bc-subscription-notification-svc-dead-letter: bc-subscription-notification-svc-failure-dev
# notification lifetime in units of days
notification:
  lifetime: 1