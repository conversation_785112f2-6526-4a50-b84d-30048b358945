spring:
  profiles:
    active: qa

amazon:
  aws:
    #    region: us-east-1
    session-token-key:
  dynamodb:
  #   endpoint: dynamodb.us-west-2.amazonaws.com
  sqs:
    credentials:
      #      accesskey:
      #      secretkey:
      session-token-key:
    region: us-east-1
    queues:
      bc-us-community-events: bc-community-notification-svc-qa
      bc-us-community-events-dead-letter: bc-community-notification-svc-failure-qa
      bc-subscription-notification-svc: bc-subscription-notification-svc-qa
      bc-subscription-notification-svc-dead-letter: bc-subscription-notification-svc-failure-qa

communityMoltres:
  us:
    baseUrl: https://qa-dev-service.babycenter.com/internal
  gb:
    baseUrl: https://qa-dev-service.babycentre.co.uk/internal
  au:
    baseUrl: https://qa-dev-service.babycenter.com.au/internal
  br:
    baseUrl: https://qa-dev-service-brasil.babycenter.com/internal
  ca:
    baseUrl: https://qa-dev-service.babycenter.ca/internal
  de:
    baseUrl: https://qa-dev-service.babycenter.de/internal
  es:
    baseUrl: https://qa-dev-service-espanol.babycenter.com/internal
  in:
    baseUrl: https://qa-dev-service.babycenter.in/internal

# # notification lifetime in units of days
notification:
  lifetime: 7

# Auth server properties
authServiceBaseUrl: https://qa-auth-service-internal.babycenter.com
authServiceIssuer: qa-auth.babycenter.com
authServiceValidationKey: |
  -----BEGIN PUBLIC KEY-----
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd
  oLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw
  lf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux
  xw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl
  9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq
  yb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe
  +QIDAQAB
  -----END PUBLIC KEY-----

logging:
  level:
    notification: DEBUG
