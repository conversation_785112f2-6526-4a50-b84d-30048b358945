spring:
  profiles:
    active: sandbox
amazon:
  aws:
    region: us-west-2
  dynamodb:
    endpoint: dynamodb.us-west-2.amazonaws.com
  sqs:
    credentials:
      accesskey:
      secretkey:
    region: us-west-2
    queues:
      bc-us-community-events: bc-community-notification-svc-sandbox
      bc-us-community-events-dead-letter: bc-community-notification-svc-failure-sandbox
      bc-subscription-notification-svc: bc-subscription-notification-svc-dev
      bc-subscription-notification-svc-dead-letter: bc-subscription-notification-svc-failure-dev
# notification lifetime in units of days
notification:
  lifetime: 1

# Auth server properties
authServiceBaseUrl: https://qa-auth-service-internal.babycenter.com
authServiceIssuer: qa-auth.babycenter.com
authServiceValidationKey: |
  -----BEGIN PUBLIC KEY-----
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd
  oLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw
  lf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux
  xw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl
  9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq
  yb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe
  +QIDAQAB
  -----END PUBLIC KEY-----