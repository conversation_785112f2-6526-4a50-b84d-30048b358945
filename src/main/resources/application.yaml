spring:
  profiles:
    active: dev
  application:
    name: notification
amazon:
  aws:
    accesskey:
    secretkey:
    session-token-key:
    region: local
  dynamodb:
    endpoint: http://localhost:8000
  sqs:
    credentials:
      #      accesskey:
      #      secretkey:
      session-token-key:
    region: us-west-2
    queues:
      bc-us-community-events: bc-community-notification-svc
      bc-us-community-events-dead-letter: bc-community-notification-svc-failure
      bc-subscription-notification-svc: bc-subscription-notification-svc
      bc-subscription-notification-svc-dead-letter: bc-subscription-notification-svc-failure

communityMoltres:
  us:
    baseUrl: https://internal-service.babycenter.com/internal
  gb:
    baseUrl: https://internal-service.babycentre.co.uk/internal
  au:
    baseUrl: https://internal-service.babycenter.com.au/internal
  br:
    baseUrl: https://internal-service-brasil.babycenter.com/internal
  ca:
    baseUrl: https://internal-service.babycenter.ca/internal
  de:
    baseUrl: https://internal-service.babycenter.de/internal
  es:
    baseUrl: https://internal-service-espanol.babycenter.com/internal
  in:
    baseUrl: https://internal-service.babycenter.in/internal

# notification lifetime in units of days
notification:
  lifetime: 7

# enable ws cors filter
ws:
  cors:
    enabled: true

# Auth server properties
authServiceBaseUrl: https://auth-service-internal.babycenter.com
authServiceIssuer: auth.babycenter.com
authServiceValidationKey: |
  -----BEGIN PUBLIC KEY-----
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwCVy9FvE7s9AHmBKr6yu
  o3/6yr0zmwZAZbaLPtQ+6U2mAULOn1rmaiq8Xeu4dmyRTuYe+JfEE3WvRmGYVNzx
  h+4kELihfZiSRzWs+B4fa3d2YylazzQsY6avELwSMeu9YQnfrVFk6ByaUFZzmYPg
  UdTp8d4R9q+aTIDBvDWGb34bdU7ySRrnOGDrxepLGzmrDeB8D+BmSXtqc8qMwIZP
  mWwtcJh1IVaMuxqO3E3YrbXg+75NIuJDU05faf+G+uhgw434rWSQLSL/D6ui5SFB
  Ug3bxQabzLoXfr1Zify5N1e/H4M5A3Mgr5NQQOORNhkp2vVGX8Sp6u5T4hSaBLk8
  wQIDAQAB
  -----END PUBLIC KEY-----
