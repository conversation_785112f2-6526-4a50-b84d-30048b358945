#
# install and run dynamodb locally and create a run config to use the dev profile
#
# 1. install dynamodb local: http://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.html
#    - download from US West (Oregon) Region, so it matches config below
# 2. run dynamodb local from install folder: java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -sharedDb
#    - you can interact with the database using http://localhost:8000/shell/ or use a gui project like https://github.com/aaronshaf/dynamodb-admin
# 3. Create the following tables in dynamodb
#    - table name = MemberActivityStatus, partition key = UserHashKey String
#    - table name = Notification, partition key = UserHashKey String, primary sort key = ContentRangeKey String
#    - table name = Notification_recover, partition key = UserHashKey String, primary sort key = ContentRangeKey String
#    - table name = Subscription, partition key = ContentHashKey String, primary sort key = UserRangeKey Number
# 4. in intellij, create a run configuration to run using dev profile;
#    - create a new run configuration for a spring boot app
#    - main class is: notification.NotificationServicesApplication
#    - VM options are: -ea
#    - working directory: $MODULE_DIR$
#    - use class path of module: notification
#    - JRE: default (should be 1.8)
#    - active profile is: dev
# 5. run all unit tests to show everything works.
# 6. Start Intl2 local (make sure it is pointing to local, see configuration in intl.properties) and create a comment in any community post.
#    You should see a Subscription added in dynamodb.
#
# subsequently, just start dynamodb, run notifications using dev profile configuration
#
spring:
  profiles:
    active: dev

amazon:
  aws:
    accesskey:
    secretkey:
    session-token-key:
    region: us-east-1
  dynamodb:
    endpoint: http://localhost:8000
  sqs:
    credentials:
      accesskey:
      secretkey:
      session-token-key:

    region: us-east-1
    queues:
      bc-us-community-events: bc-community-notification-svc-dev
      bc-us-community-events-dead-letter: bc-community-notification-svc-failure-dev
      bc-subscription-notification-svc: bc-subscription-notification-svc-dev
      bc-subscription-notification-svc-dead-letter: bc-subscription-notification-svc-failure-dev

communityMoltres:
  us:
    baseUrl: https://qa-dev-service.babycenter.com/internal
  gb:
    baseUrl: https://qa-dev-service.babycentre.co.uk/internal
  au:
    baseUrl: https://qa-dev-service.babycenter.com.au/internal
  br:
    baseUrl: https://qa-dev-service-brasil.babycenter.com/internal
  ca:
    baseUrl: https://qa-dev-service.babycenter.ca/internal
  de:
    baseUrl: https://qa-dev-service.babycenter.de/internal
  es:
    baseUrl: https://qa-dev-service-espanol.babycenter.com/internal
  in:
    baseUrl: https://qa-dev-service.babycenter.in/internal

server:
  port: 7001
logging:
  level:
    com:
      amazonaws: DEBUG
    notification: DEBUG
# notification lifetime in units of days
notification:
  lifetime: 1

# Auth server properties
#authServiceBaseUrl: http://localhost:9292
#authServiceIssuer: dev-auth.babycenter.com
#authServiceValidationKey: "null"
authServiceBaseUrl: https://qa-auth-service-internal.babycenter.com
authServiceIssuer: qa-auth.babycenter.com
authServiceValidationKey: |
  -----BEGIN PUBLIC KEY-----
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd
  oLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw
  lf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux
  xw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl
  9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq
  yb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe
  +QIDAQAB
  -----END PUBLIC KEY-----
