spring:
  profiles:
    active: stag

amazon:
  aws:
    region: us-west-2
    session-token-key:
  dynamodb:
    endpoint: dynamodb.us-west-2.amazonaws.com
  sqs:
    credentials:
      #      accesskey:
      #      secretkey:
      session-token-key:
    region: us-west-2
    queues:
      bc-us-community-events: bc-community-notification-svc-stag
      bc-us-community-events-dead-letter: bc-community-notification-svc-failure-stag
      bc-subscription-notification-svc: bc-subscription-notification-svc-stag
      bc-subscription-notification-svc-dead-letter: bc-subscription-notification-svc-failure-stag

communityMoltres:
  us:
    baseUrl: https://internal-stag-service.babycenter.com/internal
  gb:
    baseUrl: https://internal-stag-service.babycentre.co.uk/internal
  au:
    baseUrl: https://internal-stag-service.babycenter.com.au/internal
  br:
    baseUrl: https://internal-stag-service-brasil.babycenter.com/internal
  ca:
    baseUrl: https://internal-stag-service.babycenter.ca/internal
  de:
    baseUrl: https://internal-stag-service.babycenter.de/internal
  es:
    baseUrl: https://internal-stag-service-espanol.babycenter.com/internal
  in:
    baseUrl: https://internal-stag-service.babycenter.in/internal

# # notification lifetime in units of days
notification:
  lifetime: 7

# Auth server properties
authServiceBaseUrl: https://stag-auth-service-internal.babycenter.com
authServiceIssuer: stag-auth.babycenter.com
authServiceValidationKey: |
  -----BEGIN PUBLIC KEY-----
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz3+XGbBu/CcVFAfEyrVu
  O3/WzcjR/EH3if5j9ZqMLmQZ/uOiRYaXX7emuyFnFTK9vwypLnTwG2AAtYGiJKVf
  ljNiDuTEjdpoaI5Ip3cevoePar5MKl6QA2RyLgZQ4E/k3506QKqPvS8fDuJhQMS1
  rxRGd+QFDrP0Mk8Q5FVj9IWETPYdcMzvo0KVFvNo02e9HQukI9c3elzBTNpXH7S+
  ZMaQq9yXWn9UZ/iFegDPF/6lT/sHEmFwHEDMe63EzwZ1owRvW2PqcqDS10Gp7MS5
  WHrYC0VVynkaChGTDLnZvtIp3uecEz84Qid/x/j+OFRbVia5pTtM1QZVXyeUZZ46
  5wIDAQAB
  -----END PUBLIC KEY-----
