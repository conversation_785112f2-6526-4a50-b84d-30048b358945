= Babycenter Notification Web Services API Guide
<PERSON>;
:doctype: book
:icons: font
:source-highlighter: highlightjs
:toc: left
:toclevels: 4
:sectlinks:

[[overview]]
= Overview

[[overview-http-verbs]]
== HTTP verbs

RESTful notes tries to adhere as closely as possible to standard HTTP and REST conventions in its
use of HTTP verbs.

|===
| Verb | Usage

| `GET`
| Used to retrieve a resource

| `POST`
| Used to create a new resource

| `PATCH`
| Used to update an existing resource, including partial updates

| `DELETE`
| Used to delete an existing resource
|===

[[overview-http-status-codes]]
== HTTP status codes

RESTful notes tries to adhere as closely as possible to standard HTTP and REST conventions in its
use of HTTP status codes.

|===
| Status code | Usage

| `200 OK`
| The request completed successfully

| `201 Created`
| A new resource has been created successfully. The resource's URI is available from the response's
`Location` header

| `204 No Content`
| An update to an existing resource has been applied successfully

| `400 Bad Request`
| The request was malformed. The response body will include an error providing further information

| `404 Not Found`
| The requested resource did not exist
|===

[[resources]]
= Resources

[[resources-notes]]
== notifications

The notification resources is used to list all notifications for a specific user by the encoded userkey


[[resources-notifications-list]]
=== Listing notifications

A `GET` request will list all of the cards.

==== Example request

include::{snippets}/notifications/curl-request.adoc[]

==== Example response

include::{snippets}/notifications/http-response.adoc[]

==== Response structure

include::{snippets}/notifications/response-fields.adoc[]

[[resources-acknowledge]]
=== Acknowledge notifications, reset count

A `GET` request will just reset the notification count for a specific user

==== Example request

include::{snippets}/acknowledge/curl-request.adoc[]

==== Example response

include::{snippets}/acknowledge/http-response.adoc[]

==== Response structure

include::{snippets}/acknowledge/response-fields.adoc[]