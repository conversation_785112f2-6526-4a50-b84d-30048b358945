= Babycenter Notification Web Services Getting Started Guide
<PERSON>;
:doctype: book
:icons: font
:source-highlighter: highlightjs
:toc: left
:toclevels: 4
:sectlinks:

[introduction]
= Introduction

Notification Services is a RESTful web service project for Babycenter onsite notifications.

[getting-started]
= Getting started



[getting-started-running-the-service]
== Running the service
Babycenter Notification Services is written using http://projects.spring.io/spring-boot[Spring Boot] which
makes it easy to get it up and running so that you can start exploring the REST API.

The first step is to clone the Git repository:

[source,bash]
----
$ git clone https://<EMAIL>/scm/not/master.git
----

Once the clone is complete, you're ready to get the service up and running:

[source,bash]
----
$ cd {project_workspace}/notification-services
$ mvn clean package
$ java -jar target/*.jar
----

You can check that the service is up and running by executing a simple request using
cURL:

include::{snippets}/home/<USER>

This request should yield the following response

include::{snippets}/home/<USER>
