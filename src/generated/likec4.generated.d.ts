/* prettier-ignore-start */
/* eslint-disable */

/******************************************************************************
 * This file was generated
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/

import type { PropsWithChildren } from 'react'
import type { JSX } from 'react/jsx-runtime'
import type {
  DiagramView,
  LikeC4Model as GenericLikeC4Model,
} from 'likec4/model'
import type {
  LikeC4ViewProps as GenericLikeC4ViewProps,
  ReactLikeC4Props as GenericReactLikeC4Props
} from 'likec4/react'

type LikeC4ElementId =
  | "adminTools"
  | "akismet"
  | "bcAuth"
  | "bcBookmarkNotification"
  | "bcSns"
  | "bcSqs"
  | "bcWeb"
  | "bcsite"
  | "cloudWatch"
  | "dags"
  | "deToolchain"
  | "edhFtp"
  | "editor"
  | "endUser"
  | "fedGQL"
  | "ion"
  | "marketer"
  | "mobile"
  | "moderator"
  | "perspective"
  | "prediktor"
  | "push"
  | "registry"
  | "retailers"
  | "sailthru"
  | "serviceLambdas"
  | "snowplow"
  | "thinkific"
  | "thirdParty"
  | "thirdPartyLeadApis"
  | "unknown"
  | "wteAdmin"
  | "wteCommunityApi"
  | "wteLeadSweeps"
  | "wteSns"
  | "wteStandaloneCampaign"
  | "wteUserApi"
  | "wteUserLeadgen"
  | "wteWeb"
  | "adminTools.dealsSheet"
  | "adminTools.dealsTool"
  | "adminTools.productTool"
  | "bcAuth.bcAuthServer"
  | "bcBookmarkNotification.bookmarkDb"
  | "bcBookmarkNotification.bookmarkEvents"
  | "bcBookmarkNotification.bookmarkNotificationApi"
  | "bcBookmarkNotification.communityEventsProcessor"
  | "bcBookmarkNotification.communityEventsQueue"
  | "bcBookmarkNotification.notificationDb"
  | "bcBookmarkNotification.profileEventService"
  | "bcBookmarkNotification.subscriptionDb"
  | "bcBookmarkNotification.subscriptionEventsProcessor"
  | "bcBookmarkNotification.subscriptionEventsQueue"
  | "bcSns.bcIntlMemberEvents"
  | "bcSns.bcUsMemberEvents"
  | "bcSqs.bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut"
  | "bcSqs.bc-us-sailthru-member-data"
  | "bcSqs.bc-us-sailthru-transactional"
  | "bcSqs.wte-registry-delete-baby-prod"
  | "bcWeb.bcAkamaiPurgeTopic"
  | "bcWeb.bcCommunityEvents"
  | "bcWeb.bcGql"
  | "bcWeb.bcGqlCommunityDb"
  | "bcWeb.bcGqlExpertDb"
  | "bcWeb.bcGqlRedis"
  | "bcWeb.bcImageService"
  | "bcWeb.bcReactApp"
  | "bcWeb.bcReactServer"
  | "bcsite.bcCmsSync"
  | "bcsite.bcsiteServer"
  | "cloudWatch.memberEventsDashboard"
  | "dags.bc_community_delete_sqs"
  | "dags.bc-community-events-delete-ppde"
  | "dags.dagCMSReplicator"
  | "dags.dagProductProcessor"
  | "dags.dagRegistryProductClassifier"
  | "dags.dagScribbleLido"
  | "dags.upsert_roundup_page_products"
  | "dags.upsert_roundup_products"
  | "deToolchain.afcCMS"
  | "deToolchain.classifier"
  | "deToolchain.genai-community-insights-post-tagging-v2"
  | "deToolchain.redshift"
  | "deToolchain.registry-update-from-retailer"
  | "deToolchain.registryProductUpdates"
  | "deToolchain.registryUpdateSQS"
  | "deToolchain.targetRegistryAPI"
  | "edhFtp.ftpServer"
  | "fedGQL.afcServiceDB"
  | "fedGQL.afcSubgraph"
  | "fedGQL.dailyDeals"
  | "fedGQL.dailyDealsDB"
  | "fedGQL.gateway"
  | "fedGQL.intAfcSubgraph"
  | "fedGQL.intDailyDeals"
  | "fedGQL.intGateway"
  | "fedGQL.intProductCatalog"
  | "fedGQL.productCatalog"
  | "fedGQL.productCatalogDB"
  | "ion.ionAuthoringTool"
  | "ion.ionDataRepository"
  | "ion.ionHostedSite"
  | "ion.ionIntegrations"
  | "ion.ionScriplets"
  | "mobile.bcAndroid"
  | "mobile.bcBabynamesApp"
  | "mobile.bciOS"
  | "mobile.wteAndroid"
  | "mobile.wteiOS"
  | "prediktor.abuse_classifier_en_v1"
  | "prediktor.au_community_expert_content_v1"
  | "prediktor.babynamer"
  | "prediktor.babynamer_bcuk"
  | "prediktor.babynamer_bcus"
  | "prediktor.bc_app_content_recommender_v1"
  | "prediktor.bc_au_content_recommender"
  | "prediktor.bc_br_content_recommender"
  | "prediktor.bc_ca_content_recommender"
  | "prediktor.bc_de_content_recommender"
  | "prediktor.bc_es_content_recommender"
  | "prediktor.bc_uk_content_recommender"
  | "prediktor.br_community_expert_content_v1"
  | "prediktor.ca_community_expert_content_v1"
  | "prediktor.ca_stage_prediction_v1"
  | "prediktor.de_community_expert_content_v1"
  | "prediktor.es_community_expert_content_v1"
  | "prediktor.group_rec_v2"
  | "prediktor.page_rec_page_v1"
  | "prediktor.pagerec_v2"
  | "prediktor.related_posts_uk_v1"
  | "prediktor.related_posts_us_v1"
  | "prediktor.spam_de_v1"
  | "prediktor.spam_en_v1"
  | "prediktor.spam_es_v1"
  | "prediktor.spam_pt_v2"
  | "prediktor.subtopic_content_order_v1"
  | "prediktor.uk_community_expert_content_v1"
  | "prediktor.uk_pagerec_v2"
  | "prediktor.uk_stage_prediction_v1"
  | "prediktor.us_cohorts_v3"
  | "prediktor.us_stage_prediction_v1"
  | "prediktor.wte_page_rec_page_v1"
  | "push.cms"
  | "push.cmsDB"
  | "push.contentAPI"
  | "registry.eventsSnsArn"
  | "registry.registryDDB"
  | "registry.registryRDS"
  | "registry.registryRedis"
  | "registry.registryRefreshSns"
  | "registry.registryRetailerIntegrationEventsSns"
  | "registry.registryService"
  | "sailthru.sailthruApi"
  | "serviceLambdas.bc-check-content-toxicity-perspective-api-queue"
  | "serviceLambdas.bc-lambda-abuse-report"
  | "serviceLambdas.bc-lambda-abuse-report-BcAbuseReportQueue"
  | "serviceLambdas.bc-lambda-check-content-toxicity"
  | "serviceLambdas.bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt"
  | "serviceLambdas.bc-loyaltyRewards-lambda"
  | "serviceLambdas.bc-member-events-monitor"
  | "serviceLambdas.bc-parsed-from-toxicity-perspective-api"
  | "serviceLambdas.bc-rewardsReader-lambda"
  | "serviceLambdas.bc-update-thinkific-ts"
  | "serviceLambdas.bc-us-memberprofile_lambda"
  | "serviceLambdas.sailthru-transactional-email"
  | "snowplow.DAG"
  | "snowplow.collector"
  | "snowplow.enrichedKinesis"
  | "snowplow.enrichmentContainer"
  | "snowplow.lambda"
  | "snowplow.rawKinesis"
  | "snowplow.s3"
  | "thinkific.coursesSqs"
  | "thinkific.thinkificApi"
  | "thinkific.thinkificApiGw"
  | "thinkific.thinkificSite"
  | "thirdParty.localytics"
  | "thirdParty.rewardsVendor"
  | "thirdParty.sailthru"
  | "thirdPartyLeadApis.aeroflowLeadsApi"
  | "thirdPartyLeadApis.cervidilLeadsApi"
  | "thirdPartyLeadApis.huggiesLeadsApi"
  | "wteAdmin.violationQueue"
  | "wteAdmin.wteAdminUI"
  | "wteCommunityApi.communityApi"
  | "wteCommunityApi.wteCommunityLambda"
  | "wteLeadSweeps.wteLeadLogMysqlDb"
  | "wteLeadSweeps.wteLeadSweepsAeroflowSqs"
  | "wteLeadSweeps.wteLeadSweepsCervidilSqs"
  | "wteLeadSweeps.wteLeadSweepsHuggiesSqs"
  | "wteLeadSweeps.wteLeadSweepsSubmitClientAeroflow"
  | "wteLeadSweeps.wteLeadSweepsSubmitClientCervidil"
  | "wteLeadSweeps.wteLeadSweepsSubmitClientHuggiesUsa"
  | "wteSns.wteLeadSweeps"
  | "wteSns.wteUserEvents"
  | "wteStandaloneCampaign.wteStandaloneCampaignApi"
  | "wteUserApi.wteUserDDB"
  | "wteUserLeadgen.wteUserLeadgen"
  | "wteUserLeadgen.wteUserLeadgenSqs"
  | "wteWeb.community"
  | "wteWeb.wteWebServer"
  | "bcSns.bcUsMemberEvents.babyChange"
  | "bcSns.bcUsMemberEvents.babyDelete"
  | "bcSns.bcUsMemberEvents.communityComment"
  | "bcSns.bcUsMemberEvents.directMessage"
  | "bcSns.bcUsMemberEvents.emailChange"
  | "bcSns.bcUsMemberEvents.emailConfirmed"
  | "bcSns.bcUsMemberEvents.groupInvite"
  | "bcSns.bcUsMemberEvents.groupMembership"
  | "bcSns.bcUsMemberEvents.memberChange"
  | "bcSns.bcUsMemberEvents.memberDelete"
  | "bcSns.bcUsMemberEvents.memberLogin"
  | "bcSns.bcUsMemberEvents.newAnswer"
  | "bcSns.bcUsMemberEvents.newBaby"
  | "bcSns.bcUsMemberEvents.newMember"
  | "bcSns.bcUsMemberEvents.newQuestion"
  | "bcSns.bcUsMemberEvents.newScreenName"
  | "bcSns.bcUsMemberEvents.oneOffEmail"
  | "bcSns.bcUsMemberEvents.passANote"
  | "bcSns.bcUsMemberEvents.passwordReset"
  | "bcSns.bcUsMemberEvents.postSubscription"
  | "bcSns.bcUsMemberEvents.subscriptionChange"
  | "bcWeb.bcCommunityEvents.commentCreated"
  | "bcWeb.bcCommunityEvents.commentEdited"
  | "bcWeb.bcCommunityEvents.contentDeleted"
  | "bcWeb.bcCommunityEvents.postCreated"
  | "bcWeb.bcCommunityEvents.postEdited"
  | "bcWeb.bcGql.contentAbuseReport"
  | "bcWeb.bcGql.contentCreate"
  | "bcWeb.bcGql.contentDelete"
  | "bcWeb.bcGql.moderationQueue"
  | "bcWeb.bcGql.postCommentService"
  | "bcWeb.bcGql.userAbuseReport"
  | "bcWeb.bcGql.userBan"
  | "bcWeb.bcReactApp.communityAdminTools"
  | "fedGQL.gateway.activeAFC"
  | "fedGQL.gateway.activeDeals"
  | "fedGQL.gateway.roundupProducts"
  | "fedGQL.gateway.skusByRetailerIds"
  | "fedGQL.intGateway.insertBatchSkus"
  | "fedGQL.intGateway.publishAFC"
  | "fedGQL.intGateway.setActiveDeals"
  | "fedGQL.intGateway.updatePrice"
  | "fedGQL.intGateway.upsertRoundupProducts"
  | "ion.ionAuthoringTool.aeroflowDirectStandaloneCreative"
  | "ion.ionAuthoringTool.babyBumpGiveawayThankYouCreative"
  | "ion.ionAuthoringTool.currentSweepsCreative"
  | "ion.ionAuthoringTool.viacordSurveyBcCreative"
  | "ion.ionAuthoringTool.viacordSurveyWteCreative"
  | "ion.ionIntegrations.ppdeExportToAirflowIntegration"
  | "ion.ionIntegrations.sweepsSkylerHistoricalIntegration"
  | "ion.ionIntegrations.viaCordProductionIntegration"
  | "ion.ionIntegrations.viaCordWteProductionIntegration"
  | "ion.ionScriplets.aeroflowConsentApr2024Scriplet"
  | "ion.ionScriplets.aeroflowConsentFeb2025Scriplet"
  | "ion.ionScriplets.aeroflowConsentSep2024Scriplet"
  | "ion.ionScriplets.leadApiCervidilScriplet"
  | "ion.ionScriplets.leadApiHuggiesScriplet"
  | "ion.ionScriplets.leadApiNaturalWayScriplet"
  | "ion.ionScriplets.leadApiSignupJan2024Scriplet"
  | "registry.eventsSnsArn.apiCachePopulated"
  | "registry.eventsSnsArn.retailerLinked"
  | "registry.eventsSnsArn.retailerRegistryMissing"
  | "registry.eventsSnsArn.retailerUnlinked"
  | "registry.registryRetailerIntegrationEventsSns.userLinkedRegistryStopped"
  | "registry.registryService.wte-registry-add-checklist-subcategory"
  | "registry.registryService.wte-registry-add-checklist-subcategory-all"
  | "registry.registryService.wte-registry-create-profile"
  | "registry.registryService.wte-registry-create-profile-anonymous"
  | "registry.registryService.wte-registry-create-profile-default"
  | "registry.registryService.wte-registry-create-profile-empty"
  | "registry.registryService.wte-registry-delete-checklist-subcategory"
  | "registry.registryService.wte-registry-delete-registry-data"
  | "registry.registryService.wte-registry-get-public-registries"
  | "registry.registryService.wte-registry-get-registry"
  | "registry.registryService.wte-registry-get-registry-default"
  | "registry.registryService.wte-registry-get-registry-personalized-url"
  | "registry.registryService.wte-registry-get-registry-shared"
  | "registry.registryService.wte-registry-get-registry-summary"
  | "registry.registryService.wte-registry-get-retailers"
  | "registry.registryService.wte-registry-get-user-linked-registry-product"
  | "registry.registryService.wte-registry-purge-cache"
  | "registry.registryService.wte-registry-retailer-link"
  | "registry.registryService.wte-registry-retailer-link-short-url"
  | "registry.registryService.wte-registry-retailer-unlink"
  | "registry.registryService.wte-registry-send-by-email"
  | "registry.registryService.wte-registry-set-checklist-subcategory"
  | "registry.registryService.wte-registry-stop-sharing-registry"
  | "registry.registryService.wte-registry-update-personalized-url"
  | "registry.registryService.wte-registry-update-profile"
  | "registry.registryService.wte-registry-update-registry"
  | "registry.registryService.wte-registry-update-registry-sort-order"
  | "registry.registryService.wte-registry-update-user-product-content"
  | "sailthru.sailthruApi.sailthruSendHandler"
  | "sailthru.sailthruApi.sailthruUserMethod"
  | "thinkific.thinkificApiGw.coursesEnrollmentHandler"
  | "thinkific.thinkificApiGw.welcomeEmailHandler"
  | "wteAdmin.violationQueue.contentDeleted"
  | "wteAdmin.violationQueue.contentReleased"
  | "wteAdmin.wteAdminUI.markContentAsHam"
  | "wteAdmin.wteAdminUI.markContentAsSpam"
  | "wteAdmin.wteAdminUI.userBan"
  | "wteAdmin.wteAdminUI.userBanFromGroup"
  | "wteCommunityApi.wteCommunityLambda.contentCheck"
  | "wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns"
  | "wteStandaloneCampaign.wteStandaloneCampaignApi.wteLuserAddV2"
  | "wteWeb.community.contentCreate"
  | "wteWeb.community.contentFlag";

type LikeC4DeploymentId =
  never;

type LikeC4ViewId =
  | "afcService"
  | "bcCommunityEvents"
  | "bcMemberEvents"
  | "bcRegistry"
  | "bcWeb"
  | "beRegistration-localytics-read"
  | "beRegistration-localytics-write"
  | "beRegistration-sailthru"
  | "bookmarkNotificationConsolidated"
  | "bookmarkService"
  | "bookmarkSystem"
  | "categorizationContext"
  | "communityModerationSystem"
  | "dailyDealsContainers"
  | "dailyDealsSystem"
  | "deCategorization"
  | "deProductLoading"
  | "deRegistryClassifying"
  | "index"
  | "ionDataFlow"
  | "ionFtp"
  | "ionIntegrationScriplets"
  | "ionProblem"
  | "ionSns"
  | "ionUserEvents"
  | "notificationSystem"
  | "prediktor"
  | "productCatalogContainers"
  | "productCatalogNow"
  | "productCatalogProposed"
  | "productCatalogSystem"
  | "productGuideSystem"
  | "registryArchitecture"
  | "registryDeMessages"
  | "registryDeRepositories"
  | "registryDeSnsSqs"
  | "registryGetRegistry"
  | "registryProfile"
  | "registryService"
  | "registrySns"
  | "registrySystem"
  | "rewardsFlow"
  | "thinkificSystem"
  | "toxicityAbuse"
  | "wteCommunityModeration";

type LikeC4ElementKind =
  | "actor"
  | "component"
  | "container"
  | "system";

type LikeC4Tag =
  | "ghost"
  | "proposed";

declare function isLikeC4ViewId(value: unknown): value is LikeC4ViewId;

type Aux = GenericLikeC4Model.Typed<LikeC4ElementId, LikeC4DeploymentId, LikeC4ViewId, DiagramView<LikeC4ViewId>>;
type LikeC4Model = GenericLikeC4Model<Aux>;
type LikeC4ViewModel = GenericLikeC4Model.View<Aux>;

declare const likeC4Model: LikeC4Model;
declare function useLikeC4Model(): LikeC4Model;
declare function useLikeC4View(viewId: LikeC4ViewId): DiagramView<LikeC4ViewId>;

declare function LikeC4ModelProvider(props: PropsWithChildren): JSX.Element;

type IconRendererProps = {
  node: {
    id: string
    title: string
    icon?: string | undefined
  }
}
declare function RenderIcon(props: IconRendererProps): JSX.Element;

type LikeC4ViewProps = GenericLikeC4ViewProps<LikeC4ViewId, LikeC4Tag, LikeC4ElementKind>;
declare function LikeC4View({viewId, ...props}: LikeC4ViewProps): JSX.Element;

type ReactLikeC4Props = GenericReactLikeC4Props<LikeC4ViewId, LikeC4Tag, LikeC4ElementKind>
declare function ReactLikeC4({viewId, ...props}: ReactLikeC4Props): JSX.Element;

export {
  type LikeC4ElementId,
  type LikeC4DeploymentId,
  type LikeC4ViewId,
  type LikeC4Tag,
  type LikeC4ElementKind,
  type LikeC4ViewProps,
  type ReactLikeC4Props,
  type LikeC4Model,
  isLikeC4ViewId,
  useLikeC4Model,
  useLikeC4View,
  useLikeC4ViewModel,
  likeC4Model,
  LikeC4ModelProvider,
  LikeC4View,
  RenderIcon,
  ReactLikeC4
}
/* prettier-ignore-end */
