import { Resolver } from 'awilix';
import ContainerScope from '../awilix/container-scope';
import { ConfigEnvName } from '../config/environment';

/**
 * Add types to our custom properties in Express' Request type.
 */
declare global {
  namespace Express {
    export interface Request {
      requestId: string;
      remoteIp: string | undefined;
      envName: ConfigEnvName;
      userAgent: string | undefined;
      referer: string | undefined;
      register: <K extends keyof ContainerScope, T extends ContainerScope[K]>(
        name: K,
        registration: Resolver<T>,
      ) => void;
      di: ContainerScope;
    }
  }
}
