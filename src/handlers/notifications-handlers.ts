import { Request, Response } from 'express';
import { NotificationNotFoundException } from '../services/notification/notification-service';
import { decodeUser<PERSON>ey, encodeUser<PERSON><PERSON> } from '../utils/user-key-encoding';
import { Notification } from '../data/data-notification';
import { KeyEncrypter } from '../utils/key-encrypter';
import { validateAuthorizationHeader } from '../auth/validate-authorization-header';

// Custom exception class to match Java InvalidData behavior
export class InvalidDataException extends Error {
  public readonly code: number = 400;

  constructor(message: string) {
    super(message);
    this.name = 'InvalidDataException';
  }
}

export interface NotificationRequest {
  countryCode: string;
  userId: string;
  contentId: string;
  action: string;
}

export interface CreateNotificationRequest extends NotificationRequest {
  postId?: number;
  replyId?: number;
  snippet?: string;
  genericContent?: Record<string, unknown>;
  lifetimeDays?: number;
}

export interface UpdateNotificationRequest {
  count?: number;
  snippet?: string;
  genericContent?: Record<string, unknown>;
  markAsRead?: boolean;
}

export interface NotificationListParams {
  pageIndex?: number;
  pageSize?: number;
}

// Additional handlers for Java service compatibility

/**
 * V1 Handler: List notifications for user (legacy endpoint without auth header)
 * GET /v1/user/notifications?userkey={encodedKey}
 */
export async function v1ListUserNotificationsHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const encodedKey = req.query.userkey as string;

    if (!encodedKey) {
      throw new InvalidDataException('Missing userkey parameter');
    }

    // Decrypt the AES-encrypted user key using KeyEncrypter.decode()
    let userKey: string;
    try {
      userKey = KeyEncrypter.decode(encodedKey);
    } catch (error) {
      throw new InvalidDataException(`Invalid encrypted userkey: ${error}`);
    }

    let countryCode: string, userId: string;
    try {
      ({ countryCode, userId } = decodeUserKey(userKey));
    } catch (error) {
      throw new InvalidDataException(`Invalid userkey format: ${error}`);
    }

    const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 50;
    const pageIndex = req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0;

    const result = await notificationService.listUserNotifications(
      countryCode,
      userId,
      pageIndex,
      pageSize,
    );

    // Format response to match Java service
    const response = {
      unreadCount: result.notifications.filter((n) => n.count > 0).length,
      notifications: result.notifications.map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof InvalidDataException) {
      logger.error(`InvalidData error in V1 list notifications: ${e.message}`);
      res.status(400).json({
        code: 400,
        message: e.message,
      });
      return;
    }

    logger.error(`Error in V1 list notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V1 Handler: Acknowledge all notifications (legacy endpoint without auth header)
 * GET /v1/user/acknowledge?userkey={encodedKey}
 *
 * Java service returns MemberActivityStatus object with timestamp
 */
export async function v1AcknowledgeNotificationsHandler(
  req: Request,
  res: Response,
): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const encodedKey = req.query.userkey as string;

    if (!encodedKey) {
      throw new InvalidDataException('Missing userkey parameter');
    }

    logger.debug('hitting mark notification endpoint');

    // Decrypt the AES-encrypted user key using KeyEncrypter.decode()
    let userKey: string;
    try {
      userKey = KeyEncrypter.decode(encodedKey);
    } catch (error) {
      throw new InvalidDataException(`Invalid encrypted userkey: ${error}`);
    }

    let countryCode: string, userId: string;
    try {
      ({ countryCode, userId } = decodeUserKey(userKey));
    } catch (error) {
      throw new InvalidDataException(`Invalid userkey format: ${error}`);
    }

    // Acknowledge = mark all as read
    await notificationService.markAllAsRead(countryCode, userId);

    // Return MemberActivityStatus structure like Java service
    res.status(200).json({
      lastActivity: new Date().toISOString(),
      userId: userId,
      countryCode: countryCode,
    });
  } catch (e) {
    if (e instanceof InvalidDataException) {
      logger.error(`InvalidData error in V1 acknowledge notifications: ${e.message}`);
      res.status(400).json({
        code: 400,
        message: e.message,
      });
      return;
    }

    logger.error(`Error in V1 acknowledge notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V2 Handler: List notifications with authorization header
 * GET /v2/user/notifications?userkey={userKey}
 */
export async function v2ListUserNotificationsHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const userKey = req.query.userkey as string;

    if (!userKey) {
      throw new InvalidDataException('Missing userkey parameter');
    }

    let countryCode: string, userId: string;
    try {
      ({ countryCode, userId } = decodeUserKey(userKey));
    } catch (error) {
      throw new InvalidDataException(`Invalid userkey format: ${error}`);
    }

    // V2 requires authorization header
    const _user = validateAuthorizationHeader(req, userId);

    const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 50;
    const pageIndex = req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0;

    const result = await notificationService.listUserNotifications(
      countryCode,
      userId,
      pageIndex,
      pageSize,
    );

    // Format response to match Java service
    const response = {
      unreadCount: result.notifications.filter((n) => n.count > 0).length,
      notifications: result.notifications.map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof InvalidDataException) {
      logger.error(`InvalidData error in V2 list notifications: ${e.message}`);
      res.status(400).json({
        code: 400,
        message: e.message,
      });
      return;
    }

    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V2 list notifications: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V2 list notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V2 Handler: Acknowledge notifications with authorization header
 * GET /v2/user/acknowledge?userkey={userKey}
 *
 * Java service returns ListNotificationsResult after acknowledge operation
 */
export async function v2AcknowledgeNotificationsHandler(
  req: Request,
  res: Response,
): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const userKey = req.query.userkey as string;

    if (!userKey) {
      res.status(400).json({
        success: false,
        message: 'Missing userkey parameter',
      });
      return;
    }

    const { countryCode, userId } = decodeUserKey(userKey);

    // V2 requires authorization header
    const _user = validateAuthorizationHeader(req, userId);

    await notificationService.markAllAsRead(countryCode, userId);

    // Return updated notification list like Java service
    const pageSize = 50;
    const result = await notificationService.listUserNotifications(
      countryCode,
      userId,
      0,
      pageSize,
    );

    // Format response to match Java service ListNotificationsResult
    const response = {
      unreadCount: result.notifications.filter((n) => n.count > 0).length,
      notifications: result.notifications.map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V2 acknowledge: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V2 acknowledge notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V2 Handler: Mark specific notification as read
 * POST /v2/user/read
 *
 * Java service returns ListNotificationsResult after read operation
 */
export async function v2ReadNotificationHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const { userKey, contentActionKey } = req.body;

    if (!userKey || !contentActionKey) {
      res.status(400).json({
        success: false,
        message: 'Missing userKey or contentActionKey in request body',
      });
      return;
    }

    const { countryCode, userId } = decodeUserKey(userKey);
    const [contentId, action] = contentActionKey.split('#');

    if (!contentId || !action) {
      res.status(400).json({
        success: false,
        message: 'Invalid contentActionKey format. Expected: contentId#action',
      });
      return;
    }

    // V2 requires authorization header
    const _user = validateAuthorizationHeader(req, userId);

    await notificationService.updateNotification(countryCode, userId, contentId, action, {
      markAsRead: true,
    });

    // Return updated notification list like Java service
    const pageSize = 50;
    const result = await notificationService.listUserNotifications(
      countryCode,
      userId,
      0,
      pageSize,
    );

    // Format response to match Java service ListNotificationsResult
    const response = {
      unreadCount: result.notifications.filter((n) => n.count > 0).length,
      notifications: result.notifications.map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.constructor.name === 'NotificationNotFoundException') {
      logger.error(`Notification not found for V2 read: ${e.message}`);
      res.status(404).json({
        success: false,
        message: 'Notification not found',
      });
      return;
    }

    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V2 read: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V2 read notification: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V2 Handler: Mark all notifications as read
 * POST /v2/user/readall
 *
 * Java service returns ListNotificationsResult after readall operation
 */
export async function v2ReadAllNotificationsHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const { userKey } = req.body;

    if (!userKey) {
      res.status(400).json({
        success: false,
        message: 'Missing userKey in request body',
      });
      return;
    }

    const { countryCode, userId } = decodeUserKey(userKey);

    // V2 requires authorization header
    const _user = validateAuthorizationHeader(req, userId);

    await notificationService.markAllAsRead(countryCode, userId);

    // Return updated notification list like Java service
    const pageSize = 50;
    const result = await notificationService.listUserNotifications(
      countryCode,
      userId,
      0,
      pageSize,
    );

    // Format response to match Java service ListNotificationsResult
    const response = {
      unreadCount: result.notifications.filter((n) => n.count > 0).length,
      notifications: result.notifications.map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V2 read all: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V2 read all notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V3 Handler: List notifications with dual user system support
 * GET /v3/user/notifications
 */
export async function v3ListUserNotificationsHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    // V3 supports both legacy user keys and global auth IDs
    const userKey = req.query.userkey as string;
    const globalAuthId = req.query.globalAuthId as string;

    if (!userKey && !globalAuthId) {
      res.status(400).json({
        success: false,
        message: 'Missing userkey or globalAuthId parameter',
      });
      return;
    }

    // V3 requires authorization header
    const authHeader = (req.headers['authorization'] as string) ?? '';
    if (!authHeader) {
      res.status(401).json({
        success: false,
        message: 'Missing Authorization header',
      });
      return;
    }

    const notifications: Notification[] = [];
    const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 50;
    const pageIndex = req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0;

    // If global auth ID is provided, extract from JWT and get notifications
    if (globalAuthId) {
      const _user = validateAuthorizationHeader(req, globalAuthId);

      // For V3, we could query both legacy and new notification systems
      // For now, we'll use a default country code since locale is not available in BcUser
      const countryCode = 'US'; // Default to US - this could be extracted from request or user preferences
      const result = await notificationService.listUserNotifications(
        countryCode,
        globalAuthId,
        pageIndex,
        pageSize,
      );

      notifications.push(...result.notifications);
    }

    // If user key is provided, get notifications for legacy user ID
    if (userKey) {
      const { countryCode, userId } = decodeUserKey(userKey);

      // Validate that user has access to this legacy user ID
      // For now, we'll trust the JWT validation
      const result = await notificationService.listUserNotifications(
        countryCode,
        userId,
        pageIndex,
        pageSize,
      );

      notifications.push(...result.notifications);
    }

    // Remove duplicates and sort by creation date (newest first)
    const uniqueNotifications = notifications
      .filter((notification, index, array) => {
        return (
          array.findIndex(
            (n) =>
              n.contentActionKey.contentId === notification.contentActionKey.contentId &&
              n.contentActionKey.action === notification.contentActionKey.action,
          ) === index
        );
      })
      .sort((a, b) => new Date(b.createDateTime).getTime() - new Date(a.createDateTime).getTime());

    // Format response to match Java service
    const response = {
      unreadCount: uniqueNotifications.filter((n) => n.count > 0).length,
      notifications: uniqueNotifications.slice(0, pageSize).map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V3 list notifications: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V3 list notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V3 Handler: Acknowledge notifications with dual user system support
 * GET /v3/user/acknowledge
 *
 * Java service returns ListNotificationsResult after acknowledge operation
 */
export async function v3AcknowledgeNotificationsHandler(
  req: Request,
  res: Response,
): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const userKey = req.query.userkey as string;
    const globalAuthId = req.query.globalAuthId as string;

    if (!userKey && !globalAuthId) {
      res.status(400).json({
        success: false,
        message: 'Missing userkey or globalAuthId parameter',
      });
      return;
    }

    // V3 requires authorization header
    const authHeader = (req.headers['authorization'] as string) ?? '';
    if (!authHeader) {
      res.status(401).json({
        success: false,
        message: 'Missing Authorization header',
      });
      return;
    }

    const notifications: Notification[] = [];
    const pageSize = 50;

    // If global auth ID is provided, acknowledge notifications for global auth user
    if (globalAuthId) {
      const _user = validateAuthorizationHeader(req, globalAuthId);

      const countryCode = 'US'; // Default to US - this could be extracted from request or user preferences
      await notificationService.markAllAsRead(countryCode, globalAuthId);

      const result = await notificationService.listUserNotifications(
        countryCode,
        globalAuthId,
        0,
        pageSize,
      );
      notifications.push(...result.notifications);
    }

    // If user key is provided, acknowledge notifications for legacy user
    if (userKey) {
      const { countryCode, userId } = decodeUserKey(userKey);
      await notificationService.markAllAsRead(countryCode, userId);

      const result = await notificationService.listUserNotifications(
        countryCode,
        userId,
        0,
        pageSize,
      );
      notifications.push(...result.notifications);
    }

    // Remove duplicates and sort by creation date (newest first)
    const uniqueNotifications = notifications
      .filter((notification, index, array) => {
        return (
          array.findIndex(
            (n) =>
              n.contentActionKey.contentId === notification.contentActionKey.contentId &&
              n.contentActionKey.action === notification.contentActionKey.action,
          ) === index
        );
      })
      .sort((a, b) => new Date(b.createDateTime).getTime() - new Date(a.createDateTime).getTime());

    // Format response to match Java service ListNotificationsResult
    const response = {
      unreadCount: uniqueNotifications.filter((n) => n.count > 0).length,
      notifications: uniqueNotifications.slice(0, pageSize).map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V3 acknowledge: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V3 acknowledge notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V3 Handler: Mark specific notification as read with dual user system support
 * POST /v3/user/read
 *
 * Java service returns ListNotificationsResult after read operation
 */
export async function v3ReadNotificationHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const { userKey, globalAuthId, contentActionKey } = req.body;

    if ((!userKey && !globalAuthId) || !contentActionKey) {
      res.status(400).json({
        success: false,
        message: 'Missing (userKey or globalAuthId) and contentActionKey in request body',
      });
      return;
    }

    const [contentId, action] = contentActionKey.split('#');

    if (!contentId || !action) {
      res.status(400).json({
        success: false,
        message: 'Invalid contentActionKey format. Expected: contentId#action',
      });
      return;
    }

    // V3 requires authorization header
    const authHeader = (req.headers['authorization'] as string) ?? '';
    if (!authHeader) {
      res.status(401).json({
        success: false,
        message: 'Missing Authorization header',
      });
      return;
    }

    const notifications: Notification[] = [];
    const pageSize = 50;
    let updatedCount = 0;

    // If global auth ID is provided, update notification for global auth user
    if (globalAuthId) {
      const _user = validateAuthorizationHeader(req, globalAuthId);

      try {
        const countryCode = 'US'; // Default to US - this could be extracted from request or user preferences
        await notificationService.updateNotification(countryCode, globalAuthId, contentId, action, {
          markAsRead: true,
        });
        updatedCount++;

        const result = await notificationService.listUserNotifications(
          countryCode,
          globalAuthId,
          0,
          pageSize,
        );
        notifications.push(...result.notifications);
      } catch (error) {
        if (!(error instanceof NotificationNotFoundException)) {
          throw error;
        }
        // Ignore not found for dual system - notification might only exist in one system
      }
    }

    // If user key is provided, update notification for legacy user
    if (userKey) {
      try {
        const { countryCode, userId } = decodeUserKey(userKey);
        await notificationService.updateNotification(countryCode, userId, contentId, action, {
          markAsRead: true,
        });
        updatedCount++;

        const result = await notificationService.listUserNotifications(
          countryCode,
          userId,
          0,
          pageSize,
        );
        notifications.push(...result.notifications);
      } catch (error) {
        if (!(error instanceof NotificationNotFoundException)) {
          throw error;
        }
        // Ignore not found for dual system - notification might only exist in one system
      }
    }

    if (updatedCount === 0) {
      res.status(404).json({
        success: false,
        message: 'Notification not found in any user system',
      });
      return;
    }

    // Remove duplicates and sort by creation date (newest first)
    const uniqueNotifications = notifications
      .filter((notification, index, array) => {
        return (
          array.findIndex(
            (n) =>
              n.contentActionKey.contentId === notification.contentActionKey.contentId &&
              n.contentActionKey.action === notification.contentActionKey.action,
          ) === index
        );
      })
      .sort((a, b) => new Date(b.createDateTime).getTime() - new Date(a.createDateTime).getTime());

    // Format response to match Java service ListNotificationsResult
    const response = {
      unreadCount: uniqueNotifications.filter((n) => n.count > 0).length,
      notifications: uniqueNotifications.slice(0, pageSize).map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V3 read: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V3 read notification: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * V3 Handler: Mark all notifications as read with dual user system support
 * POST /v3/user/readall
 *
 * Java service returns ListNotificationsResult after readall operation
 */
export async function v3ReadAllNotificationsHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const { userKey, globalAuthId } = req.body;

    if (!userKey && !globalAuthId) {
      res.status(400).json({
        success: false,
        message: 'Missing userKey or globalAuthId in request body',
      });
      return;
    }

    // V3 requires authorization header
    const authHeader = (req.headers['authorization'] as string) ?? '';
    if (!authHeader) {
      res.status(401).json({
        success: false,
        message: 'Missing Authorization header',
      });
      return;
    }

    const notifications: Notification[] = [];
    const pageSize = 50;

    // If global auth ID is provided, mark all notifications as read for global auth user
    if (globalAuthId) {
      const _user = validateAuthorizationHeader(req, globalAuthId);

      const countryCode = 'US'; // Default to US - this could be extracted from request or user preferences
      await notificationService.markAllAsRead(countryCode, globalAuthId);

      const result = await notificationService.listUserNotifications(
        countryCode,
        globalAuthId,
        0,
        pageSize,
      );
      notifications.push(...result.notifications);
    }

    // If user key is provided, mark all notifications as read for legacy user
    if (userKey) {
      const { countryCode, userId } = decodeUserKey(userKey);
      await notificationService.markAllAsRead(countryCode, userId);

      const result = await notificationService.listUserNotifications(
        countryCode,
        userId,
        0,
        pageSize,
      );
      notifications.push(...result.notifications);
    }

    // Remove duplicates and sort by creation date (newest first)
    const uniqueNotifications = notifications
      .filter((notification, index, array) => {
        return (
          array.findIndex(
            (n) =>
              n.contentActionKey.contentId === notification.contentActionKey.contentId &&
              n.contentActionKey.action === notification.contentActionKey.action,
          ) === index
        );
      })
      .sort((a, b) => new Date(b.createDateTime).getTime() - new Date(a.createDateTime).getTime());

    // Format response to match Java service ListNotificationsResult
    const response = {
      unreadCount: uniqueNotifications.filter((n) => n.count > 0).length,
      notifications: uniqueNotifications.slice(0, pageSize).map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    if (e instanceof Error && e.message.includes('Authorization header')) {
      logger.error(`Authorization error for V3 read all: ${e.message}`);
      res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
      return;
    }

    logger.error(`Error in V3 read all notifications: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * Internal Handler: Get encoded user key hash (admin endpoint)
 * GET /internal/userkey/{userKey}
 *
 * Maps to Java: userKey(@PathVariable String userKey) -> KeyEncrypter.encode(userKey)
 */
export async function internalUserKeyHandler(req: Request, res: Response): Promise<void> {
  const { logger } = req.di;

  try {
    const userKey = req.params.userKey as string;

    if (!userKey) {
      res.status(400).json({
        success: false,
        message: 'Missing userKey parameter',
      });
      return;
    }

    logger.debug(`fetching userKey hash for ${userKey}`);

    // AES encryption to match Java KeyEncrypter.encode() functionality
    const encrypted = KeyEncrypter.encode(userKey);

    res.status(200).json(encrypted);
  } catch (e) {
    logger.error(`Error in internal user key endpoint: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

/**
 * Internal Handler: List notifications for user key (admin endpoint)
 * GET /internal/v1/user/notifications?userkey={userKey}
 *
 * Maps to Java: notifications(@RequestParam('userkey') String userKey) -> notificationHelper.listNotifications(userKey)
 * This is a convenience interface for internal users, such as load tests,
 * to load a user's notifications using an unencrypted key value
 */
export async function internalV1NotificationsHandler(req: Request, res: Response): Promise<void> {
  const { notificationService, logger } = req.di;

  try {
    const userKey = req.query.userkey as string;

    if (!userKey) {
      res.status(400).json({
        success: false,
        message: 'Missing userkey parameter',
      });
      return;
    }

    logger.debug('hitting admin user notification endpoint');

    let countryCode: string, userId: string;
    try {
      ({ countryCode, userId } = decodeUserKey(userKey));
    } catch (error) {
      res.status(400).json({
        success: false,
        message: `Invalid userkey format: ${error}`,
      });
      return;
    }

    const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 50;
    const pageIndex = req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0;

    const result = await notificationService.listUserNotifications(
      countryCode,
      userId,
      pageIndex,
      pageSize,
    );

    // Format response to match Java service
    const response = {
      unreadCount: result.notifications.filter((n) => n.count > 0).length,
      notifications: result.notifications.map((notification) => ({
        userKey: encodeUserKey(notification.userKey.countryCode, notification.userKey.userId),
        contentActionKey: `${notification.contentActionKey.contentId}#${notification.contentActionKey.action}`,
        postId: notification.postId,
        replyId: notification.replyId,
        snippet: notification.snippet,
        genericContent: notification.genericContent,
        count: notification.count,
        createDateTime: notification.createDateTime.toISOString(),
        isRead: notification.count === 0,
      })),
    };

    res.status(200).json(response);
  } catch (e) {
    logger.error(`Error in internal V1 notifications endpoint: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

// Export types already defined above
