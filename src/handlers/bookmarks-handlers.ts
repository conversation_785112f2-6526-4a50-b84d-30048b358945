import { Request, Response } from 'express';
import { Bookmark } from '../data/data-bookmark';
import {
  BookmarkNotFoundException,
  TooManyBookmarksException,
} from '../services/bookmark/bookmark-service';
import {
  InvalidTokenError,
  TokenMismatchError,
  MissingAuthHeaderError,
} from '../exceptions/auth-exceptions';
import { InvalidPaginationError } from '../exceptions/validation-exceptions';
import { validateAuthorizationHeader } from '../auth/validate-authorization-header';

interface ErrorWithStatusCode extends Error {
  statusCode: number;
  errorCode?: string;
}

export interface BookmarkRequest {
  memberId: string; // global auth id
  contentSource: string;
  contentType: string;
  contentId: string;
}

export interface BookmarkListRequest {
  memberId: string;
  contentSource: string;
  contentType: string;
}

export type SaveBookmarkRequest = BookmarkRequest & { createDate: string | null };

export type ListBookmarkResponse = Omit<Bookmark, 'id' | 'create_user' | 'update_user'>[];

export interface ListPaginatedBookmarkParams {
  pageIndex: number;
  pageSize: number;
}

export interface BatchDeleteRequest {
  memberId: string;
  contentSource: string;
  contentType: string;
  contentIds: string[];
}

export interface BatchDeleteResponse {
  deletedCount: number;
  errors: string[];
}

export interface BatchSaveBookmarkRequest {
  bookmarks: SaveBookmarkRequest[];
}

export interface BatchSaveBookmarkResponse {
  savedCount: number;
  errors: string[];
}

/* Support content_source / content_type combinations are:
bcsite,article
bcsite,checklist
bcsite,expert_question
bcsite,is_it_safe_question
bcsite,photo_gallery
bcsite,question
bcsite,ready_or_not_question
bcsite,stage_page
bcsite,topic
bcsite,video
community,post
community:de_DE,post
community:en_AU,post
community:en_CA,post
community:en_GB,post
community:en_IN,post
community:en_US,post
community:es_US,post
community:pt_BR,post
healthcheck,healthcheck
 */

function requestIntoBookmarkRequest(req: Request<BookmarkRequest>): BookmarkRequest {
  return {
    memberId: req.params.memberId,
    contentSource: req.params.contentSource,
    contentType: req.params.contentType,
    contentId: req.params.contentId,
  };
}

export async function saveBookmarkHandler(
  req: Request, // Use default params type (ParamsDictionary)
  res: Response,
): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const _user = validateAuthorizationHeader(req);

    const bookmarkRequest = req as unknown as Request<BookmarkRequest>;
    const bookmark = requestIntoBookmarkRequest(bookmarkRequest);
    const saveBookmark: SaveBookmarkRequest = {
      ...bookmark,
      createDate: req.body.createDate || null,
    };

    // Check if bookmark already exists to determine status code
    const existingBookmark = await bookmarkService.getBookmark(bookmark);
    const isUpdate = existingBookmark !== null;

    await bookmarkService.saveBookmark(saveBookmark, _user);

    // Return 201 for create, 200 for update (like Java service)
    const statusCode = isUpdate ? 200 : 201;

    // Return the bookmark object like Java service
    const savedBookmark = await bookmarkService.getBookmark(bookmark);
    if (savedBookmark) {
      res.status(statusCode).json({
        id: savedBookmark.id,
        memberId: savedBookmark.member_id,
        contentSource: savedBookmark.content_source,
        contentType: savedBookmark.content_type,
        contentId: savedBookmark.content_id,
        createDate: savedBookmark.create_date,
        updateDate: savedBookmark.update_date,
        createUser: savedBookmark.create_user,
        updateUser: savedBookmark.update_user,
      });
    } else {
      res.status(500).send();
    }
  } catch (e: unknown) {
    if (e instanceof TooManyBookmarksException) {
      logger.error(`User ${req.params.memberId} reached maximum number of bookmarks`);
      res.status(400).send('Reached maximum number of bookmarks.');
      return;
    }

    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for user ${req.params.memberId}: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error saving bookmark: ${e}`);
    res.status(500).send();
  }
}

export async function deleteBookmarkHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const _user = validateAuthorizationHeader(req);

    const bookmarkRequest = req as unknown as Request<BookmarkRequest>;
    const bookmark = requestIntoBookmarkRequest(bookmarkRequest);
    await bookmarkService.deleteBookmark(bookmark, _user);

    res.status(204).send();
  } catch (e: unknown) {
    if (e instanceof BookmarkNotFoundException) {
      logger.error(`Bookmark not found: ${(e as Error).message}`);
      res.status(404).send();
      return;
    }

    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for user ${req.params.memberId}: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error deleting bookmark: ${e}`);
    res.status(500).send();
  }
}

export async function batchDeleteBookmarksHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const memberId = req.params.memberId;
    const contentSource = req.params.contentSource;
    const contentType = req.params.contentType;

    // Get contentIds from query parameter
    const contentIdsParam = req.query.contentIds as string;

    if (!contentIdsParam) {
      res.status(400).send();
      return;
    }

    // Parse content IDs (comma-separated list)
    const contentIds = contentIdsParam
      .split(',')
      .map((id) => id.trim())
      .filter(Boolean);

    if (contentIds.length === 0) {
      res.status(400).send();
      return;
    }

    // Validate the user
    const _user = validateAuthorizationHeader(req);

    // Execute batch delete
    const _result = await bookmarkService.batchDeleteBookmarks(
      memberId,
      contentSource,
      contentType,
      contentIds,
      _user,
    );

    res.status(200).send();
  } catch (e: unknown) {
    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for batch delete: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error batch deleting bookmarks: ${e}`);
    res.status(500).send();
  }
}

export async function listBookmarkHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;
  try {
    validateAuthorizationHeader(req);

    const bookmark = {
      memberId: req.params.memberId,
      contentSource: req.params.contentSource,
      contentType: req.params.contentType,
      contentId: '', // Will be ignored for list operations
    };
    const response = await bookmarkService.listBookmarks({
      memberId: bookmark.memberId,
      contentSource: bookmark.contentSource,
      contentType: bookmark.contentType,
      contentId: bookmark.contentId,
    });

    // Convert to Java-compatible format - return array of bookmarks directly
    const bookmarks = response || [];
    const javaFormatBookmarks = bookmarks.map((bookmark) => ({
      memberId: req.params.memberId,
      contentSource: bookmark.content_source,
      contentType: bookmark.content_type,
      contentId: bookmark.content_id,
      createDate: bookmark.create_date,
      updateDate: bookmark.update_date,
    }));

    res.status(200).json(javaFormatBookmarks);
  } catch (e: unknown) {
    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for listing bookmarks: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error listing bookmarks: ${e}`);
    res.status(500).send();
  }
}

export async function listPaginatedBookmarkHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;
  try {
    validateAuthorizationHeader(req);

    const pageParams = {
      pageIndex: req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0,
      pageSize: req.query.pageSize ? parseInt(req.query.pageSize as string) : 100,
    };

    if (pageParams.pageIndex < 0 || pageParams.pageSize < 1) {
      throw new InvalidPaginationError(pageParams.pageIndex, pageParams.pageSize);
    }

    const response = await bookmarkService.listBookmarksPaginated(
      {
        memberId: req.params.memberId,
        contentSource: req.params.contentSource,
        contentType: req.params.contentType,
        contentId: '', // Not used for pagination query
      },
      pageParams,
    );

    res.status(200).json(response);
  } catch (e: unknown) {
    if (e instanceof InvalidPaginationError) {
      logger.error(`Invalid pagination parameters: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as Error).message,
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for paginated bookmarks: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error listing paginated bookmarks: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

// Additional handlers for missing bookmark endpoints

export async function getBookmarkHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const _user = validateAuthorizationHeader(req);

    const bookmarkRequest = req as unknown as Request<BookmarkRequest>;
    const bookmark = requestIntoBookmarkRequest(bookmarkRequest);
    const result = await bookmarkService.getBookmark(bookmark);

    if (!result) {
      res.status(404).json({
        success: false,
        message: 'Bookmark not found',
      });
      return;
    }

    res.status(200).json({
      id: result.id,
      memberId: result.member_id,
      contentSource: result.content_source,
      contentType: result.content_type,
      contentId: result.content_id,
      createDate: result.create_date,
      updateDate: result.update_date,
    });
  } catch (e: unknown) {
    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for getting bookmark: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error getting bookmark: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

export async function listAllBookmarksByMemberHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const memberId = req.params.memberId;

    const _user = validateAuthorizationHeader(req);

    const bookmarks = await bookmarkService.listAllBookmarksByMember(memberId);

    res.status(200).json(bookmarks);
  } catch (e: unknown) {
    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for listing all bookmarks: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error listing all bookmarks: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

export async function listBookmarksByMemberAndSourceHandler(
  req: Request,
  res: Response,
): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const memberId = req.params.memberId;
    const contentSource = req.params.contentSource;

    const _user = validateAuthorizationHeader(req);

    const bookmarks = await bookmarkService.listBookmarksByMemberAndSource(memberId, contentSource);

    res.status(200).json(bookmarks);
  } catch (e: unknown) {
    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for listing bookmarks by source: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error listing bookmarks by source: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

export async function batchSaveBookmarksHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const memberId = req.params.memberId;
    const requestBody = req.body as BatchSaveBookmarkRequest;

    if (!requestBody.bookmarks || !Array.isArray(requestBody.bookmarks)) {
      res.status(400).json({
        success: false,
        message: 'Invalid request body. Expected bookmarks array.',
      });
      return;
    }

    if (requestBody.bookmarks.length === 0) {
      res.status(400).json({
        success: false,
        message: 'Bookmarks array cannot be empty.',
      });
      return;
    }

    const _user = validateAuthorizationHeader(req);

    // Validate that all bookmarks belong to the member
    for (const bookmark of requestBody.bookmarks) {
      if (bookmark.memberId !== memberId) {
        res.status(400).json({
          success: false,
          message: `All bookmarks must belong to member ${memberId}`,
        });
        return;
      }
    }

    const result = await bookmarkService.batchSaveBookmarks(memberId, requestBody.bookmarks, _user);

    res.status(201).json({
      success: true,
      savedCount: result.savedCount,
      errors: result.errors,
    });
  } catch (e: unknown) {
    if (e instanceof TooManyBookmarksException) {
      logger.error(`Batch save failed - too many bookmarks: ${(e as Error).message}`);
      res.status(400).json({
        success: false,
        message: 'Too many bookmarks',
      });
      return;
    }

    if (
      e instanceof InvalidTokenError ||
      e instanceof TokenMismatchError ||
      e instanceof MissingAuthHeaderError
    ) {
      logger.error(`Authorization error for batch save: ${(e as Error).message}`);
      res.status((e as ErrorWithStatusCode).statusCode).json({
        success: false,
        message: (e as ErrorWithStatusCode).statusCode === 401 ? 'Unauthorized' : 'Forbidden',
        errorCode: (e as ErrorWithStatusCode).errorCode,
      });
      return;
    }

    logger.error(`Error batch saving bookmarks: ${e}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}

// Additional handlers for Java service batch content endpoints

/**
 * Internal handler for batch services to get bookmarks by content
 * GET /bookmark/v1/content/{contentSource}/{contentType}/{contentId}
 * Uses internal token authentication (bsToken header)
 */
/**
 * Internal handler for batch services to get bookmarks by specific content
 * GET /bookmark/v1/content/{contentSource}/{contentType}/{contentId}
 * Uses internal token authentication (bsToken header)
 */
export async function getBookmarksByContentHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const { contentSource, contentType, contentId } = req.params;
    const pageIndex = req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0;
    const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 100;

    // Validate internal token (bsToken header)
    const internalToken = req.headers['bstoken'] as string;
    if (!internalToken) {
      res.status(401).send();
      return;
    }

    // TODO: Validate internal token against expected value from environment
    // For now, we'll accept any non-empty token (this should be enhanced)

    const bookmarks = await bookmarkService.getBookmarksByContent(
      contentSource,
      contentType,
      contentId,
      pageIndex,
      pageSize,
    );

    // Return simple array like Java service
    res.status(200).json(
      bookmarks.map((bookmark) => ({
        id: bookmark.id,
        memberId: bookmark.member_id,
        contentSource: bookmark.content_source,
        contentType: bookmark.content_type,
        contentId: bookmark.content_id,
        createDate: bookmark.create_date,
        updateDate: bookmark.update_date,
        createUser: bookmark.create_user,
        updateUser: bookmark.update_user,
      })),
    );
  } catch (error) {
    logger.error(`Error getting bookmarks by content: ${error}`);
    res.status(500).send();
  }
}

/**
 * Internal handler for batch services to get bookmarks by content type
 * GET /bookmark/v1/content/{contentSource}/contentType/{contentType}
 * Uses internal token authentication (bsToken header)
 */
export async function getBookmarksByContentTypeHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const { contentSource, contentType } = req.params;
    const pageIndex = req.query.pageIndex ? parseInt(req.query.pageIndex as string) : 0;
    const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 100;

    // Validate internal token (bsToken header)
    const internalToken = req.headers['bstoken'] as string;
    if (!internalToken) {
      res.status(401).send();
      return;
    }

    // TODO: Validate internal token against expected value from environment

    const bookmarks = await bookmarkService.getBookmarksByContentType(
      contentSource,
      contentType,
      pageIndex,
      pageSize,
    );

    // Return simple array like Java service
    res.status(200).json(
      bookmarks.map((bookmark) => ({
        id: bookmark.id,
        memberId: bookmark.member_id,
        contentSource: bookmark.content_source,
        contentType: bookmark.content_type,
        contentId: bookmark.content_id,
        createDate: bookmark.create_date,
        updateDate: bookmark.update_date,
        createUser: bookmark.create_user,
        updateUser: bookmark.update_user,
      })),
    );
  } catch (error) {
    logger.error(`Error getting bookmarks by content type: ${error}`);
    res.status(500).send();
  }
}

/**
 * Internal handler for batch services to delete bookmarks when content is removed
 * DELETE /bookmark/v1/content/{contentSource}/{contentType}/{contentId}
 * Uses internal token authentication (bsToken header)
 */
export async function deleteBookmarksByContentHandler(req: Request, res: Response): Promise<void> {
  const { bookmarkService, logger } = req.di;

  try {
    const { contentSource, contentType, contentId } = req.params;

    // Validate internal token (bsToken header)
    const internalToken = req.headers['bstoken'] as string;
    if (!internalToken) {
      res.status(401).send();
      return;
    }

    // TODO: Validate internal token against expected value from environment

    const _deletedCount = await bookmarkService.deleteBookmarksByContent(
      contentSource,
      contentType,
      contentId,
    );

    // Return 204 No Content like Java service
    res.status(204).send();
  } catch (error) {
    logger.error(`Error deleting bookmarks by content: ${error}`);
    res.status(500).send();
  }
}

// Export types already defined above
