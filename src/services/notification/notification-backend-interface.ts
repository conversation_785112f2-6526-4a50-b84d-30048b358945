import { Notification } from '../../data/data-notification';

/**
 * Interface for notification storage backends
 *
 * DynamoDB-only backend interface for notifications.
 */

export interface NotificationBackend {
  /**
   * Create a new notification
   */
  createNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
    additionalData?: {
      postId?: number;
      replyId?: number;
      snippet?: string;
      genericContent?: Record<string, unknown>;
      lifetimeDays?: number;
    },
  ): Promise<boolean>;

  /**
   * Get a specific notification
   */
  getNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<Notification | null>;

  /**
   * Update a notification
   */
  updateNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
    updates: {
      count?: number;
      snippet?: string;
      genericContent?: Record<string, unknown>;
      markAsRead?: boolean;
      ReadStatus?: string;
      ReadDateTime?: string;
    },
  ): Promise<boolean>;

  /**
   * Delete a notification
   */
  deleteNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<boolean>;

  /**
   * List notifications for a user
   */
  listUserNotifications(
    countryCode: string,
    userId: string,
    options?: {
      pageSize?: number;
      lastEvaluatedKey?: string;
      startTime?: Date;
      endTime?: Date;
    },
  ): Promise<{
    notifications: Notification[];
    lastEvaluatedKey?: string;
    unreadCount: number;
  }>;

  /**
   * Mark notifications as read/acknowledged
   */
  markNotificationsAsRead(
    countryCode: string,
    userId: string,
    notificationIds?: string[],
  ): Promise<number>;

  /**
   * Clean up expired notifications
   */
  cleanupExpiredNotifications(): Promise<number>;
}

/**
 * Configuration for DynamoDB notification backend
 */
export interface NotificationBackendConfig {
  region: string;
  tableName: string;
  ttlDays?: number;
  endpoint?: string; // For local development
}
