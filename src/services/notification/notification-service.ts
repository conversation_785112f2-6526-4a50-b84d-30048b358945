import { NotificationDynamoDBBackend } from './notification-dynamodb-backend';
import { DynamoDBConfig } from '../../config/environment';
import AppLogger from '../../logs/app-logger';
import { Notification } from '../../data/data-notification';

export class NotificationNotFoundException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotificationNotFoundException';
  }
}

export class TooManyNotificationsException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TooManyNotificationsException';
  }
}

/**
 * Notification Service using DynamoDB backend
 *
 * This service provides notification management functionality that mirrors
 * the original Java notification service but uses TypeScript and DynamoDB.
 */
class NotificationService {
  static readonly NOTIFICATION_LIMIT_PER_USER = 100;
  static readonly DEFAULT_NOTIFICATION_LIFETIME_DAYS = 7;
  static readonly DEFAULT_PAGE_SIZE = 20;

  private backend: NotificationDynamoDBBackend;
  private logger?: AppLogger;

  constructor(dynamoConfig: DynamoDBConfig, logger?: AppLogger) {
    this.backend = new NotificationDynamoDBBackend(logger, dynamoConfig);
    this.logger = logger;
  }

  /**
   * Create a new notification
   */
  async createNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
    additionalData?: {
      postId?: number;
      replyId?: number;
      snippet?: string;
      genericContent?: Record<string, unknown>;
      lifetimeDays?: number;
    },
  ): Promise<Notification> {
    try {
      this.logger?.debugMeta('Creating notification', {
        countryCode,
        userId,
        contentId,
        action,
        additionalData,
      });

      const success = await this.backend.createNotification(
        countryCode,
        userId,
        contentId,
        action,
        additionalData,
      );

      if (!success) {
        throw new TooManyNotificationsException(`User ${userId} has reached notification limit`);
      }

      // Return created notification for handler compatibility
      const notification = await this.backend.getNotification(
        countryCode,
        userId,
        contentId,
        action,
      );
      if (!notification) {
        throw new Error('Failed to retrieve created notification');
      }
      return notification;
    } catch (error) {
      this.logger?.errorMeta('Failed to create notification', {
        error,
        countryCode,
        userId,
        contentId,
        action,
      });
      throw error;
    }
  }

  /**
   * Get a specific notification
   */
  async getNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<Notification> {
    try {
      const notification = await this.backend.getNotification(
        countryCode,
        userId,
        contentId,
        action,
      );

      if (!notification) {
        throw new NotificationNotFoundException(
          `Notification not found for user ${userId}, content ${contentId}, action ${action}`,
        );
      }

      return notification;
    } catch (error) {
      this.logger?.errorMeta('Failed to get notification', {
        error,
        countryCode,
        userId,
        contentId,
        action,
      });
      throw error;
    }
  }

  /**
   * Update a notification
   */
  async updateNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
    updates: {
      count?: number;
      snippet?: string;
      genericContent?: Record<string, unknown>;
      markAsRead?: boolean;
      ReadStatus?: string;
      ReadDateTime?: string;
    },
  ): Promise<Notification> {
    try {
      const success = await this.backend.updateNotification(
        countryCode,
        userId,
        contentId,
        action,
        updates,
      );

      if (!success) {
        throw new NotificationNotFoundException(
          `Notification not found for user ${userId}, content ${contentId}, action ${action}`,
        );
      }

      // Return updated notification for handler compatibility
      const notification = await this.backend.getNotification(
        countryCode,
        userId,
        contentId,
        action,
      );
      if (!notification) {
        throw new NotificationNotFoundException(
          `Notification not found after update for user ${userId}, content ${contentId}, action ${action}`,
        );
      }
      return notification;
    } catch (error) {
      this.logger?.errorMeta('Failed to update notification', {
        error,
        countryCode,
        userId,
        contentId,
        action,
        updates,
      });
      throw error;
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<boolean> {
    try {
      return await this.backend.deleteNotification(countryCode, userId, contentId, action);
    } catch (error) {
      this.logger?.errorMeta('Failed to delete notification', {
        error,
        countryCode,
        userId,
        contentId,
        action,
      });
      throw error;
    }
  }

  /**
   * List notifications for a user with pagination (handler-compatible signature)
   */
  async listUserNotifications(
    countryCode: string,
    userId: string,
    pageIndexOrOptions?:
      | number
      | {
          pageSize?: number;
          lastEvaluatedKey?: string;
          startTime?: Date;
          endTime?: Date;
        },
    pageSize?: number,
  ): Promise<{
    notifications: Notification[];
    lastEvaluatedKey?: string;
    unreadCount: number;
    totalCount?: number;
    pageIndex?: number;
    pageSize?: number;
  }> {
    try {
      let options: {
        pageSize?: number;
        lastEvaluatedKey?: string;
        startTime?: Date;
        endTime?: Date;
      } = {};
      let actualPageIndex = 0;
      let actualPageSize = NotificationService.DEFAULT_PAGE_SIZE;

      // Handle both old (pageIndex, pageSize) and new (options) signatures
      if (typeof pageIndexOrOptions === 'number') {
        actualPageIndex = pageIndexOrOptions;
        actualPageSize = pageSize || NotificationService.DEFAULT_PAGE_SIZE;
        options = { pageSize: actualPageSize };
      } else if (pageIndexOrOptions) {
        options = pageIndexOrOptions;
        actualPageSize = options.pageSize || NotificationService.DEFAULT_PAGE_SIZE;
      } else {
        options = { pageSize: actualPageSize };
      }

      const result = await this.backend.listUserNotifications(countryCode, userId, options);

      // Add compatibility fields for handlers expecting old format
      return {
        ...result,
        totalCount: result.notifications.length,
        pageIndex: actualPageIndex,
        pageSize: actualPageSize,
      };
    } catch (error) {
      this.logger?.errorMeta('Failed to list user notifications', {
        error,
        countryCode,
        userId,
        pageIndexOrOptions,
        pageSize,
      });
      throw error;
    }
  }

  /**
   * Mark notifications as read/acknowledged
   * Compatible with V1, V2, V3 notification endpoints
   */
  async acknowledgeNotifications(
    countryCode: string,
    userId: string,
    notificationIds?: string[],
  ): Promise<number> {
    try {
      return await this.backend.markNotificationsAsRead(countryCode, userId, notificationIds);
    } catch (error) {
      this.logger?.errorMeta('Failed to acknowledge notifications', {
        error,
        countryCode,
        userId,
        notificationIds,
      });
      throw error;
    }
  }

  /**
   * Mark specific notification as read
   */
  async readNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<boolean> {
    try {
      return await this.backend.updateNotification(countryCode, userId, contentId, action, {
        ReadStatus: 'READ',
        ReadDateTime: new Date().toISOString(),
      });
    } catch (error) {
      this.logger?.errorMeta('Failed to mark notification as read', {
        error,
        countryCode,
        userId,
        contentId,
        action,
      });
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async readAllNotifications(countryCode: string, userId: string): Promise<number> {
    try {
      return await this.backend.markNotificationsAsRead(countryCode, userId);
    } catch (error) {
      this.logger?.errorMeta('Failed to mark all notifications as read', {
        error,
        countryCode,
        userId,
      });
      throw error;
    }
  }

  /**
   * Mark all notifications as read (alias for compatibility with handlers)
   */
  async markAllAsRead(countryCode: string, userId: string): Promise<number> {
    return this.readAllNotifications(countryCode, userId);
  }

  /**
   * Clean up expired notifications (handled automatically by DynamoDB TTL)
   */
  async cleanupExpiredNotifications(): Promise<number> {
    try {
      return await this.backend.cleanupExpiredNotifications();
    } catch (error) {
      this.logger?.errorMeta('Failed to cleanup expired notifications', { error });
      throw error;
    }
  }

  /**
   * Parse user key from various formats (V1, V2, V3 compatibility)
   */
  static parseUserKey(userKey: string): { countryCode: string; userId: string } {
    try {
      // Handle base64 encoded keys (V2/V3)
      let decodedKey = userKey;
      try {
        decodedKey = Buffer.from(userKey, 'base64').toString('utf-8');
      } catch {
        // Not base64, use as-is
      }

      // Parse format: "countryCode,userId"
      const parts = decodedKey.split(',');
      if (parts.length !== 2) {
        throw new Error('Invalid user key format');
      }

      return {
        countryCode: parts[0].trim(),
        userId: parts[1].trim(),
      };
    } catch (error) {
      throw new Error(`Invalid user key format: ${userKey}`);
    }
  }

  /**
   * Parse global auth ID for V3 compatibility
   */
  static parseGlobalAuthId(globalAuthId: string): { countryCode: string; userId: string } {
    // For V3 endpoints, global auth ID is used directly as userId
    // Country code is typically determined from the request context or defaulted
    return {
      countryCode: 'US', // Default, should be overridden by request context
      userId: globalAuthId,
    };
  }
}

export default NotificationService;
