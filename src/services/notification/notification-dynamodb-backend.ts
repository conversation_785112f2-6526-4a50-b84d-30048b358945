import { DynamoDBClient, DynamoDBClientConfig } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  UpdateCommand,
  DeleteCommand,
  QueryCommand,
  QueryCommandInput,
} from '@aws-sdk/lib-dynamodb';
import { NotificationBackend } from './notification-backend-interface';
import { Notification } from '../../data/data-notification';
import AppLogger from '../../logs/app-logger';

interface DynamoDBNotificationItem {
  UserHashKey: string;
  ContentRangeKey: string;
  CountryCode: string;
  UserId: string;
  Action: string;
  ContentId: string;
  DateNumber: number;
  CreateDateTime: string;
  ActionCreateDate: string;
  Count: number;
  ttl: number;
  PostId?: number | null;
  ReplyId?: number | null;
  Snippet?: string | null;
  GenericContent?: Record<string, unknown> | null;
  ReadStatus?: string;
  ReadDateTime?: string;
}

/**
 * DynamoDB implementation of notification backend
 *
 * This matches the original Java notification service architecture using DynamoDB.
 * Benefits: High performance, auto-scaling, built-in TTL, time-based queries
 *
 * Table Structure (matches original service):
 * - Partition Key: UserHashKey (String) - "{countryCode},{userId}"
 * - Sort Key: ContentRangeKey (String) - "{action}#{contentId}#{timestamp}"
 * - TTL: ttl (Number) - Unix timestamp for automatic cleanup
 * - GSI: DateNumber-index for time-based queries
 */
export class NotificationDynamoDBBackend implements NotificationBackend {
  private dynamoClient: DynamoDBDocumentClient;
  private tableName: string;
  private ttlDays: number;

  constructor(
    private logger: AppLogger | undefined,
    config: {
      region: string;
      tableName: string;
      ttlDays?: number;
      endpoint?: string; // For local development
    },
  ) {
    const clientConfig: DynamoDBClientConfig = {
      region: config.region,
    };

    // For local development with DynamoDB Local
    if (config.endpoint) {
      clientConfig.endpoint = config.endpoint;
      clientConfig.credentials = {
        accessKeyId: 'dummy',
        secretAccessKey: 'dummy',
      };
    }

    const dynamoClient = new DynamoDBClient(clientConfig);
    this.dynamoClient = DynamoDBDocumentClient.from(dynamoClient);
    this.tableName = config.tableName;
    this.ttlDays = config.ttlDays || 7;
  }

  private createUserHashKey(countryCode: string, userId: string): string {
    return `${countryCode},${userId}`;
  }

  private createContentRangeKey(action: string, contentId: string, timestamp?: number): string {
    const ts = timestamp || Date.now();
    return `${action}#${contentId}#${ts}`;
  }

  private convertDynamoItemToNotification(item: DynamoDBNotificationItem): Notification {
    // Parse the UserHashKey to get country code and user ID
    const [countryCode, userId] = item.UserHashKey.split(',');

    // Parse the ContentRangeKey to get action, content ID, and timestamp
    const [action, contentId, timestamp] = item.ContentRangeKey.split('#');

    return {
      userKey: {
        countryCode,
        userId,
      },
      contentActionKey: {
        action,
        contentId,
        createDate: new Date(parseInt(timestamp)),
      },
      postId: item.PostId || undefined,
      replyId: item.ReplyId || undefined,
      snippet: item.Snippet || undefined,
      genericContent: item.GenericContent || undefined,
      count: item.Count || 0,
      createDateTime: new Date(item.CreateDateTime),
      ttl: item.ttl,
    };
  }

  private createDateNumber(): number {
    // YYYYMMDD format as number for GSI queries
    const now = new Date();
    return parseInt(
      now.getFullYear().toString() +
        (now.getMonth() + 1).toString().padStart(2, '0') +
        now.getDate().toString().padStart(2, '0'),
    );
  }

  private calculateTTL(): number {
    return Math.floor(Date.now() / 1000) + this.ttlDays * 24 * 60 * 60;
  }

  async createNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
    additionalData?: {
      postId?: number;
      replyId?: number;
      snippet?: string;
      genericContent?: Record<string, unknown>;
      lifetimeDays?: number;
    },
  ): Promise<boolean> {
    try {
      // Check user notification count (prevent spam)
      const userNotificationCount = await this.getUserNotificationCount(countryCode, userId);
      if (userNotificationCount >= 100) {
        // Configurable limit
        this.logger?.warnMeta('User has too many notifications', {
          countryCode,
          userId,
          count: userNotificationCount,
        });
        return false;
      }

      const userHashKey = this.createUserHashKey(countryCode, userId);
      const contentRangeKey = this.createContentRangeKey(action, contentId);
      const dateNumber = this.createDateNumber();
      const ttl = this.calculateTTL();

      const item = {
        UserHashKey: userHashKey,
        ContentRangeKey: contentRangeKey,
        CountryCode: countryCode,
        UserId: userId,
        Action: action,
        ContentId: contentId,
        DateNumber: dateNumber,
        CreateDateTime: new Date().toISOString(),
        ActionCreateDate: new Date().toISOString(),
        Count: 1,
        ttl: ttl,
        // Additional fields from original service
        PostId: additionalData?.postId || null,
        ReplyId: additionalData?.replyId || null,
        Snippet: additionalData?.snippet || null,
        GenericContent: additionalData || null,
      };

      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.tableName,
          Item: item,
          // Prevent duplicate notifications with same natural key
          ConditionExpression:
            'attribute_not_exists(UserHashKey) AND attribute_not_exists(ContentRangeKey)',
        }),
      );

      this.logger?.debugMeta('Created notification', { countryCode, userId, action, contentId });
      return true;
    } catch (error: unknown) {
      if ((error as Error).name === 'ConditionalCheckFailedException') {
        // Notification already exists, update count instead
        return await this.incrementNotificationCount(countryCode, userId, contentId, action);
      }

      this.logger?.errorMeta('Failed to create notification', {
        error: (error as Error).message,
        countryCode,
        userId,
        contentId,
        action,
      });
      return false;
    }
  }

  private async incrementNotificationCount(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<boolean> {
    try {
      const userHashKey = this.createUserHashKey(countryCode, userId);
      // For updates, we need to find the existing item first
      const existingItems = await this.queryUserNotificationsByAction(
        countryCode,
        userId,
        action,
        contentId,
      );

      if (existingItems.length === 0) {
        return false;
      }

      const existingItem = existingItems[0];

      await this.dynamoClient.send(
        new UpdateCommand({
          TableName: this.tableName,
          Key: {
            UserHashKey: userHashKey,
            ContentRangeKey:
              existingItem.contentActionKey.action +
              '#' +
              existingItem.contentActionKey.contentId +
              '#' +
              existingItem.contentActionKey.createDate.getTime(),
          },
          UpdateExpression: 'ADD #count :inc SET CreateDateTime = :now, ttl = :ttl',
          ExpressionAttributeNames: {
            '#count': 'Count',
          },
          ExpressionAttributeValues: {
            ':inc': 1,
            ':now': new Date().toISOString(),
            ':ttl': this.calculateTTL(),
          },
        }),
      );

      return true;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to increment notification count', {
        error: (error as Error).message,
        countryCode,
        userId,
        contentId,
        action,
      });
      return false;
    }
  }

  private async getUserNotificationCount(countryCode: string, userId: string): Promise<number> {
    try {
      const userHashKey = this.createUserHashKey(countryCode, userId);

      const response = await this.dynamoClient.send(
        new QueryCommand({
          TableName: this.tableName,
          KeyConditionExpression: 'UserHashKey = :userKey',
          ExpressionAttributeValues: {
            ':userKey': userHashKey,
          },
          Select: 'COUNT',
        }),
      );

      return response.Count || 0;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to get user notification count', {
        error: (error as Error).message,
        countryCode,
        userId,
      });
      return 0;
    }
  }

  private async queryUserNotificationsByAction(
    countryCode: string,
    userId: string,
    action: string,
    contentId?: string,
  ): Promise<Notification[]> {
    try {
      const userHashKey = this.createUserHashKey(countryCode, userId);

      let keyConditionExpression = 'UserHashKey = :userKey';
      const expressionAttributeValues: Record<string, unknown> = {
        ':userKey': userHashKey,
      };

      if (contentId) {
        keyConditionExpression += ' AND begins_with(ContentRangeKey, :actionContent)';
        expressionAttributeValues[':actionContent'] = `${action}#${contentId}`;
      } else {
        keyConditionExpression += ' AND begins_with(ContentRangeKey, :action)';
        expressionAttributeValues[':action'] = `${action}#`;
      }

      const response = await this.dynamoClient.send(
        new QueryCommand({
          TableName: this.tableName,
          KeyConditionExpression: keyConditionExpression,
          ExpressionAttributeValues: expressionAttributeValues,
          ScanIndexForward: false, // Most recent first
        }),
      );

      return (response.Items || []).map((item) =>
        this.convertDynamoItemToNotification(item as DynamoDBNotificationItem),
      );
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to query user notifications by action', {
        error: (error as Error).message,
        countryCode,
        userId,
        action,
        contentId,
      });
      return [];
    }
  }

  async getNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<Notification | null> {
    try {
      const items = await this.queryUserNotificationsByAction(
        countryCode,
        userId,
        action,
        contentId,
      );
      return items.length > 0 ? items[0] : null;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to get notification', {
        error: (error as Error).message,
        countryCode,
        userId,
        contentId,
        action,
      });
      return null;
    }
  }

  async updateNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
    updates: Record<string, unknown>,
  ): Promise<boolean> {
    try {
      const existingItems = await this.queryUserNotificationsByAction(
        countryCode,
        userId,
        action,
        contentId,
      );

      if (existingItems.length === 0) {
        return false;
      }

      const existingItem = existingItems[0];
      const userHashKey = this.createUserHashKey(countryCode, userId);

      // Build update expression dynamically
      const updateExpressions: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, unknown> = {};

      Object.entries(updates).forEach(([key, value], index) => {
        const nameAlias = `#attr${index}`;
        const valueAlias = `:val${index}`;

        updateExpressions.push(`${nameAlias} = ${valueAlias}`);
        expressionAttributeNames[nameAlias] = key;
        expressionAttributeValues[valueAlias] = value;
      });

      if (updateExpressions.length === 0) {
        return true;
      }

      await this.dynamoClient.send(
        new UpdateCommand({
          TableName: this.tableName,
          Key: {
            UserHashKey: userHashKey,
            ContentRangeKey:
              existingItem.contentActionKey.action +
              '#' +
              existingItem.contentActionKey.contentId +
              '#' +
              existingItem.contentActionKey.createDate.getTime(),
          },
          UpdateExpression: `SET ${updateExpressions.join(', ')}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
        }),
      );

      return true;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to update notification', {
        error: (error as Error).message,
        countryCode,
        userId,
        contentId,
        action,
        updates,
      });
      return false;
    }
  }

  async deleteNotification(
    countryCode: string,
    userId: string,
    contentId: string,
    action: string,
  ): Promise<boolean> {
    try {
      const existingItems = await this.queryUserNotificationsByAction(
        countryCode,
        userId,
        action,
        contentId,
      );

      if (existingItems.length === 0) {
        return true; // Already deleted
      }

      const userHashKey = this.createUserHashKey(countryCode, userId);

      // Delete all matching items (in case there are multiple)
      for (const item of existingItems) {
        await this.dynamoClient.send(
          new DeleteCommand({
            TableName: this.tableName,
            Key: {
              UserHashKey: userHashKey,
              ContentRangeKey:
                item.contentActionKey.action +
                '#' +
                item.contentActionKey.contentId +
                '#' +
                item.contentActionKey.createDate.getTime(),
            },
          }),
        );
      }

      return true;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to delete notification', {
        error: (error as Error).message,
        countryCode,
        userId,
        contentId,
        action,
      });
      return false;
    }
  }

  async listUserNotifications(
    countryCode: string,
    userId: string,
    options?: {
      pageSize?: number;
      lastEvaluatedKey?: string;
      startTime?: Date;
      endTime?: Date;
    },
  ): Promise<{
    notifications: Notification[];
    lastEvaluatedKey?: string;
    unreadCount: number;
  }> {
    try {
      const userHashKey = this.createUserHashKey(countryCode, userId);
      const pageSize = options?.pageSize || 20;

      const queryParams: QueryCommandInput = {
        TableName: this.tableName,
        KeyConditionExpression: 'UserHashKey = :userKey',
        ExpressionAttributeValues: {
          ':userKey': userHashKey,
        },
        Limit: pageSize,
        ScanIndexForward: false, // Most recent first
      };

      // Handle pagination
      if (options?.lastEvaluatedKey) {
        try {
          queryParams.ExclusiveStartKey = JSON.parse(
            Buffer.from(options.lastEvaluatedKey, 'base64').toString(),
          );
        } catch (e) {
          this.logger?.warnMeta('Invalid lastEvaluatedKey provided', {
            lastEvaluatedKey: options.lastEvaluatedKey,
          });
        }
      }

      const response = await this.dynamoClient.send(new QueryCommand(queryParams));

      // Get total count for unread count (this is an approximation)
      const countResponse = await this.dynamoClient.send(
        new QueryCommand({
          TableName: this.tableName,
          KeyConditionExpression: 'UserHashKey = :userKey',
          ExpressionAttributeValues: {
            ':userKey': userHashKey,
          },
          Select: 'COUNT',
        }),
      );

      const notifications = (response.Items || []).map((item) =>
        this.convertDynamoItemToNotification(item as DynamoDBNotificationItem),
      );
      const nextKey = response.LastEvaluatedKey
        ? Buffer.from(JSON.stringify(response.LastEvaluatedKey)).toString('base64')
        : undefined;

      return {
        notifications,
        lastEvaluatedKey: nextKey,
        unreadCount: countResponse.Count || 0,
      };
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to list user notifications', {
        error: (error as Error).message,
        countryCode,
        userId,
        options,
      });
      return {
        notifications: [],
        unreadCount: 0,
      };
    }
  }

  async markNotificationsAsRead(
    countryCode: string,
    userId: string,
    notificationIds?: string[],
  ): Promise<number> {
    try {
      const userHashKey = this.createUserHashKey(countryCode, userId);
      let itemsToUpdate: Record<string, unknown>[] = [];

      if (notificationIds && notificationIds.length > 0) {
        // Mark specific notifications as read
        for (const contentRangeKey of notificationIds) {
          itemsToUpdate.push({
            UserHashKey: userHashKey,
            ContentRangeKey: contentRangeKey,
          });
        }
      } else {
        // Mark all notifications as read
        const allNotifications = await this.listUserNotifications(countryCode, userId, {
          pageSize: 100,
        });
        itemsToUpdate = allNotifications.notifications.map((item) => ({
          UserHashKey: this.createUserHashKey(item.userKey.countryCode, item.userKey.userId),
          ContentRangeKey:
            item.contentActionKey.action +
            '#' +
            item.contentActionKey.contentId +
            '#' +
            item.contentActionKey.createDate.getTime(),
        }));
      }

      let markedCount = 0;

      // Update in batches (DynamoDB batch limit is 25)
      for (let i = 0; i < itemsToUpdate.length; i += 25) {
        const batch = itemsToUpdate.slice(i, i + 25);

        for (const item of batch) {
          try {
            await this.dynamoClient.send(
              new UpdateCommand({
                TableName: this.tableName,
                Key: item,
                UpdateExpression: 'SET ReadStatus = :read, ReadDateTime = :now',
                ExpressionAttributeValues: {
                  ':read': 'READ',
                  ':now': new Date().toISOString(),
                },
                ConditionExpression: 'attribute_exists(UserHashKey)',
              }),
            );
            markedCount++;
          } catch (e) {
            // Item might not exist, continue
            this.logger?.debugMeta('Failed to mark item as read', { item, error: e });
          }
        }
      }

      return markedCount;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to mark notifications as read', {
        error: (error as Error).message,
        countryCode,
        userId,
        notificationIds,
      });
      return 0;
    }
  }

  async cleanupExpiredNotifications(): Promise<number> {
    try {
      // DynamoDB TTL handles this automatically, but we can also run manual cleanup
      // This method would scan for expired items and delete them manually if needed

      this.logger?.infoMeta('DynamoDB TTL handles automatic cleanup', {
        ttlDays: this.ttlDays,
        tableName: this.tableName,
      });

      // Return 0 since TTL handles this automatically
      return 0;
    } catch (error: unknown) {
      this.logger?.errorMeta('Failed to cleanup expired notifications', {
        error: (error as Error).message,
      });
      return 0;
    }
  }
}
