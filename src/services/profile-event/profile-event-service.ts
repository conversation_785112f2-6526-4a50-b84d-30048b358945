import AWS from 'aws-sdk';
import BcUser from '../../auth/bc/bc-user';
import { BookmarkRequest } from '../../handlers/bookmarks-handlers';
import AppLogger from '../../logs/app-logger';

const HubMap: Record<string, string> = {
  en_GB: 'gb',
  en_AU: 'gb',
  en_IN: 'gb',
  en_CA: 'gb',
  de_DE: 'de',
  ar_SA: 'ae',
  pt_BR: 'br',
  es_US: 'es',
  en_US: 'us',
};

export interface BookmarkEvent {
  eventType: string;
  eventVersion: string;
  timestamp: number | string;
  siteMemberId: string;
  globalMemberId: string;
  email?: string;
  hub: string;
  countryCode: string;
  contentSource: string;
  contentType: string;
  contentId: string;
  action: string;
}

export interface SubscriptionEvent {
  eventType: string;
  eventVersion: string;
  subscriptionType: string;
  timestamp: number | string;
  email?: string;
  siteMemberId: string;
  globalMemberId: string;
  hub: string;
  countryCode: string;
  subscriptionId: string;
  subscriptionMethod: string;
  subscribed: string;
}

class ProfileEventService {
  #snsClient: AWS.SNS;
  #usMemberEventsArn: string;
  #logger?: AppLogger;

  constructor(usMemberEventArn: string, logger?: AppLogger) {
    this.#snsClient = new AWS.SNS();
    this.#usMemberEventsArn = usMemberEventArn;
    this.#logger = logger;
  }

  /**
   * Sends a subscription event to SNS when a user subscribes or unsubscribes from content
   * This is used specifically for community post subscriptions
   */
  async sendSubscriptionEvent(
    user: BcUser,
    bookmark: BookmarkRequest,
    subscribed: boolean,
  ): Promise<boolean> {
    let countryCode = 'en_US';
    let hub = 'us';

    if (bookmark.contentSource.startsWith('community')) {
      countryCode = bookmark.contentSource.split(':')[1];
      hub = HubMap[countryCode] ?? 'us';
    }

    const message: SubscriptionEvent = {
      eventType: 'Subscription',
      eventVersion: '0.1',
      subscriptionType: 'Subscription',
      timestamp: Date.now(),
      email: user.email || '',
      siteMemberId: user.memberId?.toString() || user.globalAuthId,
      globalMemberId: user.globalAuthId,
      hub: hub,
      countryCode: countryCode,
      subscriptionId: bookmark.contentId,
      subscriptionMethod: bookmark.contentType,
      subscribed: subscribed ? 'true' : 'false',
    };

    try {
      const result = await this.#snsClient
        .publish({
          TopicArn: this.#usMemberEventsArn,
          Message: JSON.stringify(message),
        })
        .promise();

      this.#logger?.debugMeta('SNS subscription event sent', {
        messageId: result.MessageId,
        memberId: user.globalAuthId,
        contentId: bookmark.contentId,
        subscribed: subscribed,
      });

      return true;
    } catch (error) {
      this.#logger?.errorMeta('Failed to send SNS subscription event', {
        error: (error as Error).message,
        memberId: user.globalAuthId,
        contentId: bookmark.contentId,
      });
      return false;
    }
  }

  /**
   * Sends a bookmark event to SNS when a user creates or deletes a bookmark
   * This is a generic event used for all bookmark types
   */
  async sendBookmarkEvent(
    user: BcUser,
    bookmark: BookmarkRequest,
    action: 'create' | 'delete',
  ): Promise<boolean> {
    let countryCode = 'en_US';
    let hub = 'us';

    if (bookmark.contentSource.startsWith('community')) {
      countryCode = bookmark.contentSource.split(':')[1];
      hub = HubMap[countryCode] ?? 'us';
    }

    const message: BookmarkEvent = {
      eventType: 'Bookmark',
      eventVersion: '0.1',
      timestamp: Date.now(),
      siteMemberId: user.memberId?.toString() || user.globalAuthId,
      globalMemberId: user.globalAuthId,
      email: user.email || '',
      hub: hub,
      countryCode: countryCode,
      contentSource: bookmark.contentSource,
      contentType: bookmark.contentType,
      contentId: bookmark.contentId,
      action: action.toString(),
    };

    try {
      const result = await this.#snsClient
        .publish({
          TopicArn: this.#usMemberEventsArn,
          Message: JSON.stringify(message),
        })
        .promise();

      this.#logger?.debugMeta('SNS bookmark event sent', {
        messageId: result.MessageId,
        memberId: user.globalAuthId,
        contentId: bookmark.contentId,
        action: action,
      });

      return true;
    } catch (error) {
      this.#logger?.errorMeta('Failed to send SNS bookmark event', {
        error: (error as Error).message,
        memberId: user.globalAuthId,
        contentId: bookmark.contentId,
        action: action,
      });
      return false;
    }
  }

  /**
   * Send a system bookmark event (for batch operations without user context)
   * @param bookmark Bookmark details
   * @param action Action performed ('create' or 'delete')
   * @returns Promise<boolean> indicating success
   */
  async sendSystemBookmarkEvent(
    bookmark: { memberId: string; contentSource: string; contentType: string; contentId: string },
    action: 'create' | 'delete',
  ): Promise<boolean> {
    try {
      // For system events, we create a minimal event structure
      const message = {
        eventType: 'bookmark_system_event',
        action: action,
        timestamp: new Date().toISOString(),
        data: {
          memberId: bookmark.memberId,
          contentSource: bookmark.contentSource,
          contentType: bookmark.contentType,
          contentId: bookmark.contentId,
        },
        source: 'bookmark-service-batch',
      };

      const result = await this.#snsClient
        .publish({
          TopicArn: this.#usMemberEventsArn,
          Message: JSON.stringify(message),
        })
        .promise();

      this.#logger?.debugMeta('SNS system bookmark event sent', {
        messageId: result.MessageId,
        memberId: bookmark.memberId,
        contentId: bookmark.contentId,
        action: action,
      });

      return true;
    } catch (error) {
      this.#logger?.errorMeta('Failed to send SNS system bookmark event', {
        error: (error as Error).message,
        memberId: bookmark.memberId,
        contentId: bookmark.contentId,
        action: action,
      });
      return false;
    }
  }
}

export default ProfileEventService;
