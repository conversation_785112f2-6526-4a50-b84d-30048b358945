import { OkPacket, Pool, RowDataPacket } from 'mysql2/promise';
import { BookmarkRow } from '../../data/data-bookmark';
import {
  BookmarkRequest,
  ListPaginatedBookmarkParams,
  SaveBookmarkRequest,
} from '../../handlers/bookmarks-handlers';
import ProfileEventService from '../profile-event/profile-event-service';
import BcUser from '../../auth/bc/bc-user';
import AppLogger from '../../logs/app-logger';
import { Queries } from './bookmark-queries';

export class TooManyBookmarksException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TooManyBookmarksException';
  }
}

export class BookmarkNotFoundException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'BookmarkNotFoundException';
  }
}

class BookmarkService {
  static readonly BOOKMARK_LIMIT_BY_CONTENT_SOURCE_AND_TYPE = 200;

  #dbPool: Pool;
  #profileEventService: ProfileEventService;
  #logger?: AppLogger;

  constructor(dbPool: Pool, profileEventService: ProfileEventService, logger?: AppLogger) {
    this.#dbPool = dbPool;
    this.#profileEventService = profileEventService;
    this.#logger = logger;
  }

  async countBookmarks(bookmark: BookmarkRequest): Promise<number> {
    const [rows] = await this.#dbPool.query<(RowDataPacket & { count: number })[]>(
      Queries.countBookmarks,
      bookmark,
    );
    const rowCount = rows[0].count;

    return rowCount;
  }

  async saveBookmark(bookmark: SaveBookmarkRequest, user?: BcUser): Promise<void> {
    if (
      (await this.countBookmarks(bookmark)) >=
      BookmarkService.BOOKMARK_LIMIT_BY_CONTENT_SOURCE_AND_TYPE
    ) {
      throw new TooManyBookmarksException(
        `User ${bookmark.memberId} has reached the maximum number of bookmarks for ${bookmark.contentSource}/${bookmark.contentType}`,
      );
    }

    const [{ affectedRows }] = await this.#dbPool.query<OkPacket>(Queries.insertBookmark, bookmark);

    if (affectedRows !== 1) throw new Error('Bookmark not saved');

    // Send event notification if user info is provided
    if (user) {
      try {
        await this.#profileEventService.sendBookmarkEvent(user, bookmark, 'create');

        // Also send subscription event for community posts
        if (bookmark.contentSource.startsWith('community') && bookmark.contentType === 'post') {
          await this.#profileEventService.sendSubscriptionEvent(user, bookmark, true);
        }
      } catch (error) {
        this.#logger?.errorMeta('Failed to send bookmark creation event', {
          error: (error as Error).message,
          memberId: user.globalAuthId,
          bookmark,
        });
        // Don't throw - the bookmark was saved successfully
      }
    }
  }

  async deleteBookmark(bookmark: BookmarkRequest, user?: BcUser): Promise<void> {
    this.#logger?.debugMeta('Deleting bookmark', { bookmark });

    const [{ affectedRows }] = await this.#dbPool.query<OkPacket>(Queries.deleteBookmark, bookmark);

    if (affectedRows !== 1) {
      throw new BookmarkNotFoundException(
        `Bookmark not found for member ${bookmark.memberId} and content ${bookmark.contentSource}/${bookmark.contentType}/${bookmark.contentId}`,
      );
    }

    // Send event notification if user info is provided
    if (user) {
      try {
        await this.#profileEventService.sendBookmarkEvent(user, bookmark, 'delete');

        // Also send subscription event for community posts
        if (bookmark.contentSource.startsWith('community') && bookmark.contentType === 'post') {
          await this.#profileEventService.sendSubscriptionEvent(user, bookmark, false);
        }
      } catch (error) {
        this.#logger?.errorMeta('Failed to send bookmark deletion event', {
          error: (error as Error).message,
          memberId: user.globalAuthId,
          bookmark,
        });
        // Don't throw - the bookmark was deleted successfully
      }
    }
  }

  async batchDeleteBookmarks(
    memberId: string,
    contentSource: string,
    contentType: string,
    contentIds: string[],
    user?: BcUser,
  ): Promise<{ deletedCount: number; errors: string[] }> {
    if (!contentIds.length) {
      return { deletedCount: 0, errors: [] };
    }

    this.#logger?.debugMeta('Batch deleting bookmarks', {
      memberId,
      contentSource,
      contentType,
      contentIds: contentIds.length,
    });

    const errors: string[] = [];
    let deletedCount = 0;

    // MySQL IN clause with prepared statements is tricky - we format it directly
    const placeholders = contentIds.map(() => '?').join(',');
    const query = `
      DELETE FROM bookmark
      WHERE member_id = ?
      AND content_source = ?
      AND content_type = ?
      AND content_id IN (${placeholders})
    `;

    const [result] = await this.#dbPool.query<OkPacket>(query, [
      memberId,
      contentSource,
      contentType,
      ...contentIds,
    ]);

    deletedCount = result.affectedRows;

    // Send events for each deleted bookmark if user info is provided
    if (user && deletedCount > 0) {
      for (const contentId of contentIds) {
        const bookmark: BookmarkRequest = {
          memberId,
          contentSource,
          contentType,
          contentId,
        };

        try {
          await this.#profileEventService.sendBookmarkEvent(user, bookmark, 'delete');

          // Also send subscription event for community posts
          if (contentSource.startsWith('community') && contentType === 'post') {
            await this.#profileEventService.sendSubscriptionEvent(user, bookmark, false);
          }
        } catch (error) {
          this.#logger?.errorMeta('Failed to send bookmark deletion event in batch operation', {
            error: (error as Error).message,
            memberId: user.globalAuthId,
            contentId,
          });
          errors.push(
            `Failed to send event for contentId ${contentId}: ${(error as Error).message}`,
          );
        }
      }
    }

    return { deletedCount, errors };
  }

  async listBookmarks(
    bookmark: BookmarkRequest,
  ): Promise<Omit<BookmarkRow, 'id' | 'create_user' | 'update_user'>[] | null> {
    this.#logger?.debugMeta('Listing bookmarks', { bookmark });

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(Queries.listBookmarks, bookmark);

    if (rows.length === 0) return null;
    return rows.map((r) => {
      return {
        content_source: r.content_source,
        content_type: r.content_type,
        content_id: r.content_id,
        create_date: r.create_date,
        update_date: r.update_date,
      };
    });
  }

  async listBookmarksPaginated(
    bookmark: BookmarkRequest,
    pageParams: ListPaginatedBookmarkParams,
  ): Promise<{
    items: Omit<BookmarkRow, 'id' | 'create_user' | 'update_user'>[] | null;
    totalItems: number;
    pageIndex: number;
    pageSize: number;
  }> {
    this.#logger?.debugMeta('Listing paginated bookmarks', { bookmark, pageParams });

    // Get total count first for pagination info
    const totalItems = await this.countBookmarks(bookmark);

    if (totalItems === 0) {
      return {
        items: null,
        totalItems: 0,
        pageIndex: pageParams.pageIndex,
        pageSize: pageParams.pageSize,
      };
    }

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(Queries.listBookmarksPaginated, {
      ...bookmark,
      pageOffset: pageParams.pageIndex * pageParams.pageSize,
      pageSize: pageParams.pageSize,
    });

    if (rows.length === 0) {
      return {
        items: null,
        totalItems,
        pageIndex: pageParams.pageIndex,
        pageSize: pageParams.pageSize,
      };
    }

    const items = rows.map((r) => {
      return {
        content_source: r.content_source,
        content_type: r.content_type,
        content_id: r.content_id,
        create_date: r.create_date,
        update_date: r.update_date,
      };
    });

    return {
      items,
      totalItems,
      pageIndex: pageParams.pageIndex,
      pageSize: pageParams.pageSize,
    };
  }

  // New methods for missing functionality
  async getBookmark(bookmark: BookmarkRequest): Promise<BookmarkRow | null> {
    this.#logger?.debugMeta('Getting single bookmark', { bookmark });

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(Queries.getBookmark, [
      bookmark.memberId,
      bookmark.contentSource,
      bookmark.contentType,
      bookmark.contentId,
    ]);

    return rows.length > 0 ? rows[0] : null;
  }

  async listAllBookmarksByMember(
    memberId: string,
  ): Promise<Omit<BookmarkRow, 'id' | 'create_user' | 'update_user'>[]> {
    this.#logger?.debugMeta('Listing all bookmarks for member', { memberId });

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(
      `SELECT * FROM bookmark
       WHERE member_id = ?
       ORDER BY create_date DESC`,
      [memberId],
    );

    return rows.map((r) => ({
      content_source: r.content_source,
      content_type: r.content_type,
      content_id: r.content_id,
      create_date: r.create_date,
      update_date: r.update_date,
    }));
  }

  async listBookmarksByMemberAndSource(
    memberId: string,
    contentSource: string,
  ): Promise<Omit<BookmarkRow, 'id' | 'create_user' | 'update_user'>[]> {
    this.#logger?.debugMeta('Listing bookmarks by member and source', {
      memberId,
      contentSource,
    });

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(
      `SELECT * FROM bookmark
       WHERE member_id = ?
       AND content_source = ?
       ORDER BY create_date DESC`,
      [memberId, contentSource],
    );

    return rows.map((r) => ({
      content_source: r.content_source,
      content_type: r.content_type,
      content_id: r.content_id,
      create_date: r.create_date,
      update_date: r.update_date,
    }));
  }

  async batchSaveBookmarks(
    memberId: string,
    bookmarks: SaveBookmarkRequest[],
    user?: BcUser,
  ): Promise<{ savedCount: number; errors: string[] }> {
    if (!bookmarks.length) {
      return { savedCount: 0, errors: [] };
    }

    this.#logger?.debugMeta('Batch saving bookmarks', {
      memberId,
      bookmarkCount: bookmarks.length,
    });

    const errors: string[] = [];
    let savedCount = 0;

    // Validate all bookmarks belong to the member
    for (const bookmark of bookmarks) {
      if (bookmark.memberId !== memberId) {
        errors.push(`Bookmark member ID ${bookmark.memberId} does not match ${memberId}`);
        continue;
      }
    }

    if (errors.length > 0) {
      return { savedCount: 0, errors };
    }

    // Check bookmark limits for each content source/type combination
    const uniqueCombinations = new Set(bookmarks.map((b) => `${b.contentSource}:${b.contentType}`));

    for (const combination of uniqueCombinations) {
      const [contentSource, contentType] = combination.split(':');
      const existingCount = await this.countBookmarks({
        memberId,
        contentSource,
        contentType,
        contentId: '', // Not used in count query
      });

      const newBookmarksForCombination = bookmarks.filter(
        (b) => b.contentSource === contentSource && b.contentType === contentType,
      ).length;

      if (
        existingCount + newBookmarksForCombination >
        BookmarkService.BOOKMARK_LIMIT_BY_CONTENT_SOURCE_AND_TYPE
      ) {
        throw new TooManyBookmarksException(
          `Adding ${newBookmarksForCombination} bookmarks would exceed limit for ${contentSource}/${contentType}`,
        );
      }
    }

    // Use transaction for batch operations
    const connection = await this.#dbPool.getConnection();
    try {
      await connection.beginTransaction();

      // Prepare values for batch insert
      const values = bookmarks.map((bookmark) => [
        bookmark.memberId,
        bookmark.contentSource,
        bookmark.contentType,
        bookmark.contentId,
        user?.globalAuthId || bookmark.memberId,
        bookmark.createDate || new Date(),
      ]);

      const [result] = await connection.query<OkPacket>(
        `INSERT INTO bookmark (member_id, content_source, content_type, content_id, create_user, create_date)
         VALUES ?
         ON DUPLICATE KEY UPDATE
         update_user = VALUES(create_user),
         update_date = NOW()`,
        [values],
      );
      await connection.commit();
      savedCount = result.affectedRows;

      // Send events for each saved bookmark if user info is provided
      if (user && savedCount > 0) {
        for (const bookmark of bookmarks) {
          try {
            await this.#profileEventService.sendBookmarkEvent(user, bookmark, 'create');

            // Also send subscription event for community posts
            if (bookmark.contentSource.startsWith('community') && bookmark.contentType === 'post') {
              await this.#profileEventService.sendSubscriptionEvent(user, bookmark, true);
            }
          } catch (error) {
            this.#logger?.errorMeta('Failed to send bookmark creation event in batch operation', {
              error: (error as Error).message,
              memberId: user.globalAuthId,
              bookmark,
            });
            errors.push(
              `Failed to send event for bookmark ${bookmark.contentSource}/${bookmark.contentType}/${bookmark.contentId}: ${(error as Error).message}`,
            );
          }
        }
      }

      return { savedCount, errors };
    } catch (error) {
      await connection.rollback();
      this.#logger?.errorMeta('Failed to batch save bookmarks', {
        error: (error as Error).message,
        memberId,
        bookmarkCount: bookmarks.length,
      });
      throw error;
    } finally {
      connection.release();
    }
  }

  // Internal/batch endpoints for other services

  /**
   * Get bookmarks by specific content (internal batch endpoint)
   * Used by other services to find who bookmarked specific content
   */
  async getBookmarksByContent(
    contentSource: string,
    contentType: string,
    contentId: string,
    pageIndex: number = 0,
    pageSize: number = 100,
  ): Promise<BookmarkRow[]> {
    this.#logger?.debugMeta('Getting bookmarks by content', {
      contentSource,
      contentType,
      contentId,
      pageIndex,
      pageSize,
    });

    const offset = pageIndex * pageSize;

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(
      `SELECT * FROM bookmark
       WHERE content_source = ?
       AND content_type = ?
       AND content_id = ?
       ORDER BY create_date DESC
       LIMIT ? OFFSET ?`,
      [contentSource, contentType, contentId, pageSize, offset],
    );

    return rows;
  }

  /**
   * Get bookmarks by content type (internal batch endpoint)
   * Used by other services to find all bookmarks of a specific type
   */
  async getBookmarksByContentType(
    contentSource: string,
    contentType: string,
    pageIndex: number = 0,
    pageSize: number = 100,
  ): Promise<BookmarkRow[]> {
    this.#logger?.debugMeta('Getting bookmarks by content type', {
      contentSource,
      contentType,
      pageIndex,
      pageSize,
    });

    const offset = pageIndex * pageSize;

    const [rows] = await this.#dbPool.query<BookmarkRow[]>(
      `SELECT * FROM bookmark
       WHERE content_source = ?
       AND content_type = ?
       ORDER BY create_date DESC
       LIMIT ? OFFSET ?`,
      [contentSource, contentType, pageSize, offset],
    );

    return rows;
  }

  /**
   * Delete bookmarks by content (internal batch endpoint)
   * Used when content is removed from the system
   */
  async deleteBookmarksByContent(
    contentSource: string,
    contentType: string,
    contentId: string,
  ): Promise<number> {
    this.#logger?.debugMeta('Deleting bookmarks by content', {
      contentSource,
      contentType,
      contentId,
    });

    const [result] = await this.#dbPool.query<OkPacket>(
      `DELETE FROM bookmark
       WHERE content_source = ?
       AND content_type = ?
       AND content_id = ?`,
      [contentSource, contentType, contentId],
    );

    return result.affectedRows;
  }
}

export default BookmarkService;
