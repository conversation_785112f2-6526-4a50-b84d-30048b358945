/**
 * Bookmark SQL Queries
 *
 * This file contains all SQL queries used by the bookmark service as string constants.
 * This replaces the individual SQL files that were previously read from disk at runtime.
 */

export const Queries = {
  /**
   * Count bookmarks for a member with specific content source and type
   */
  countBookmarks: `
SELECT COUNT(*) AS count
    FROM bookmark
    WHERE member_id = :memberId
      AND content_source = :contentSource
      AND content_type = :contentType;
  `,

  /**
   * Insert a new bookmark
   */
  insertBookmark: `
INSERT INTO bookmark (member_id, content_source, content_type, content_id, create_date, update_date, create_user, update_user)
    VALUES (:memberId, :contentSource, :contentType, :contentId,
            COALESCE(:createDate, NOW()), COALESCE(:updateDate, NOW()),
            COALESCE(:createUser, :memberId), COALESCE(:updateUser, :memberId));
  `,

  /**
   * Delete a bookmark
   */
  deleteBookmark: `
DELETE FROM bookmark
    WHERE member_id = :memberId
        AND content_source = :contentSource
        AND content_type = :contentType
        AND content_id = :contentId;
  `,

  /**
   * List all bookmarks for a member with specific content source and type
   */
  listBookmarks: `
SELECT id, member_id, content_source, content_type, content_id, create_date, update_date, create_user, update_user
    FROM bookmark
    WHERE member_id = :memberId
        AND content_source = :contentSource
        AND content_type = :contentType;
  `,

  /**
   * List paginated bookmarks for a member with specific content source and type
   */
  listBookmarksPaginated: `
SELECT id, member_id, content_source, content_type, content_id, create_date, update_date, create_user, update_user
FROM bookmark
WHERE member_id = :memberId
  AND content_source = :contentSource
  AND content_type = :contentType
  LIMIT :pageSize OFFSET :pageOffset;
  `,

  /**
   * Get a single bookmark
   * Note: This was previously defined directly in the bookmark-service.ts file
   */
  getBookmark: `
SELECT * FROM bookmark
WHERE member_id = ?
AND content_source = ?
AND content_type = ?
AND content_id = ?
LIMIT 1
  `,
};
