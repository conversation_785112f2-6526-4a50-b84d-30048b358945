import fs from 'fs/promises';
import fetchAwsSecret from '../../secrets/fetch-aws-secret';
import { ConfigData } from '../../config/environment';

export enum SignatureType {
  AWS_SECRET = 'aws-secret',
  FILE = 'file',
  TEXT = 'text',
}

async function loadFileContent(filePath: string): Promise<string> {
  try {
    return await fs.readFile(filePath, 'utf-8');
  } catch (error) {
    throw new Error(`Error reading file at ${filePath}: ${error}`);
  }
}

export function parseAuthSignatureTypeValue(
  signature: string | undefined,
  env: ConfigData['envName'],
): { type: SignatureType; value: string } {
  if (!signature) throw new Error('BC_AUTH_SIGNATURE is missing');

  const index = signature.indexOf(':');
  if (index < 0) {
    throw new Error('Invalid BC_AUTH_SIGNATURE format');
  }

  const rawType = signature.slice(0, index);
  const value = signature.slice(index + 1);

  const type = rawType as SignatureType;
  if (!Object.values(SignatureType).includes(type)) {
    throw new Error('Invalid BC_AUTH_SIGNATURE format');
  }

  if ((env === 'prod' || env === 'stag') && type !== SignatureType.AWS_SECRET) {
    throw new Error(
      'Only aws-secret type is allowed in prod and stag environments for BC_AUTH_SIGNATURE',
    );
  }

  return { type, value };
}

async function parseAuthSignature(
  signature: string | undefined,
  env: ConfigData['envName'],
): Promise<string | undefined> {
  if (!signature) throw new Error('BC_AUTH_SIGNATURE is missing');

  const { type, value } = parseAuthSignatureTypeValue(signature, env);

  switch (type) {
    case SignatureType.AWS_SECRET:
      return await fetchAwsSecret(value);

    case SignatureType.FILE:
      if (env !== 'qa' && env !== 'dev') {
        throw new Error(
          'File-based signature is allowed only in qa and dev environments for BC_AUTH_SIGNATURE',
        );
      }
      return await loadFileContent(value);

    case SignatureType.TEXT:
      if (env !== 'qa' && env !== 'dev') {
        throw new Error(
          'Text-based signature is allowed only in qa and dev environments for BC_AUTH_SIGNATURE',
        );
      }
      return value;

    default:
      throw new Error('Unsupported BC_AUTH_SIGNATURE type');
  }
}

export async function resolveBcAuthSignature(
  signature: string | undefined,
  env: ConfigData['envName'],
): Promise<string | undefined> {
  const normalizedEnv = env.toLowerCase();
  const validEnvs = ['prod', 'stag', 'qa', 'dev'];

  if (!validEnvs.includes(normalizedEnv)) {
    throw new Error(`Invalid environment: ${env}`);
  }

  try {
    const parsedSignature = await parseAuthSignature(signature, env);
    return parsedSignature?.replace(/\\n/g, '\n');
  } catch (error) {
    if (error instanceof Error) {
      if (normalizedEnv === 'prod' || normalizedEnv === 'stag') {
        throw new Error(`Failed to load BC_AUTH_SIGNATURE: ${error.message}`);
      }
      console.warn(`BC_AUTH_SIGNATURE warning in ${env} mode: ${error.message}`);
    } else {
      if (normalizedEnv === 'prod' || normalizedEnv === 'stag') {
        throw new Error(`Failed to load BC_AUTH_SIGNATURE: ${error}`);
      }
      console.warn(`BC_AUTH_SIGNATURE warning in ${env} mode: Unknown error`);
    }
    return undefined;
  }
}
