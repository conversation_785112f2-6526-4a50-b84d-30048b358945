import * as jwt from 'jsonwebtoken';
import AppLogger from '../../logs/app-logger';
import BcUser from './bc-user';
import StatsdService, { Metric } from '../../statsd/statsd-service';

const DOMAIN_TO_AUDIENCE_MAP: Record<string, string | undefined> = {
  // Notification Service
  'notifications-qa.babycenter.com': 'notification-service',
  'notifications-stag.babycenter.com': 'notification-service',
  'notifications.babycenter.com': 'notification-service',

  // Registry Service
  'api-registry.babycenter.com': 'registry-service',
  'api-registry.whattoexpect.com': 'registry-service',

  // Photo Service
  'bc-image-service-qa.babycenter.com': 'photo-service',
  'bc-image-service-stag.babycenter.com': 'photo-service',
  'bc-image-service.babycenter.com': 'photo-service',

  // Bookmark Service
  'qa-bookmark-service.babycenter.com': 'bookmark-service',
  'stag-bookmark-service.babycenter.com': 'bookmark-service',
  'bookmark-service.babycenter.com': 'bookmark-service',

  localhost: 'bookmark-service',
  'localhost:4000': 'bookmark-service',
};

export default class BcAuthService {
  #metrics: StatsdService;
  #logger: AppLogger;
  #bcAuthSignatureSecret: string;
  #bcAuthBaseUrl: string;

  constructor(
    metrics: StatsdService,
    logger: AppLogger,
    bcAuthSignatureSecret: string,
    bcAuthBaseUrl: string,
  ) {
    this.#metrics = metrics;
    this.#logger = logger;
    this.#bcAuthSignatureSecret = bcAuthSignatureSecret;
    this.#bcAuthBaseUrl = bcAuthBaseUrl;
  }

  public async getMemberInfo(globalAuthId: string, bcAuthToken: string): Promise<BcUser> {
    const memberInfoResponse = await fetch(`${this.#bcAuthBaseUrl}/profile/member/{globalAuthId}`, {
      method: 'GET',
      headers: {
        Authorization: bcAuthToken,
      },
    });

    if (!memberInfoResponse.ok) {
      throw new Error(`Failed to get member info for ${globalAuthId}`);
    }

    const memberInfo = await memberInfoResponse.json();
    return {
      globalAuthId: globalAuthId,
      email: memberInfo.member.email,
      memberId: memberInfo.member.id,
      scope: [],
    };
  }

  public validate(
    domain: string,
    bcAuthToken: string,
    now: number = new Date().getTime(),
  ): BcAuthServiceValidatePayload {
    try {
      const jwtPayload = jwt.decode(bcAuthToken, { json: true });
      if (!jwtPayload) {
        this.#logger.error('missing jwt payload');
        this.#metrics.increment(Metric.AUTH_BC_MISSING_JWT_PAYLOAD);
        return { valid: false, reason: 'other' };
      }

      const audience = DOMAIN_TO_AUDIENCE_MAP[domain];

      try {
        jwt.verify(bcAuthToken, this.#bcAuthSignatureSecret, {
          clockTimestamp: now / 1000,
          audience,
          algorithms: ['RS256'],
        });
      } catch (ex: unknown) {
        this.#logger.error(`exception on verify: ${JSON.stringify(ex)}`);
        if (ex instanceof Error) {
          if (ex.name === 'TokenExpiredError') {
            this.#logger.debug(`token expired`);
            this.#metrics.increment(Metric.AUTH_BC_EXPIRED_TOKEN);
            return { valid: false, reason: 'expired' };
          }
          if (ex.name === 'JsonWebTokenError' && ex.message?.startsWith('jwt audience invalid')) {
            this.#logger.info(`invalid audience`);
            this.#metrics.increment(Metric.AUTH_BC_INVALID_AUDIENCE);
            return { valid: false, reason: 'audience' };
          }
        }
        this.#logger.error(`exception on verify: ${JSON.stringify(ex)}`);
        this.#metrics.increment(Metric.AUTH_BC_EXCEPTION_ON_VERIFY);
        return { valid: false, reason: 'other' };
      }

      const { sub, scope } = jwtPayload;
      if (!sub) {
        this.#logger.error(`token with no subject`);
        this.#metrics.increment(Metric.AUTH_BC_TOKEN_WITHOUT_SUBJECT);
        return { valid: false, reason: 'other' };
      }

      const user: BcUser = { globalAuthId: sub, scope: scope };
      this.#metrics.increment(Metric.AUTH_BC_VALID);
      return { valid: true, user };
    } catch (ex: unknown) {
      this.#logger.error(`exception on validate: ${JSON.stringify(ex)}`);
      this.#metrics.increment(Metric.AUTH_BC_EXCEPTION_ON_VALIDATE);
      return { valid: false, reason: 'other' };
    }
  }
}

interface BcAuthServiceValidPayload {
  valid: true;
  user: BcUser;
}

interface BcAuthServiceInvalidPayload {
  valid: false;
  reason: 'expired' | 'audience' | 'other';
}

export type BcAuthServiceValidatePayload = BcAuthServiceValidPayload | BcAuthServiceInvalidPayload;
