import express from 'express';
import {
  InvalidTokenError,
  MissingAuthHeaderError,
  TokenMismatchError,
} from '../exceptions/auth-exceptions';
import BcUser from './bc/bc-user';

export function validateAuthorizationHeader(req: express.Request, globalAuthId?: string): BcUser {
  const { bcAuthService } = req.di;
  const domainName = req.hostname ?? req.headers['host'] ?? '';
  const authorizationHeader =
    ((req.headers['Authorization'] ?? req.headers['authorization']) as string) ?? '';

  if (!authorizationHeader) {
    throw new MissingAuthHeaderError();
  }

  const token = authorizationHeader.startsWith('Bearer ')
    ? authorizationHeader.split(' ')[1]
    : authorizationHeader;

  const validate = bcAuthService.validate(domainName, token, Date.now());
  if (!validate.valid) {
    throw new InvalidTokenError(validate.reason);
  }
  if (!globalAuthId) {
    globalAuthId = req.params.memberId;
  }

  if (validate.user.globalAuthId !== globalAuthId) {
    throw new TokenMismatchError(validate.user.globalAuthId, globalAuthId);
  }

  return validate.user;
}
