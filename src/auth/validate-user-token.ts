import express from 'express';
import { AuthResponseCode } from './auth-responses';
import BcUser from './bc/bc-user';

const BC_AUTHORIZATION_HEADER = 'Authorization';

const BC_AUTH_HEADERS = [BC_AUTHORIZATION_HEADER] as const;

type BcAuthHeader = (typeof BC_AUTH_HEADERS)[number];

type BcUserToken = {
  productId: 'bc';
  userInfo: BcUser;
  memberId: string;
  bcAuthHeader: BcAuthHeader;
};

type ValidUserTokenPayload = BcUserToken;

type InvalidUserTokenPayload = {
  error: AuthResponseCode;
};

export type ValidateUserTokenPayload = ValidUserTokenPayload | InvalidUserTokenPayload;

function validateBcUserTokens(
  bcAuthHeader: string,
  bcAuthTokens: string[],
  req: express.Request,
): ValidateUserTokenPayload {
  if (bcAuthTokens.length !== 1) {
    // We can't handle more than one token
    req.di.logger.error('request with multiple BC tokens');
    return { error: AuthResponseCode.MULTIPLE_AUTH_TOKENS };
  }

  const host = req.get('host');
  const origin = req.get('origin');
  const domain = origin || host || '';

  const [authHeaderValue] = bcAuthTokens;

  // Extract token from "Bearer <token>" format
  if (!authHeaderValue.startsWith('Bearer ')) {
    req.di.logger.error('Authorization header does not start with "Bearer "');
    return { error: AuthResponseCode.INVALID_AUTH_TOKEN };
  }

  const token = authHeaderValue.substring(7); // Remove "Bearer " prefix
  const validateResult = req.di.bcAuthService.validate(domain, token);
  if (!validateResult.valid) {
    const invalidResult = validateResult as { reason: string; valid: false };
    if (invalidResult.reason === 'expired') {
      return { error: AuthResponseCode.EXPIRED_AUTH_TOKEN };
    }
    if (invalidResult.reason === 'audience') {
      return { error: AuthResponseCode.INCORRECT_AUTH_TOKEN_AUDIENCE };
    }
    return { error: AuthResponseCode.INVALID_AUTH_TOKEN };
  }
  const { user } = validateResult;
  return {
    productId: 'bc',
    memberId: user.globalAuthId,
    userInfo: user,
    bcAuthHeader: BC_AUTHORIZATION_HEADER,
  };
}

export const validateUserToken = (req: express.Request): ValidateUserTokenPayload => {
  const bcAuthHeader = 'Authorization';

  // get authorization or Authorization case insensitive
  const authHeader = req.headers[bcAuthHeader] ?? req.headers[bcAuthHeader.toLowerCase()];

  // Convert to array format (for compatibility with existing validation function)
  const bcAuthTokens = authHeader ? [authHeader].flat() : [];

  const hasBcAuthToken = !!bcAuthTokens?.length;

  if (hasBcAuthToken) {
    return validateBcUserTokens(bcAuthHeader, bcAuthTokens, req);
  }

  // No tokens to validate
  return { error: AuthResponseCode.NO_AUTH_TOKEN };
};
