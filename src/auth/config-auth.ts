import { validateUserToken } from './validate-user-token';
import BcAuthService from './bc/bc-auth-service';
import { ConfigData } from '../config/environment';
import { resolveBcAuthSignature } from './bc/resolve-bc-auth-signature';
import { RequestScopeRegister } from '../awilix/container-scope';

export default async function configAuth(
  configs: ConfigData,
): Promise<Pick<RequestScopeRegister, 'bcAuthService' | 'user'>> {
  // Load BC secrets
  console.log('Loading BC secrets for auth config...');
  const { bcAuthSignature, envName, bcAuthBaseUrl } = configs;
  const bcAuthSignatureSecret = await resolveBcAuthSignature(bcAuthSignature, envName);

  console.log('Done.\n');

  return {
    bcAuthService: bcAuthSignatureSecret
      ? ({ logger, metrics }): BcAuthService => {
          return new BcAuthService(metrics, logger, bcAuthSignatureSecret, bcAuthBaseUrl);
        }
      : (): never => {
          throw new Error(
            `Missing or invalid BC_AUTH_SIGNATURE. Environment: ${envName}. Cannot accept BC auth tokens`,
          );
        },

    user: ({ request, logger }): ReturnType<typeof validateUserToken> => {
      const validateResult = validateUserToken(request);
      logger.infoMeta('user', validateResult);
      return validateResult;
    },
  };
}
