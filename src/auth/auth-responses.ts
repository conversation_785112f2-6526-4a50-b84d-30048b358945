import express from 'express';

export enum AuthResponseCode {
  MULTIPLE_AUTH_TOKENS = 'MULTIPLE_AUTH_TOKENS',
  EXPIRED_AUTH_TOKEN = 'EXPIRED_AUTH_TOKEN',
  INVALID_AUTH_TOKEN = 'INVALID_AUTH_TOKEN',
  INCORRECT_AUTH_TOKEN_AUDIENCE = 'INCORRECT_AUTH_TOKEN_AUDIENCE',
  NO_AUTH_TOKEN = 'NO_AUTH_TOKEN',
}

const HTTP_STATUS_400_BAD_REQUEST = 400;
const HTTP_STATUS_401_UNAUTHORIZED = 401;
const HTTP_STATUS_403_FORBIDDEN = 403;

const authResponseConfigs: {
  readonly [key in AuthResponseCode]: { status: number; message: string };
} = {
  [AuthResponseCode.MULTIPLE_AUTH_TOKENS]: {
    status: HTTP_STATUS_400_BAD_REQUEST,
    message: 'bn-multiple-auth-tokens',
  },
  [AuthResponseCode.EXPIRED_AUTH_TOKEN]: {
    status: HTTP_STATUS_401_UNAUTHORIZED,
    message: 'bn-expired-auth-token',
  },
  [AuthResponseCode.INVALID_AUTH_TOKEN]: {
    status: HTTP_STATUS_401_UNAUTHORIZED,
    message: 'bn-invalid-auth-token',
  },
  [AuthResponseCode.INCORRECT_AUTH_TOKEN_AUDIENCE]: {
    status: HTTP_STATUS_403_FORBIDDEN,
    message: 'bn-incorrect-auth-token-audience',
  },
  [AuthResponseCode.NO_AUTH_TOKEN]: {
    status: HTTP_STATUS_401_UNAUTHORIZED,
    message: 'bn-no-auth-token',
  },
};

export default function authResponse(res: express.Response, code: AuthResponseCode): void {
  const { status, message } = authResponseConfigs[code];
  res.status(status).send(message);
}
