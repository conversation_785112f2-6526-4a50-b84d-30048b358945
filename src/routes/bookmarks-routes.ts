import express from 'express';
import {
  save<PERSON><PERSON>markHand<PERSON>,
  deleteBookmarkHandler,
  getBookmarkHandler,
  listBookmarkHandler,
  listPaginatedBookmarkHandler,
  batchDeleteBookmarksHandler,
  listAllBookmarksByMemberHandler,
  listBookmarksByMemberAndSourceHandler,
  batchSaveBookmarksHandler,
  getBookmarksByContentHandler,
  getBookmarksByContentTypeHandler,
  deleteBookmarksByContentHandler,
} from '../handlers/bookmarks-handlers';
import { wrapHandler } from './utils/route-wrapper';

export function createBookmarksRoutes(): express.Router {
  const router = express.Router();

  // Main bookmark CRUD routes - matching Java service patterns
  router
    .route('/bookmark/v1/member/:memberId/content/:contentSource/:contentType/:contentId')
    .get(wrapHandler('getBookmark', getBookmarkHandler))
    .post(wrapHandler('saveBookmark', saveBookmarkHandler))
    .delete(wrapHandler('deleteBookmark', deleteBookmarkHandler));

  // List bookmarks routes
  router.get(
    '/bookmark/v1/member/:memberId/content/:contentSource/:contentType',
    wrapHandler('listBookmarks', listBookmarkHandler),
  );

  // Paginated list (v2 for pagination)
  router.get(
    '/bookmark/v2/member/:memberId/content/:contentSource/:contentType',
    wrapHandler('listPaginatedBookmarks', listPaginatedBookmarkHandler),
  );

  // Batch delete multiple bookmarks by content IDs
  router.delete(
    '/bookmark/v1/member/:memberId/content/:contentSource/:contentType',
    wrapHandler('batchDeleteBookmarks', batchDeleteBookmarksHandler),
  );

  // Member-specific routes
  router.get(
    '/bookmark/member/:memberId',
    wrapHandler('listAllBookmarksByMember', listAllBookmarksByMemberHandler),
  );

  router.get(
    '/bookmark/member/:memberId/content/:contentSource',
    wrapHandler('listBookmarksByMemberAndSource', listBookmarksByMemberAndSourceHandler),
  );

  // Additional single bookmark save route
  router.post(
    '/bookmark/v1/member/:memberId/content',
    wrapHandler('saveBookmark', saveBookmarkHandler),
  );

  // Batch save bookmarks
  router.post(
    '/bookmark/member/:memberId',
    wrapHandler('batchSaveBookmarks', batchSaveBookmarksHandler),
  );

  // Internal/batch endpoints (use bsToken authentication)

  // Get bookmarks by specific content
  router.get(
    '/bookmark/v1/content/:contentSource/:contentType/:contentId',
    wrapHandler('getBookmarksByContent', getBookmarksByContentHandler),
  );

  // Get bookmarks by content type
  router.get(
    '/bookmark/v1/content/:contentSource/contentType/:contentType',
    wrapHandler('getBookmarksByContentType', getBookmarksByContentTypeHandler),
  );

  // Delete bookmarks when content is removed
  router.delete(
    '/bookmark/v1/content/:contentSource/:contentType/:contentId',
    wrapHandler('deleteBookmarksByContent', deleteBookmarksByContentHandler),
  );

  return router;
}
