import { Request, Response, RequestHandler } from 'express';

/**
 * Wraps route handlers with consistent error handling
 */
export function wrapHandler<T = Record<string, string>>(
  handlerName: string,
  handler: (req: Request<T>, res: Response) => Promise<void> | void,
): RequestHandler {
  return async (req: Request, res: Response) => {
    try {
      await handler(req as Request<T>, res);
    } catch (error) {
      console.error(`Error in ${handlerName} handler:`, error);
      res.status(500).send();
    }
  };
}
