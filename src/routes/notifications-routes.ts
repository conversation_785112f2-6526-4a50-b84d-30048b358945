import express from 'express';
import {
  v1ListUserNotifications<PERSON>andler,
  v1AcknowledgeNotificationsHandler,
  v2ListUserNotificationsHandler,
  v3ListUserNotificationsHandler,
  v2ReadNotificationHandler,
  v2ReadAllNotificationsHandler,
  v2AcknowledgeNotificationsHandler,
  v3AcknowledgeNotificationsHandler,
  v3ReadNotificationHandler,
  v3ReadAllNotificationsHandler,
  internalUserKeyHandler,
  internalV1NotificationsHandler,
} from '../handlers/notifications-handlers';
import { wrapHandler } from './utils/route-wrapper';

export function createNotificationsRoutes(): express.Router {
  const router = express.Router();

  // V1 API routes - matching Java service patterns (no auth required)
  router.get(
    '/v1/user/notifications',
    wrapHandler('v1ListUserNotifications', v1ListUserNotificationsHandler),
  );

  router.get(
    '/v1/user/acknowledge',
    wrapHandler('v1AcknowledgeNotifications', v1AcknowledgeNotificationsHandler),
  );

  // V2 API routes - with authorization (returns ListNotificationsResult)
  router.get(
    '/v2/user/notifications',
    wrapHandler('v2ListUserNotifications', v2ListUserNotificationsHandler),
  );

  router.get(
    '/v2/user/acknowledge',
    wrapHandler('v2AcknowledgeNotifications', v2AcknowledgeNotificationsHandler),
  );

  router.post('/v2/user/read', wrapHandler('v2ReadNotification', v2ReadNotificationHandler));

  router.post(
    '/v2/user/readall',
    wrapHandler('v2ReadAllNotifications', v2ReadAllNotificationsHandler),
  );

  // V3 API routes - dual user system support (returns ListNotificationsResult)
  router.get(
    '/v3/user/notifications',
    wrapHandler('v3ListUserNotifications', v3ListUserNotificationsHandler),
  );

  router.get(
    '/v3/user/acknowledge',
    wrapHandler('v3AcknowledgeNotifications', v3AcknowledgeNotificationsHandler),
  );

  router.post('/v3/user/read', wrapHandler('v3ReadNotification', v3ReadNotificationHandler));

  router.post(
    '/v3/user/readall',
    wrapHandler('v3ReadAllNotifications', v3ReadAllNotificationsHandler),
  );

  // Internal admin endpoints
  router.get(
    '/internal/userkey/:userKey',
    wrapHandler('internalUserKeyEndpoint', internalUserKeyHandler),
  );

  router.get(
    '/internal/v1/user/notifications',
    wrapHandler('internalV1Notifications', internalV1NotificationsHandler),
  );

  return router;
}
