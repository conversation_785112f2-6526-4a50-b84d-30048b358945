# Smart Messaging

## Context

Registry Builder is a subdomain for BC and WTE which allows users to curate their own baby registries of products they wish to own.

### Checklists 

One of the features of the registry is a "checklist". This checklist is generated for a user through the taking of a quiz. The output of the quiz is a collection of suggested categories and subcategories of products that we recommend for the user. Each category and subcategory points to a "Product Roundup" guide to products of that category that we recommend. The user can refer to the checklist and the product guides to see what types of products are suggested for them, and they can "check off" certain entries in the checklist once the user decides that the checklist item no longer applies to them.

A checklist item can be checked off when:
- The user adds a product or products of that category to their registry, indicating that they are no longer determining which product of that category they are looking for
- The user buys or receives a product of that category, indicating they no longer wish it to be part of their registry
- The user decides they have no interest in a product of that category

There is currently no functionality that will automatically check off items on the checklist based off the contents of the user's registry, nor are we wishing for that functionality, due to the variety of ways people use checklists. For instance, if a user puts a stroller on their registry, they may not want to check off "Registry" on their checklist, as they may wish to also add additional strollers to their Registry.

Another reason we don't automatically sync the checklist with our registries is because of Amazon's Terms & Conditions. The bulk of our registry products are Amazon products, and we are not allowed to aggregate Amazon registry data for the purposes of product recommendations. If we synced registry content with checklists, we would be making recommendations based off of the contents of the user's registry.

### Prices

Products on registries have prices, which we gather in a variety of ways. For Target, we query their API, and for Amazon, we receive a high-volume feed from Amazon of when registry contents change.

*Question: Do we receive registry updates merely for price changes? Can we be certain registry prices are up to date?*

Separately, we also have price update integration in our Product Catalog, but these are only for editorially-curated products, and not necessarily all products that are reflected in our registry.

In either case, for Amazon, we are prohibited from aggregating price data or storing price history.

## Problem

We wish to increase engagement of our registry visitors, so that when they visit our registry subdomains, they are visually prompted to take certain actions that we believe will increase their engagement, and thus CTR, conversion, and revenue.

For the purposes of this project, we wish to display "smart messages", which are defined as visual panels on the registry website. A "smart message" should:

- be displayed one at a time
- be dismissable by the user
- include a clickable call to action

A "smart message" does *not* suggest any functionality such as a message center, an onsite notification system like a "bell icon", or any kind of external messaging through push notifications or email.

Along with other styles of messages that don't need further definition in this document, we wish to spur more registry users to include our recommended products on their registries, and we wish to drive conversion by informing users of price drops.

## Requirements

### Product Recommendations

In order to "smart message" users effective product recommendations, we need to compare a user's registry content with the state of their checklist:

- We wish to recommend categories (and product guides) for any categories that *don't* exist on the registry, *exist* on the checklist, and are *not* checked.

- For products that *do* exist on the registry, *exist* on the checklist, and are *not* checked, we wish to encourage the user to check the item off their checklist.

- We should store history, so that after a smart message is dismissed, we don't make the same recommendation for that category, for that user, again.

### Prices

In order to "smart message" users news of a price drop, we need to store a price history of the most recent price before the current price, for that product.

We would need to define a rule such that if the new price is more than x% below the prior price, a message will be displayed if the user visits the site during that time.

We would need to define a time period for how long that price difference would be relevant. For instance, if the "previous price" was from a month ago, perhaps we wouldn't want the product to be eligible for a price drop "smart message".

### Prioritization

A user might be eligible to receive multiple smart messages, and yet only one should be displayed at a time. Smart messages could therefore be delivered as a stack, and client-side logic would control whether to immediately display the next message on the stack after the first is dismissed, or if there would be a delay.

## Specification

This design would require several alterations in our legal agreement with Amazon, including:

1. Ability to aggregate registry products for the purpose of product recommendations

2. Ability to store price history

Assuming those agreements are reached, then we need to build capability to categorize registry products by registry checklist categories, and to store price history, either on the registry side or the product catalog side. If price history is stored on the product catalog side, then it means we would also need to store registry products in the product catalog.

TBD:
- Whether to categorize on registry side or PC side
- Whether to store price history on registry side or PC side

