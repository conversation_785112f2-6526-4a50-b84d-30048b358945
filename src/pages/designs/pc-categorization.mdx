import { LikeC4View } from '../../generated/likec4.generated';

# Arc Design: Product Catalog Categories

## Context

We currently have a product catalog, which is a GraphQL Subgraph behind our Federated GraphQL system.
This contains product data that we already use for some production purposes, and wish to use for more
business purposes in the future.

<LikeC4View viewId="productCatalogSystem" />

We also have a registry builder system. Users add products to registries on retailer sites, and we
sync with those sites to retrieve registry data. Then we display these combined registries to the users
to encourage them to buy products. We receive affiliate revenue from these purchases.

<LikeC4View viewId="registrySystem" className="like-med" />

## Problem

Several of our future product ideas require a notion of categorization. Here are some of the use cases
we've identified:

- In Registry, we wish to know when a person has added a product of a certain category, so we can
prompt them to check that category off their "registry checklist".
- For semantic recommendations, we wish to have a product-based vector db that reads information like
product name and product description, and encodes that semantic understanding. In that database, we
also would like to retain metadata like categories to help with querying.

Without an understanding of product categorization, we are prevented from implementing feature
ideas such as these. With categories, it opens up possibilities for these feature ideas and more in the future.

## Requirements

One problem we've identified is that different product needs require different taxonomies.

Rather than go through the exercise of determining how to assign products to categories for each taxonomy,
we've elected to start with an internal "Uber-Taxonomy". This taxonomy has been defined through several
rounds of refinement by looking at CMS page categories, LLM advice, retailer taxonomies, and other sources.

This "Uber-Taxonomy" is granular, with categories and subcategories. We believe we can use it as a rosetta stone,
and map from it to whatever other feature-specific taxonomies we may wish to support in the future.

Since we want to do this, we cannot support arbitrary category/subcategory names that 
may drift over time, as it would impede our ability to translate to other coarser 
taxonomies later. Therefore, for each product in the product catalog, we want to 
assign a category and subcategory from this defined uber-taxonomy, and store it 
against predefined `category` and `subcategory` tables in our database.

In addition, we also wish to categorize a subset of products from our Registry system, and send those products
to the product catalog, as part of our ongoing efforts to centralize our product-oriented technologies around the
Product Catalog.

### Non-Requirements

The first version of this project will not support "recategorization".
This means we assume the initial "uber-taxonomy" will remain static. If
changes are needed in the uber-taxonomy, it would require a separate project
to either support category migrations, or a full truncation and re-categorization
effort.

We will not support a sku having multiple categories beyond one category and one subcategory.

## Specification

We currently have a process that loads products into the product catalog. This will be adapted to also look
up product categorization from the LLM classifier:

<LikeC4View viewId="deProductLoading" />

In order for the Product Loading DAG to be able to submit compliant categories, 
this will be supported by a new query against the product catalog that will 
return a json representation of all categories/subcategories and their IDs:

```

query {
  categories {
    id
    name
    subcategories {
      id
      name
    }
  }
}
```

The mutation resolver should then check the submitted skus to validate that 
the submitted subcategories are members of the indicated category.

**(Note: Work to import/categorize registry products into product 
catalog is currently paused, pending legal questions.)**

In addition, we will need to add a process that categorizes some number of registry products, and adds
them to the Product Catalog, via the same mutation:

<LikeC4View viewId="deRegistryClassifying" />

The Product Catalog subgraph will be updated to accept both category and subcategory, and will expose
the category and subcategory in queries for products.

DE will probably have to run a one-time process against the product catalog database to add categories for
existing products, beyond the new products that will be inserted.

On the product catalog side, categories will be stored in a category table 
with an `id`, and subcategories will be stored in a subcategory table with an 
`id` and `category_id`. The sku table will be expanded to support both 
`category_id` and `subcategory_id`. `subcategory_id` may be nullable if 
products can be assigned categories without subcategories.

### Remaining questions:

- Do we wish to support upsert for categories? Or insert only?
- We also have a need to identify checklist categories for registry products, perhaps far more than 500-1500.
Should that be folded in to this effort?
- Do we need to support revisions in category and subcategory? Wholesale changes could be difficult, compared
to incremental migrations of adding/removing single categories/subcategories at a time.

