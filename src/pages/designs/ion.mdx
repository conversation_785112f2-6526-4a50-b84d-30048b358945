import { LikeC4View } from '../../generated/likec4.generated';

# Ion

## Context

Ion is a platform for hosting and building interactive web pages.
This is used by the marketing team and WTE web team to build pages for sweepstake and survey registrations.
They are multi-page flows, which are referred to as “Creatives”.

The **hierarchy** of Ion is the following. Each level has as a one-to-many relationship with the next.
1. Portfolio
2. Campaign
3. Creative
4. Page

There are 3 “active” Portfolios:
* Babycenter
* Everyday Health - Leadgen
* What To Expect (2019)


There are 3 “active” Campaigns:
* Babycenter/Viacord Survey
* Everyday Health - Leadgen/WTE Sweeps
* What To Expect (2019)/WTE - VIacord Survey

Notably, Campaigns have Themes associated with them.
Themes are not be confused with templates, as the each page within a Creative is still different.

#### Ion Usage (Integrations)

Aside from building pages, there is also the integration side of Ion
These are actions that can be triggered by the Ion backend to send data to external sources.
Commonly this is done via CSV dumps or API calls.

The first part of integrations are the “Integration Field Mappings”.
These are effectively a reusable configuration of form fields for an export type
So multiple integrations can reference the same integration field mappings.

#### Ion Usage (Server Scriplets)

Scriptlets are code that can be injected into the backend processing.
Server Scriptlets start with “Scriplet Libraries”, which can be referenced by multiple Server Scriplets (1:many parent-child relationship).
A Server Scriplet can only reference a single Scriplet Library.
The Scriplets themselves are the actual javascript code. 


#### Ion Usage (Page Development)

It has a drag-n-drop style GUI for building webpages.
Building some pages are handled by the WTE web team (via in-platform code, while others by the marketing team (via GUI).

Fields within the page can be edited from the GUI as well.
This is done to change text and image urls by the marketing team.
For example, the “Baby Bump Giveaway” has a monthly winner which is manually updated by the marketing team.

Ion also supports A/B testing, so we can build similar pages and serve them from the same URL at random. 
This is configurable, such 50/50 or 70/30 between two page.
The values are inputted as integers between 0-10, adding to a total of 10.

For Campaigns, we are required to have sponsor approval.
The marketing team will send screenshots of pre-published (not live) pages for approval.
This can go through several iterations.
The sponsor’s can make requests, and the marketing team can be unsatisfied with the iteration and want to make more changes.
Currently tools like Figma are not utilized in the process, and marketing team will use the Ion GUI to design and develop on the fly.

Some pages might reference static assets.
For example, a user might be given a PDF file to download depending on how they branch in the flow. The marketing team can upload and manage these through Ion.
These are rare to change, often happens when a sponsor (campaign) changes branding.

## Problem

Working with Ion is very manual and error prone.
There is a cross-team element that increases siloed knowledge and collaboration complexity.
The publishing flow involves duplication of pages, which is manual and unintuitive. 
The process has been to manually maintain a spreadsheet to coordinate which page is the “source” to clone is very manual as its not a feature baked into the platform.

The registration/lead flows it provides are flagged as critical for our business and is a revenue stream, so this is something we want to be reliable and easy to maintain.

## Requirements
* Hierarchy support. We need some concept of a Portfolio (in practice this is essential the brand) > Creative (group of pages) > Page. 
  * Portfolio (brand) can support multiple Creatives can support multiple Pages.
  * Pages can flow into different pages, its not always page 1 → 2 → 3. 
* Theme support. We need to have themes that different Creatives can reference (common style rules applied to all pages in the Creative grouping, or across Creative groupings)
* A/B Testing. We must be able to support multiple page variations behind a single URL. This should be configurable, so we can support different weights for different variants (i.e. 75% chance for A and 25% for B).
* Publishing. We should be able to simply publish un-publish pages.
* Static files. We need to be able to host, replace, and deliver static files, like PDFs.
* Cloning. We must be able to clone a page (live or not) to create a new page using the other as a starting point.
* Backwards compatibility for the backend integrations. This includes calling the Standalone Campaign REST API and/or CSV upload for submissions.
* Environment isolation. We need to have it easy to separate environments such as staging and production.

## Specification

Start by cleaning up Ion platform in 4 phases.
1. Delete server scriplets and integrations that have zero references.
2. Archive (or preferably delete) campaigns and creatives that are live/draft but are completely unused.
3. Delete server scriplets and integrations that have zero references round 2 (more will appear without zombie pages).
4. Delete any remaining unused scripts or form configurations.