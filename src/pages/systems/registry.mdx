import { LikeC4View } from '../../generated/likec4.generated';

# Registry builder

I'll start with just documenting the "get registry" flow.

When the "get registry" endpoint is called, it invokes a lambda on the services side which uses several resources:

<LikeC4View viewId="registryGetRegistry" />

Note that the "Cache Populated" SNS message doesn't appear to go anywhere; I was unable to find any subscribers.

The other `RetailerRegistryMissing` SNS message triggers the `registry-update-from-retailer` DE lambda, as indicated above. The DE lambdas internally utilize both SNS and SQS, as charted here:

<LikeC4View viewId="registryDeMessages" />

However, that's not the only thing those lambdas do: they also reach in to repository resources that the Service lambdas also talk to:

<LikeC4View viewId="registryDeRepositories" />

## Open Considerations

This is complicated. :) The current approach arguably goes against best practices in terms of repositories being owned by multiple codebases (and multiple teams). 

Ideally, only Services would read and write the repositories, and DE would use an API layer. However, it's also possible that some registry *tables* in RDS (like `unlinked_registry`) were only intended to be owned by DE, and others only to be owned by Services. If that's true, perhaps they should be separated into separate databases; I think we risk confusion otherwise that could invite both teams to interoperate. (We might already have examples of Services registry interacting with this table directly.)
