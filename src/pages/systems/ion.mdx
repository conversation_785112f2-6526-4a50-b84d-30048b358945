import { LikeC4View } from '../../generated/likec4.generated';

# Ion

Just for arrow clarity, I decided to introduce "Ion Internal Data", and to make it clearer that it's a hosted solution that end users submit data to. (I'm not really sure what invokes the Scriplets/Integrations; the authoring side or the hosting side.) For the boundary, I decided to stop at the outward network connections to our integrations.

<LikeC4View viewId="ionProblem"  className="like-med" />

----

This next one is specifically centered around the ftp communication.

One thing I think would be good to add is what happens after Redshift... what's the thing that reads from Redshift and delivers to our clients? That seems like it would be the natural endpoint of this whole diagram(s).

<LikeC4View viewId="ionFtp" />

----

Here's the part of the standalone campaign api that has to do with users. One minor question I have is why does it write to the user DDB? I mean, besides the question of why it isn't going through the WTE User API - what's it actually doing?

As for the diagram, I think it would be good to add what else the `wte-user-leadgen` lambda is reaching out to. After it polls SQS, does it write to Redshift, or something else?

<LikeC4View viewId="ionUserEvents" />

----

The last bit is how the standalone campaign API triggers the whole lead-sweeps system. BTW, one thing you could try if you wanted more clarity in a zoomed-in graph is to add components to wte-lead-sweeps, one for each message type. That way you could focus on the individual flows to each client type (like huggies) if you wanted.

The larger question is what else hooks into the Lead-log MySQL DB. Is it a DE lambda that reads from it and writes to Redshift? Sends something to clients? Here too the natural endpoint would be the arrow that sends something to a client location I guess.

<LikeC4View viewId="ionSns" />

----

That's it... overall I think this is a somewhat decent example of how to break apart a larger diagram into a set of smaller diagrams that each tell part of the story. Network or team boundaries appear to be a natural part of where to choose to split the diagrams.

