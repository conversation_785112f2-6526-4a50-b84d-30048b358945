import { LikeC4View } from '../generated/likec4.generated';

# ArcDoc

This site uses [likec4.dev](https://likec4.dev) to generate architecture diagrams. This allows us to document our entire architecture in one repository, and then create "views" of that architecture depending on what we want to show or exclude.

Here's an example:

<LikeC4View viewId="bcWeb" />

If you click on the above React component, you will be able to browse relationships between systems, containers, and components.

Please visit our [repository](https://github.com/babycenter/arcdoc) and suggest PRs if you would like to help expand our architecture.

## C4 Model

We are encouraging people to follow the [C4 Model](https://c4model.com/) for the diagrams, which, for our purposes, means focusing on the following three levels of architecture:

### Systems

It's a little hard to define systems without first defining the second level of "containers", as "containers" are deployable units of technology. A "system" is a group of containers that work together to provide value to the business.

For instance, here is a diagram of systems describing our Daily Deals feature:

<LikeC4View viewId="dailyDealsSystem" className="like-med" />

Note that it is high level, and doesn't go into the details of our various code deployments.

### Containers

As mentioned above, containers are deployable units of technology. For instance, here is a container diagram
of the Daily Deals system:


<LikeC4View viewId="dailyDealsContainers" className="like-med" />

Note that we have broken out the different deployable units. As containers are more in depth than systems, a specific graph may only focus on diagramming one part of the system.

### Components

Components are one step more zoomed in than containers. For example, within a service you may want to focus on a particular endpoint. Or within an SNS topic, you may want to focus on one particular message. Here is an example of the service side of
our "get Registry" flow from Registry Builder:

<LikeC4View viewId="registryGetRegistry" />

In the above view, "wte-registry-get-registry" is a component of the Registry Service, and the two SNS messages are components of that SNS Topic.

### Code

There is also a code level to the C4 model, but it is rarely recommended. It can include things like UML diagrams and class diagrams, and maybe even snippets of code. In practice, for our purposes, Systems and Containers will be most useful, and occasionally Component diagrams.

### Deployments

There's also a separate style of diagram for deployments that show infrastructure like regions and zones; examples will be added later.

## Instructions

**Note:** In general, you can refer to the [likec4 documentation](https://likec4.dev) to learn more about LikeC4 techniques. It's well-written and a quick read.

### Codebase

Please check out the [repository](https://github.com/babycenter/arcdoc).

The best way of working with the repository is to use VSCode, because there's an awesome extension named "LikeC4" that only works with VSCode. Please install the extension. Don't install other "C4" plugins, only the one named "LikeC4" is necessary.

When making code changes, please submit Pull Requests. David or I will review them and then suggest or make adjustments to fit existing conventions.

### Conventions

When drawing diagrams, your code changes will be limited to the `src/c4` folder. The LikeC4 system automatically reads from every file in this folder to construct the diagrams.

- **src/c4/spec.c4:**
    This is the initial setup for our conventions. Since we are following C4 conventions, we've defined three elements to which we'd like to restrict ourselves: **system**, **container**, and **component**. (See the C4 section above for definitions.) In addition, we've defined an "actor" element to represent the types of people that interact with our systems.

    You can also define "tags" here, if you want to [learn how to use them](https://likec4.dev).

- **src/models:**
    These contain our model files. Each file is intended to be a part of our architecture. The way we decide to separate which parts of our architecture goes into which file is somewhat arbitrary. Generally, each file should reflect a **system**, a portion of our architecture that feels like it "goes together". We are free to refactor this all liberally as we get more of a sense of what makes sense to separate or keep together.

    Each model contains two sections: the system definition, which can contain multiple containers and components, and the relationship section.

    There are many ways to structure relationships, but for our purposes, we are deciding on two clear conventions:
    - **Arrows are about initiation, not data flow:** 
        There are generally two ways to draw arrows in an architecture diagram; to either indicate a direction of data flow, or to indicate the initiation of a connection. For **all** our diagrams, we should use the *initiation* approach.

        For instance, if a service selects data from the database, the arrow should be drawn from the service to the database. The arrow should not be drawn from the database to the service indicating the selected data.

        Similarly, we should not draw arrows to indicate responses; only requests. A truly push-based system like SNS can initiate connections, but SQS is inherently pull-based. Therefore, you can draw an arrow from a service to SNS (indicating publishing a message), and from SNS to SQS or a lambda (indicating triggering behavior), but you should never draw an arrow from SQS to something else. Instead, you should draw an arrow from a lambda to SQS, since SQS is polled.

        <LikeC4View viewId="registryDeSnsSqs" />


    - **Add relationships to the initiating model:** 
        Each model's relationship section should only only include the relationships initiated from that model.

    Beyond that, refer to other existing models for many examples of how to construct systems, containers, components, and relationships.

    For each model, we strongly recommend adding supporting documentation via the "link" tag. The link can point to your GitHub repository for codebases, to AWS Console for resources, etc. Each element can have multiple link tags.

    One more pointer: Generally speaking, it is *usually* better to construct relationships between the most detailed elements the model supports. The diagrams will generally be able to interpret this correctly even when drawing abstract views.

- **src/views:**
  A view is an actual diagram. We'd never want to diagram our entire architecture all at once, because it would be too huge.

  A diagram is for a communicative purpose. This means you should keep in mind the audience for your diagram. You may want to create high-level and low-level diagrams of the same architecture.

  Creating a view is essentially defining which *parts* of our many models you want to include. Most of our views are very simple and should be readily apparent. 
  
  To start developing a view, definite the initial name, title, and description, and then click the "open preview" link above the view definition. (This requires the VSCode plugin.) Then 
  you can just start adding which parts of the model you'd like to include: the viewer window should auto-update.

### Publishing

There are a few ways to publish your diagram. The easiest way is just to stop there, and open a PR. Once it is merged to `main`, it will auto-deploy, and your diagram will be viewable through the [arcdoc viewer](https://arcdoc.babycenter.com/viewer/index.html#/). From there, you can find the href to your diagram, and share that as a url. You can also `npm run likec4:serve` if you want to see the viewer locally, but the VSCode plugin is usually enough.

If you'd prefer to refer to this in a wiki, while we don't have direct Confluence plugin integration (that'd be awesome), you can take a screenshot of your diagram and drop that in to your wiki document. For more interactivity, from within Confluence, you can create an iframe in your wiki document and point it to the viewer href of your diagram. Then, from within the Confluence wiki page, you will be able to zoom, click on components, explore relationships, etc.

Finally, if you don't want to deal with Confluence, you can create an article on arcdoc itself. Arcdoc supports markdown through MDX. To do this:

- start `npm run dev` to start your local nextjs to see the documentation output at http://localhost:3000/

- run `npm run gen` anytime you make c4 changes, so that the generated javascript is available to your markdown pages

- Write documentation in `src/pages` in markdown. Refer to the existing examples to see how to include diagrams inline, in the pages.

Ultimately, however, Confluence just supports more features, so you may prefer the iframe or screenshot route.

## Source of Truth

This site is intended to be the "source of truth" for our architecture across all our systems and teams. Please consider moving old architecture diagrams to this site as needed.
