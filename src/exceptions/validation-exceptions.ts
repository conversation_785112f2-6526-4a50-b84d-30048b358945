/**
 * Validation exceptions for request parameters and data
 */

export class ValidationError extends Error {
  public readonly statusCode: number;
  public readonly errorCode: string;

  constructor(message: string, statusCode = 400, errorCode = 'VALIDATION_ERROR') {
    super(message);
    this.name = 'ValidationError';
    this.statusCode = statusCode;
    this.errorCode = errorCode;
  }
}

export class InvalidParametersError extends ValidationError {
  constructor(message: string) {
    super(message, 400, 'INVALID_PARAMETERS');
  }
}

export class InvalidPaginationError extends ValidationError {
  constructor(pageIndex: number, pageSize: number) {
    super(
      `Invalid params. pageIndex should be greater than or equal to 0 and pageSize should be greater than 0. Got pageIndex: ${pageIndex}, pageSize: ${pageSize}`,
      400,
      'INVALID_PAGINATION',
    );
  }
}
