/**
 * Authentication and authorization exceptions
 * These maintain compatibility with the Java service response codes
 */

export class AuthenticationError extends Error {
  public readonly statusCode: number;
  public readonly errorCode: string;

  constructor(message: string, statusCode = 401, errorCode = 'AUTHENTICATION_FAILED') {
    super(message);
    this.name = 'AuthenticationError';
    this.statusCode = statusCode;
    this.errorCode = errorCode;
  }
}

export class InvalidTokenError extends AuthenticationError {
  constructor(reason: string) {
    super(`Invalid Authorization header: ${reason}`, 401, 'INVALID_TOKEN');
  }
}

export class TokenMismatchError extends AuthenticationError {
  constructor(jwtSub: string, expectedMemberId: string) {
    super(`JWT sub ${jwtSub} does not match memberId: ${expectedMemberId}`, 403, 'TOKEN_MISMATCH');
  }
}

export class MissingAuthHeaderError extends AuthenticationError {
  constructor() {
    super('Missing Authorization header', 401, 'MISSING_AUTH_HEADER');
  }
}
