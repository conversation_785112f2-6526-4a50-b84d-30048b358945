// Generic reusable types

export type ProductId = 'bc'; //  | 'wte'

export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor: string | null;
  endCursor: string | null;
}

export interface Connection<T extends Node, E> {
  totalCount: number;
  pageInfo: PageInfo;
  edges: Edge<T, E>[];
}

export type Edge<T extends Node, E> = E & {
  node: T;
  cursor: string;
};

export type Node = {
  id: string | number;
};
