import fetchAwsSecret from '../secrets/fetch-aws-secret';

export interface ConfigData {
  envName: ConfigEnvName;
  logLevel: string;
  statsdHost: string | undefined;
  port: number;
  bcAuthSignature: string;
  bcAuthBaseUrl: string;
  dbConfig: DatabaseConfig;
  dynamoConfig: DynamoDBConfig;
  usMemberEventArn: string;
  corsConfig: CorsConfig;
}

export interface DynamoDBConfig {
  region: string;
  tableName: string;
  ttlDays: number;
  endpoint?: string; // For local development
}

export type ConfigEnvName = 'dev' | 'qa' | 'stag' | 'prod';

export interface DatabaseConfig {
  username: string;
  password: string;
  engine: string;
  host: string;
  port: number;
  database: string;
}

export interface SnsConfig {
  queueUrl: string;
}

export interface CorsConfig {
  allowedOrigins: string[];
}

/**
 * Looks in `process.env` for the key.
 * If not found, it will look in the optional store, if exists.
 * Otherwise, it will return undefined.
 *
 * The store is expected to contain the .env file values.
 * These are injected into a local object instead of the global process.env.
 * Jenkins jobs might hardcode non-sensitive environment variables.
 * Since these exist outside of a .env, they will not be captured in this local object.
 * Therefore, we must check the store object (.env values) and the global process.env (process environment variables).
 * E.g. `PORT=4000 node file.js` - this would not exist in the store (as its not in a .env file).
 *
 * @param key   The key to look up in the store.
 * @param store The store containing the values.
 * @returns     The value of the key in the store, or undefined if not found.
 */
const readEnvVariable = (
  key: string,
  store?: Record<string, string | undefined>,
): string | undefined => {
  return process.env[key] ?? store?.[key];
};

const readRequiredEnvVariable = (
  key: string,
  store?: Record<string, string | undefined>,
): string => {
  const value = readEnvVariable(key, store);
  if (!value) {
    throw new Error(`Missing env var ${key}`);
  }
  return value;
};

const getPort = (store: Record<string, string | undefined>): number => {
  const key = 'PORT';
  const port = readEnvVariable(key, store);
  if (!port) {
    // Default port
    return 4000;
  }

  // If supplied, it must be valid
  const portInt = Number.parseInt(port, 10);
  if (Number.isNaN(portInt)) {
    throw new Error(`"${key}" environment variable must be a number`);
  }
  return portInt;
};

const getEnvName = (store: Record<string, string | undefined>): ConfigData['envName'] => {
  // Use ENVIRONMENT as the primary environment variable
  const envName = readEnvVariable('ENVIRONMENT', store);
  if (!envName) {
    throw new Error('Missing ENVIRONMENT environment variable');
  }
  if (['prod', 'stag', 'qa', 'dev'].indexOf(envName) < 0) {
    throw new Error('Invalid ENVIRONMENT value');
  }
  return envName as ConfigEnvName;
};

export const getCorsConfig = async (envName: string): Promise<CorsConfig> => {
  if (envName == 'prod')
    return {
      allowedOrigins: [
        'https://www.babycenter.com',
        'https://www.babycentre.co.uk',
        'https://www.babycenter.com.au',
        'https://www.babycenter.in',
        'https://www.babycenter.ca',
        'https://www.babycenter.de',
        'https://brasil.babycenter.com',
        'https://espanol.babycenter.com',

        // community sub-domains
        'https://community.babycenter.com',
        'https://community.babycentre.co.uk',

        // main shrub sites
        'https://shrub.babycenter.com',
        'https://shrub.babycentre.co.uk',
        'https://shrub.babycenter.com.au',
        'https://shrub.babycenter.in',
        'https://shrub.babycenter.ca',
        'https://shrub.babycenter.de',
        'https://shrub-brasil.babycenter.com',
        'https://shrub-espanol.babycenter.com',

        // shrub sub-domains
        'https://shrub-react.babycenter.com',
        'https://shrub-community.babycenter.com',
        'https://shrub-react-brasil.babycenter.com',
        'https://shrub-react-espanol.babycenter.com',
        'https://shrub-react.babycenter.ca',
        'https://shrub-react.babycenter.com.au',
        'https://shrub-react.babycentre.co.uk',
        'https://shrub-community.babycentre.co.uk',
        'https://shrub-react.babycenter.in',
        'https://shrub-react.babycenter.de',
      ],
    };

  return {
    allowedOrigins: ['*'],
  };
};

export const getDatabaseConfig = async (
  store: Record<string, string | undefined>,
): Promise<DatabaseConfig> => {
  const dbSecretArn = readEnvVariable('DB_SECRET_ARN', store);

  return dbSecretArn
    ? await getDatabaseConfigFromSecretsManager(dbSecretArn)
    : getDatabaseConfigFromEnv(store);
};

export const getDatabaseConfigFromSecretsManager = async (
  secretArn: string,
): Promise<DatabaseConfig> => {
  const secretString = await fetchAwsSecret(secretArn);
  if (!secretString) {
    throw new Error(`Unable to get DB credentials from ${secretArn}`);
  }
  const secretData = JSON.parse(secretString);

  const dbConfig: DatabaseConfig = {
    username: secretData.DBUSERNAME,
    password: secretData.DBPASSWORD,
    engine: secretData.DBENGINE ?? 'mysql',
    host: secretData.DBHOSTNAME,
    port: Number(secretData.DBPORT ?? '3306'),
    database: secretData.DBNAME ?? 'bookmarks',
  };

  return dbConfig;
};

export const getDatabaseConfigFromEnv = (
  store: Record<string, string | undefined>,
): DatabaseConfig => {
  return {
    username: readRequiredEnvVariable('DB_USER', store),
    password: readRequiredEnvVariable('DB_PASS', store),
    engine: readEnvVariable('DB_ENGINE', store) ?? 'mysql',
    host: readRequiredEnvVariable('DB_HOST', store),
    port: Number(readEnvVariable('DB_PORT', store) ?? '3306'),
    database: readEnvVariable('DB_NAME', store) ?? 'bookmarks',
  };
};

export const getLogLevel = (store: Record<string, string | undefined>): string => {
  return readEnvVariable('LOG_LEVEL', store) ?? 'info';
};

const getDynamoDBConfig = (
  store: Record<string, string | undefined>,
  envName: ConfigEnvName,
): DynamoDBConfig => {
  const region = readEnvVariable('DYNAMODB_REGION', store) ?? 'us-east-1';
  const ttlDays = parseInt(readEnvVariable('DYNAMODB_TTL_DAYS', store) ?? '7', 10);

  // Environment-specific table names
  const tableNameMap: Record<ConfigEnvName, string> = {
    dev: 'notification',
    qa: 'Notification',
    stag: 'Notification',
    prod: 'Notification',
  };

  const tableName = readEnvVariable('DYNAMODB_TABLE_NAME', store) ?? tableNameMap[envName];

  const config: DynamoDBConfig = {
    region,
    tableName,
    ttlDays,
  };

  // For local development
  const endpoint = readEnvVariable('DYNAMODB_ENDPOINT', store);
  if (endpoint) {
    config.endpoint = endpoint;
  }

  return config;
};

export const getConfigs = async (
  store: Record<string, string | undefined>,
): Promise<ConfigData> => {
  const envName = getEnvName(store);
  return {
    envName,
    logLevel: getLogLevel(store),
    statsdHost: readEnvVariable('STATSD_HOST', store),
    port: getPort(store),
    bcAuthSignature: readRequiredEnvVariable('BC_AUTH_SIGNATURE', store),
    bcAuthBaseUrl: readRequiredEnvVariable('BC_AUTH_BASE_URL', store),
    dbConfig: await getDatabaseConfig(store),
    dynamoConfig: getDynamoDBConfig(store, envName),
    usMemberEventArn:
      readEnvVariable('US_MEMBERS_EVENTS_SNS_ARN', store) ??
      'arn:aws:sns:us-east-1:123456789012:test-topic',
    corsConfig: await getCorsConfig(envName),
  };
};
