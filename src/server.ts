import http from 'http';

export default function server(server: http.Server, port: number): http.Server {
  // Explicitly listen on all available network interfaces for AWS Fargate to access
  console.log(`Starting server on port ${port}...`);

  // Explicitly bind to 0.0.0.0 to ensure the container is accessible both internally and externally
  const httpServer = server.listen(port, '0.0.0.0', () => {
    console.log('==============================');
    console.log(`Server successfully started!`);
    console.log(`Server running on port ${port}`);
    console.log(`Health check endpoint available at /health`);
    console.log(`Environment: ${process.env.ENVIRONMENT || 'unknown'}`);
    console.log(`Binding Address: 0.0.0.0:${port} (accessible from all networks)`);

    // Display network interfaces for debugging
    try {
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();
      console.log('Network interfaces:');
      Object.keys(networkInterfaces).forEach((ifname) => {
        networkInterfaces[ifname]?.forEach(
          (iface: { address: string; family: string; internal: boolean }) => {
            if (iface.family === 'IPv4') {
              console.log(`  ${ifname}: ${iface.address}`);
            }
          },
        );
      });
    } catch (err) {
      console.error('Error displaying network interfaces:', err);
    }

    console.log('==============================');
  });

  // Add specific error handling for the main server
  httpServer.on('error', (err) => {
    console.error('HTTP server error:', err);
  });

  return httpServer;
}
