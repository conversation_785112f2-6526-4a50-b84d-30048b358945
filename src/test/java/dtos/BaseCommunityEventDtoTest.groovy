package dtos

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import notification.AppConfig
import notification.dtos.BaseCommunityEventDto
import notification.pojos.GsonUTCDateAdapter
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.context.annotation.Import
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import static org.junit.Assert.assertEquals
import static org.junit.Assert.assertTrue;

@RunWith(SpringJUnit4ClassRunner.class)
@Import(AppConfig.class)
class BaseCommunityEventDtoTest
{
	private Validator validator

	private Gson gson = new GsonBuilder()
		.registerTypeAdapter(Date.class, new GsonUTCDateAdapter())
		.setPrettyPrinting()
		.create()

	@Before
	void setUp() {
		ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
		validator = factory.getValidator();
	}

	@Test
	void testValidDto ()
	{
		def validData =
			[
				"globalMemberId": "Xrcjl3gdXfEl4P4I",
				"siteMemberId": 165419348091,
				"countryCode": "en_us",
				"contentId": 363,
				"eventType": "comment",
				"eventVersion": "0.1",
				"timestamp": 1597762346000,
				"snippet": "Lorem ipsum dolor sit amet, consectetur adipiscing elit",
				"genericContent": [
					"userScreenName": "pdiniz",
					"href": "https://google.com#c1006",
					"messageMultipleOccurrence": "\u003cb\u003epdiniz\u003c/b\u003e and {count} others commented on your post \"Lorem ipsum dolor sit amet, consectetur adipiscing elit.\"",
					"message": "\u003cb\u003epdiniz\u003c/b\u003e commented on your post \"Lorem ipsum dolor sit amet, consectetur adipiscing elit..\"",
					"originalCommentUserId": "vy04VMoa0ZVAPala"
				]
			]

		String validJson = gson.toJson(validData)
		def output = gson.fromJson(validJson, BaseCommunityEventDto.class)
		assertEquals validJson, gson.toJson(output)

		Set<ConstraintViolation<BaseCommunityEventDto>> violations = validator.validate(validData)
		assertTrue("Violations were found: " + violations, violations.isEmpty())
	}


	@Test
	void testInvalidDtoInList ()
	{
		def validData =
		[
			//Missing contentId
			"globalMemberId": "Xrcjl3gdXfEl4P4I",
			"siteMemberId": 165419348091,
			"countryCode": "en_us",
			"eventType": "comment",
			"eventVersion": "0.1",
			"timestamp": 1597762346000,
			"snippet": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
			"genericContent": [
					"userScreenName": "pdiniz",
					"href": "https://google.com#c1006",
					"messageMultipleOccurrence": "\u003cb\u003epdiniz\u003c/b\u003e and {count} others commented on your post \"Lorem ipsum dolor sit amet, consectetur adipiscing elit.\"",
					"message": "\u003cb\u003epdiniz\u003c/b\u003e commented on your post \"Lorem ipsum dolor sit amet, consectetur adipiscing elit..\"",
					"originalCommentUserId": "vy04VMoa0ZVAPala"
			]
		]

		String validJson = gson.toJson(validData)
		def output = gson.fromJson(validJson, BaseCommunityEventDto.class)
		Set<ConstraintViolation<BaseCommunityEventDto>> violations = validator.validate(output);

		assertTrue("Violations were not found: " + violations, !violations.isEmpty())

	}
}
