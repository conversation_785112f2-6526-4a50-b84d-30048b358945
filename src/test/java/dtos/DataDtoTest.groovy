package dtos

import notification.AppConfig
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Import
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner

import javax.validation.Validator;

import static org.junit.Assert.*

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import notification.dtos.DataDto;
import notification.pojos.GsonUTCDateAdapter;
import org.junit.Test;

import javax.validation.ConstraintViolation;

@RunWith(SpringJUnit4ClassRunner.class)
@Import(AppConfig.class)
class DataDtoTest
{
	@Autowired
	private Validator validator

	private Gson gson = new GsonBuilder()
		.registerTypeAdapter(Date.class, new GsonUTCDateAdapter())
		.setPrettyPrinting()
		.create()

	@Test
	void testValidDto ()
	{
		def validData =
			[
				countryCode: 'en_US',
				userId: 10001L,
				contentId: 10000L,
				action: 'comment',
				originalCommentUserId: 10002L,
				replyId: 20000L
			]

		String validJson = gson.toJson(validData)
		def output = gson.fromJson(validJson, DataDto.class)
		assertEquals validJson, gson.toJson(output)

		Set<ConstraintViolation<DataDto>> violations = validator.validate(validData)
		assertTrue("Violations were found: " + violations, violations.isEmpty())
	}


	//@Test
	void testInvalidDtoInList ()
	{
		def validData =
		[
			[
				countryCode: 'en_US',
				userId: 8001L,
				contentId: 8000L,
				action: 'comment',
				originalCommentUserId: 8002L,
				replyId: 18000L
			],
			[
				countryCode: '',
				userId: 9001L,
				contentId: 9000L,
				action: 'comment',
				originalCommentUserId: 9002L,
				replyId: 19000L,
			],
			[
				countryCode: 'en_US',
				userId: 10001L,
				contentId: 10000L,
				action: 'comment',
				originalCommentUserId: 10002L,
				replyId: 20000L
			]
		]

		Set<ConstraintViolation<DataDto>> violations = validator.validate(validData[0])
		assertTrue("Violations were found: " + violations, violations.isEmpty())

		violations = validator.validate(validData[1])
		// TODO Fix validation so this works correctly!
		assertTrue("Violations were not found: " + violations, !violations.isEmpty())

		violations = validator.validate(validData[2])
		assertTrue("Violations were found: " + violations, violations.isEmpty())
	}
}
