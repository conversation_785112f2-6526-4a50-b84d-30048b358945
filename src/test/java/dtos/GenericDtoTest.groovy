package dtos

import static org.junit.Assert.*
import notification.dtos.DataDto
import notification.dtos.GenericDto
import notification.pojos.GsonUTCDateAdapter

import org.junit.Test

import com.google.gson.Gson
import com.google.gson.GsonBuilder

class GenericDtoTest {
	private Gson gson = new GsonBuilder()
		.registerTypeAdapter(Date.class, new GsonUTCDateAdapter())
		.setPrettyPrinting()
		.create()
	
	@Test
	void testValid() {
		def validData = [countryCode: 'en_US',
			userIds: [10001L, 10002L, 10003L],
			contentId: 10000L,
			genericContent: [type: 'AdmMessage',
					imgClass: 'icon_welcome',
					href: '/a/groups',
					message: '''Welcome to community <a href='http://www.zyx.com'>xyz.com</a>'''
						]
			]
		
		def validJson = gson.toJson(validData)
		
		println validJson
		
		def output = gson.fromJson(validJson, GenericDto.class)
		
		println output
		
		assertEquals validJson, gson.toJson(output)
	}
	
	@Test
	void testTimestamp() {
		def data = [countryCode: 'en_US',
				userId: 1200L,
				contentId: 2035L,
				action: 'reactOnPost',
				actionTime: '2016-10-19T22:17:22.683Z'
			]
		def json = gson.toJson(data)
		
		println json
		
		DataDto obj = gson.fromJson(json, DataDto.class)
		
		println obj
		
		def newjson = gson.toJson(obj)
		
		println newjson
		
		obj.actionTime = new Date()
		
		println obj
		
		newjson = gson.toJson(obj)
		
		println newjson
	}

}
