package com.babycenter.bookmarkservice.auth;

import org.junit.Test;

import static org.junit.Assert.assertFalse;

/**
 * Created by emurphy on 4/21/17.
 */
public class AccessTokenRequestValidatorTest
{
	private static final String issue = "qa-auth.babycenter.com";
	
	@Test
	public void test()
	{
		final String validationKey = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd\noLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw\nlf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux\nxw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl\n9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq\nyb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe\n+QIDAQAB\n-----END PUBLIC KEY-----";
		final String issuer = "qa-auth.babycenter.com";
		final String accessToken = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
		final String userId = "VCNyezPbJjhf7RxH";
		
		final AccessTokenRequestValidator accessTokenRequestValidator =
			new AccessTokenRequestValidator(
				new VerifyAlgorithmProducer(),
				new MockValidationKeyProducer(validationKey),
				issuer);
		
		assertFalse("token is exired", accessTokenRequestValidator.isValid(accessToken, userId));
	}
	
	//
	// mock always return the given validation key
	//
	private class MockValidationKeyProducer extends ValidationKeyProducer
	{
		public MockValidationKeyProducer(final String validateKey)
		{
			super(null);
			this.setAuthServiceValidationKey(validateKey);
		}
	}
}
