package com.babycenter.bookmarkservice.auth;

import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.Assert.*;

/**
 * Created by a<PERSON><PERSON> on 5/26/16.
 */
@RunWith(DataProviderRunner.class)
public class RequestTokenTest
{
	private String SECRET_KEY = "secretKey";
	
	/**
	 * Data provider to test token includes
	 * Member id
	 * ZonedDateTime
	 * Expected token
	 *
	 * @return Object[][]
	 */
	@DataProvider
	public static Object[][] dataProviderAdd()
	{
		return new Object[][] {
			{ 10L,
				ZonedDateTime.of(LocalDateTime.of(2013, Month.JULY, 20, 19, 30),  ZoneId.of("America/Los_Angeles")),
				"-t6yc25pmomplzckyzbdc3212qd6baptwz1w2ety7ghz9onwtc" // expected Token
			},
			{ 10L,
				ZonedDateTime.of(LocalDateTime.of(2013, Month.JULY, 20, 19, 30),  ZoneId.of("Asia/Calcutta")),
				"-gftgux9c1qlbbp8arrrank9gnivybz8gkiivsqn8lm1t76xx6"
			}
		};
	}

	
	/**
	 * Test to check Token method return right token
	 * uses a data provider that passes the 3 params needed
	 *
	 * @param id - user member id
	 * @param zonedDateTime
	 * @param expectedToken
	 */

	@Test
	@UseDataProvider("dataProviderAdd")
	public void testToken(Long id, ZonedDateTime zonedDateTime, String expectedToken)
	{
		RequestToken requestToken = new RequestToken();
		String token = requestToken.token(SECRET_KEY, id.toString(), zonedDateTime);
		assertEquals(expectedToken, token);
	}

	@Test
	public void testValidToken()
	{
		final String theSharedSecret = "foo";
		final Long theMemberId = 1000L;
		final ZonedDateTime now = ZonedDateTime.now();
		final ZonedDateTime past = now.minusSeconds(RequestToken.TIME_OUT_SECONDS);
		
		Assert.assertTrue("The request token should be valid", RequestToken.isTokenValid(theSharedSecret, theMemberId.toString(), RequestToken.token(theSharedSecret, theMemberId.toString(), now)));
		Assert.assertTrue("The request token from prior period should be valid", RequestToken.isTokenValid(theSharedSecret, theMemberId.toString(), RequestToken.token(theSharedSecret, theMemberId.toString(), past)));
	}

	@Test
	public void testInvalidToken()
	{
		final String theSharedSecret = "foo";
		final Long theMemberId = 1000L;
		final ZonedDateTime now = ZonedDateTime.now();
		final ZonedDateTime past = now.minusSeconds(RequestToken.TIME_OUT_SECONDS * 2);
		final ZonedDateTime future = now.plusSeconds(RequestToken.TIME_OUT_SECONDS);
		
		Assert.assertTrue("The request token should be valid", RequestToken.isTokenValid(theSharedSecret, theMemberId.toString(), RequestToken.token(theSharedSecret, theMemberId.toString(), now)));
		Assert.assertFalse("The request token from past should be invalid", RequestToken.isTokenValid(theSharedSecret, theMemberId.toString(), RequestToken.token(theSharedSecret, theMemberId.toString(), past)));
		Assert.assertFalse("The request token from future should be invalid", RequestToken.isTokenValid(theSharedSecret, theMemberId.toString(), RequestToken.token(theSharedSecret, theMemberId.toString(), future)));
	}

	@Test
	public void testNonDatedToken() {
		final String sharedSecret = "foo";
		final String path = "/url/path/100";
		final String expectedBase64Value = "N8Ct7ZuzoxaRoTOxzPY/z3Q9lfLio85dzi1bS5rmnao=";
		String base64Token = RequestToken.nonTimedToken(sharedSecret, path);
		Assert.assertEquals("The base64 token should be correct", expectedBase64Value, base64Token);
		Assert.assertTrue("The request token should be valid", RequestToken.isTokenValid(sharedSecret, path, expectedBase64Value));
	}
}