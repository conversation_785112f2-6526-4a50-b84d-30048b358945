package com.babycenter.bookmarkservice.util;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by emurphy on 3/30/17.
 */
public final class Lists
{
	private Lists() { throw new RuntimeException(); }
	
	/**
	 * Convert an iterable to a list.
	 * In the case of null, this creates an empty list.
	 *
	 * @param <T> the item type
	 * @param items the iterable, may be null
	 * @return a list of items.  This will be empty is null is passed.
	 */
	public static final <T> List<T> toList(Iterable<T> items)
	{
		final List<T> list = new ArrayList<>();
		if(null != items)
		{
			for (T item : items)
			{
				list.add(item);
			}
		}
		return list;
	}
	
	public static final <T> List<T> of(final T a)
	{
		final List<T> list = new ArrayList<>();
		list.add(a);
		return list;
	}
	public static final <T> List<T> of(final T a, final T b)
	{
		final List<T> list = new ArrayList<>();
		list.add(a);
		list.add(b);
		return list;
	}
	public static final <T> List<T> of(final T a, final T b, final T c)
	{
		final List<T> list = new ArrayList<>();
		list.add(a);
		list.add(b);
		list.add(c);
		return list;
	}
	public static final <T> List<T> of(final T a, final T b, final T c, final T d)
	{
		final List<T> list = new ArrayList<>();
		list.add(a);
		list.add(b);
		list.add(c);
		list.add(d);
		return list;
	}
	public static final <T> List<T> of(final T a, final T b, final T c, final T d, final T e)
	{
		final List<T> list = new ArrayList<>();
		list.add(a);
		list.add(b);
		list.add(c);
		list.add(d);
		list.add(e);
		return list;
	}
	
}
