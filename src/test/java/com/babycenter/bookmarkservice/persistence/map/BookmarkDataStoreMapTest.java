package com.babycenter.bookmarkservice.persistence.map;

import com.babycenter.bookmarkservice.model.Bookmark;

import java.util.ArrayList;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Created by emurphy on 3/17/17.
 */
public class BookmarkDataStoreMapTest
{
	@Test
	public void testReadOneSaveOne()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		final Bookmark createdBookmark = dataStore.saveOne(bookmark);

		assertEquals("Natural key fields should be in database.", bookmark.getMemberId(), createdBookmark.getMemberId());
		assertEquals("Natural key fields should be in database.", bookmark.getContentSource(), createdBookmark.getContentSource());
		assertEquals("Natural key fields should be in database.", bookmark.getContentType(), createdBookmark.getContentType());
		assertEquals("Natural key fields should be in database.", bookmark.getContentId(), createdBookmark.getContentId());
		assertNotNull("There should be a createDate", createdBookmark.getCreateDate());
		assertNotNull("There should be a updateDate", createdBookmark.getUpdateDate());

		assertNull("The createUser must be supplied by the caller.", createdBookmark.getCreateUser());
		assertNull("The updateUser must be supplied by the caller.", createdBookmark.getUpdateUser());

		final Bookmark loadedBookmark = dataStore.readOne(createdBookmark.getId());
		assertNotNull("The bookmark should be retrieved from the database.", loadedBookmark);
		assertFalse("This should be a separate instance.", createdBookmark == loadedBookmark);
		assertEquals("Instances read from the database must be equivalent", createdBookmark, loadedBookmark);

		final Bookmark reloadedBookmark = dataStore.readOne("123", "bcsite", "answer", "321");
		assertNotNull("The bookmark should be retrieved from the database.", reloadedBookmark);
		assertFalse("This should be a separate instance.", createdBookmark == reloadedBookmark);
		assertEquals("Instances read from the database must be equivalent", createdBookmark, reloadedBookmark);
	}

	@Test
	public void testDeleteOne()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		final Bookmark createdBookmark = dataStore.saveOne(bookmark);

		dataStore.deleteOne(createdBookmark.getId());

		final Bookmark loadedBookmark = dataStore.readOne(createdBookmark.getId());
		assertNull("The deleted bookmark should not exist in the database.", loadedBookmark);
	}
	
	@Test
	public void testReadAllMemberSourceTypeBookmarks()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();
		
		// 4 bookmarks for member 123
		final Bookmark bookmark1 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "321"));
		final Bookmark bookmark2 = dataStore.saveOne(new Bookmark("123", "social", "answer", "322"));
		final Bookmark bookmark3 = dataStore.saveOne(new Bookmark("123", "bcsite", "question", "323"));
		final Bookmark bookmark4 = dataStore.saveOne(new Bookmark("123", "social", "question", "324"));
		
		// bookmark for another member
		final Bookmark otherBookmark = dataStore.saveOne(new Bookmark("124", "bcsite", "answer", "321"));
		
		//
		// filter by member id, content source and content type
		//
		final List<Bookmark> bookmarks = Lists.newArrayList(dataStore.readAll("123", "bcsite", "answer"));
		
		assertTrue("There should be 1 results for member 123, bcsite, answer", bookmarks.size() == 1);
		assertTrue(bookmarks.contains(bookmark1));
		assertFalse(bookmarks.contains(bookmark2));
		assertFalse(bookmarks.contains(bookmark3));
		assertFalse(bookmarks.contains(bookmark4));
		assertFalse(bookmarks.contains(otherBookmark));
	}
	
	@Test
	public void testReadAllMemberSourceBookmarks()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();
		
		// 4 bookmarks for member 123
		final Bookmark bookmark1 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "321"));
		final Bookmark bookmark2 = dataStore.saveOne(new Bookmark("123", "social", "answer", "322"));
		final Bookmark bookmark3 = dataStore.saveOne(new Bookmark("123", "bcsite", "question", "323"));
		final Bookmark bookmark4 = dataStore.saveOne(new Bookmark("123", "social", "question", "324"));
		
		// bookmark for another member
		final Bookmark otherBookmark = dataStore.saveOne(new Bookmark("124", "bcsite", "answer", "321"));
		
		//
		// filter by member id and content source
		//
		final List<Bookmark> bookmarks = Lists.newArrayList(dataStore.readAll("123", "bcsite"));
		
		assertTrue("There should be 2 results for member 123, bcsite", bookmarks.size() == 2);
		assertTrue(bookmarks.contains(bookmark1));
		assertFalse(bookmarks.contains(bookmark2));
		assertTrue(bookmarks.contains(bookmark3));
		assertFalse(bookmarks.contains(bookmark4));
		assertFalse(bookmarks.contains(otherBookmark));
	}
	
	@Test
	public void testReadAllMemberBookmarks()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();
		
		// 4 bookmarks for member 123
		final Bookmark bookmark1 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "321"));
		final Bookmark bookmark2 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "322"));
		final Bookmark bookmark3 = dataStore.saveOne(new Bookmark("123", "bcsite", "question", "323"));
		final Bookmark bookmark4 = dataStore.saveOne(new Bookmark("123", "social", "question", "324"));
		
		// bookmark for another member
		final Bookmark otherBookmark = dataStore.saveOne(new Bookmark("124", "bcsite", "answer", "321"));
		
		//
		// filter by member id only
		//
		final List<Bookmark> bookmarks = Lists.newArrayList(dataStore.readAll("123"));
		
		assertTrue("There should be 4 results for member 123", bookmarks.size() == 4);
		assertTrue(bookmarks.contains(bookmark1));
		assertTrue(bookmarks.contains(bookmark2));
		assertTrue(bookmarks.contains(bookmark3));
		assertTrue(bookmarks.contains(bookmark4));
		assertFalse(bookmarks.contains(otherBookmark));
	}
	
	@Test
	public void testReadAllMembers()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();

		// 4 bookmarks for member 123
		final Bookmark bookmark1 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "321"));
		final Bookmark bookmark2 = dataStore.saveOne(new Bookmark("123", "social", "answer", "322"));
		final Bookmark bookmark3 = dataStore.saveOne(new Bookmark("123", "bcsite", "question", "323"));
		final Bookmark bookmark4 = dataStore.saveOne(new Bookmark("123", "social", "question", "324"));

		// bookmark for another member
		final Bookmark otherBookmark = dataStore.saveOne(new Bookmark("124", "bcsite", "answer", "321"));

		//
		// filter by content source, type and id
		//
		final List<Bookmark> bookmarks = Lists.newArrayList(dataStore.readAllContent("bcsite", "answer", "321", 0, 10).getResults());

		assertEquals("There should be 2 results for bcsite answer 321", 2, bookmarks.size());
		assertTrue(bookmarks.contains(bookmark1));
		assertTrue(bookmarks.contains(otherBookmark));
	}

	@Test
	public void testReadAllMembersPaging()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();

		final Bookmark bookmark1 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "321"));
		final Bookmark otherBookmark = dataStore.saveOne(new Bookmark("124", "bcsite", "answer", "321"));
		final Bookmark yetAnotherBookmark = dataStore.saveOne(new Bookmark("125", "bcsite", "answer", "321"));

		final List<Bookmark> bookmarks = Lists.newArrayList(dataStore.readAllContent("bcsite", "answer", "321", 0, 2).getResults());

		assertTrue("There should be 2 results for answer 321 first page", bookmarks.size() == 2);

		final List<Bookmark> bookmarksPageTwo = Lists.newArrayList(dataStore.readAllContent("bcsite", "answer", "321", 1, 2).getResults());
		assertTrue("There should be 1 result for answer 321 page 2", bookmarksPageTwo.size() == 1);

		//
		// combine results and make sure each bookmark is represented
		//
		final List<Bookmark> allBookmarks = new ArrayList<>(bookmarks);
		allBookmarks.addAll(bookmarksPageTwo);
		assertTrue(allBookmarks.contains(bookmark1));
		assertTrue(allBookmarks.contains(otherBookmark));
		assertTrue(allBookmarks.contains(yetAnotherBookmark));

	}
	
	@Test
	public void testDeleteAll()
	{
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();
		
		// 4 bookmarks for member 123
		final Bookmark bookmark1 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "321"));
		final Bookmark bookmark2 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "322"));
		final Bookmark bookmark3 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "323"));
		final Bookmark bookmark4 = dataStore.saveOne(new Bookmark("123", "bcsite", "answer", "324"));
		
		// bookmark for another member
		final Bookmark otherMember = dataStore.saveOne(new Bookmark("124", "bcsite", "answer", "321"));
		
		// bookmark for another source
		final Bookmark otherSource = dataStore.saveOne(new Bookmark("123", "community", "answer", "321"));
		
		// bookmark for another content type
		final Bookmark otherType = dataStore.saveOne(new Bookmark("123", "bcsite", "question", "321"));
		
		//
		// delete all 123:bcsite:answers with id's 321, 322, 323
		//
		dataStore.deleteAll("123", "bcsite", "answer", com.babycenter.bookmarkservice.util.Lists.of("321", "322", "323"));
		
		//
		// filter by content source, type and id
		//
		final List<Bookmark> bookmarks = Lists.newArrayList(dataStore.readAll("123", "bcsite", "answer"));
		
		assertTrue("There should be 1 results for 123:bcsite:answer", bookmarks.size() == 1);
		assertFalse(bookmarks.contains(bookmark1));
		assertFalse(bookmarks.contains(bookmark2));
		assertFalse(bookmarks.contains(bookmark3));
		assertTrue(bookmarks.contains(bookmark4));
		
		// check that the other entities are still there
		assertNotNull(dataStore.readOne("124", "bcsite", "answer", "321"));
		assertNotNull(dataStore.readOne("123", "community", "answer", "321"));
		assertNotNull(dataStore.readOne("123", "bcsite", "question", "321"));
	}

}
