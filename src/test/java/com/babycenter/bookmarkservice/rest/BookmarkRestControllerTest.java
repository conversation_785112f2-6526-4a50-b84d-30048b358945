package com.babycenter.bookmarkservice.rest;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.babycenter.JsonTestUtils;
import com.babycenter.bookmarkservice.auth.AccessTokenRequestValidator;
import com.babycenter.bookmarkservice.auth.ValidationKeyProducer;
import com.babycenter.bookmarkservice.auth.VerifyAlgorithmProducer;
import com.babycenter.bookmarkservice.business.BookmarkService;
import com.babycenter.bookmarkservice.event.ProfileEventService;
import com.babycenter.bookmarkservice.model.Bookmark;
import com.babycenter.bookmarkservice.persistence.BookmarkDataStore;
import com.babycenter.bookmarkservice.persistence.map.BookmarkDataStoreMap;
import com.babycenter.bookmarkservice.util.Lists;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * Created by emurphy on 3/17/17.
 */
public class BookmarkRestControllerTest
{
	private MockMvc mockMvc;
	private String signingSecret = "secret";

	//
	// mocks
	//
	class MockValidationKeyProducer extends ValidationKeyProducer
	{
		public MockValidationKeyProducer()
		{
			super(null);
		}

		@Override
		public String validationKey()
		{
			return signingSecret;
		}
	}
	class MockVerifyAlgorithmProducer extends VerifyAlgorithmProducer
	{
		@Override
		public Algorithm verifyAlgorithm(final String signingKey)
		{
			return Algorithm.HMAC256(signingKey.getBytes());
		}
	}
	private Algorithm signingAlgorithm(final String signingKey)
	{
		return Algorithm.HMAC256(signingKey.getBytes());
	}


	//
	// test helpers
	//
	private String accessToken(final String memberId, final String contentSource)
	{
		return 	JWT.create()
			.withSubject(memberId)
			.withAudience(contentSource)
			.withExpiresAt(new Date(System.currentTimeMillis() + 1000L * 60 * 20))
			.withIssuer("auth.babycenter.com")
			.sign(signingAlgorithm(signingSecret));
	}

	private BookmarkRestController newBookmarkRestController(final BookmarkDataStore bookmarkDataStore)
	{
		ProfileEventService profileEventService = new ProfileEventService();

		BookmarkService bookmarkService = new BookmarkService();
		bookmarkService.setDataStore(bookmarkDataStore);
		bookmarkService.setProfileEventService(profileEventService);

		final BookmarkRestController controller = new BookmarkRestController();
		controller.setDataStore(bookmarkDataStore);
		controller.setAccessTokenRequestValidator(
			new AccessTokenRequestValidator(
				new MockVerifyAlgorithmProducer(),
				new MockValidationKeyProducer(),
				"auth.babycenter.com"));
		controller.setBookmarkService(bookmarkService);

		return controller;
	}


	private BookmarkRestController newBookmarkRestController()
	{
		return newBookmarkRestController(new BookmarkDataStoreMap());
	}

	@Test
	public void saveTest()
	{
		final BookmarkDataStore bookmarkDataStore = new BookmarkDataStoreMap();
		final BookmarkRestController controller = newBookmarkRestController(bookmarkDataStore);

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		final ResponseEntity<Bookmark> responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken("123", "bcsite"),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId(),
				bookmark);

		assertEquals("Status should be 201 created", HttpStatus.CREATED, responseEntity.getStatusCode());
		assertNotNull("bookmark should be in data store",
			bookmarkDataStore.readOne(
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId()));

		//
		// result should have a primary key, a created date and an updated date
		//
		assertNotNull("result should have a primary key", responseEntity.getBody().getId());
		assertNotNull("result should have a created date", responseEntity.getBody().getCreateDate());
		assertNotNull("result should have a updated date", responseEntity.getBody().getUpdateDate());

		//
		// natural key fields should be equal
		//
		assertEquals("natural key fields should be equal", bookmark.getMemberId(), responseEntity.getBody().getMemberId());
		assertEquals("natural key fields should be equal", bookmark.getContentSource(), responseEntity.getBody().getContentSource());
		assertEquals("natural key fields should be equal", bookmark.getContentType(), responseEntity.getBody().getContentType());
		assertEquals("natural key fields should be equal", bookmark.getContentId(), responseEntity.getBody().getContentId());
	}

	@Test
	public void deleteTest()
	{
		final BookmarkDataStore bookmarkDataStore = new BookmarkDataStoreMap();
		final BookmarkRestController controller = newBookmarkRestController(bookmarkDataStore);

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		controller.save(
			accessToken("123", "bcsite"),
			bookmark.getMemberId(),
			bookmark.getContentSource(),
			bookmark.getContentType(),
			bookmark.getContentId(),
			bookmark);

		//
		// successful delete returns 204
		//
		ResponseEntity<Bookmark> responseEntity =
			controller.delete(
				accessToken("123", "bcsite"),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId());

		assertEquals("Status should be 204 No Content.", HttpStatus.NO_CONTENT, responseEntity.getStatusCode());
		assertNull("bookmark should NOT be in data store",
			bookmarkDataStore.readOne(
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId()));

		//
		// deleting something that does not exists returns a 404
		//
		responseEntity =
			controller.delete(
				accessToken("123", "bcsite"),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId());
		assertEquals("Status should be 404 Not Found.", HttpStatus.NOT_FOUND, responseEntity.getStatusCode());


	}

	@Test
	public void readAllTest()
	{
		final BookmarkRestController controller = newBookmarkRestController();

		//
		// create a variety of bookmarks
		//
		final List<Bookmark> bookmarks = new ArrayList<>();
		bookmarks.add(new Bookmark("123", "bcsite", "answer", "321"));
		bookmarks.add(new Bookmark("123", "bcsite", "question", "321"));
		bookmarks.add(new Bookmark("123", "social", "answer", "321"));
		bookmarks.add(new Bookmark("123", "social", "question", "321"));
		bookmarks.add(new Bookmark("456", "bcsite", "answer", "321"));
		bookmarks.add(new Bookmark("456", "bcsite", "question", "321"));
		bookmarks.add(new Bookmark("456", "social", "answer", "321"));
		bookmarks.add(new Bookmark("456", "social", "question", "321"));

		//
		// save each bookmark and keep the result for checking
		//
		final List<Bookmark> savedBookmarks = new ArrayList<>(bookmarks.size());
		for(Bookmark bookmark : bookmarks)
		{
			savedBookmarks.add(((ResponseEntity<Bookmark>)controller.save(
					accessToken(bookmark.getMemberId(), bookmark.getContentSource()),
					bookmark.getMemberId(),
					bookmark.getContentSource(),
					bookmark.getContentType(),
					bookmark.getContentId(),
					bookmark)).getBody());
		}

		//
		// test filter by member id, source and type
		//
		ResponseEntity<Iterable<Bookmark>> response = controller.read(accessToken("123", "bcsite"), "123", "bcsite", "answer");
		assertEquals("Should return 200 http status", 200, response.getStatusCodeValue());

		List<Bookmark> loadedBookmarks = Lists.toList(response.getBody());
		assertEquals("There should be 1 results for member 123, bcsite, question", 1, loadedBookmarks.size());
		assertEquals("Bookmark 0 should have been loaded", savedBookmarks.get(0), loadedBookmarks.get(0));


		//
		// test filter by member id and source
		//
		response = controller.read(accessToken("123", "bcsite"), "123", "bcsite");
		assertEquals("Should return 200 http status", 200, response.getStatusCodeValue());

		loadedBookmarks = Lists.toList(response.getBody());
		assertEquals("There should be 2 results for member 123, bcsite", 2, loadedBookmarks.size());
		assertTrue("Bookmark 0 should have been loaded", loadedBookmarks.contains(savedBookmarks.get(0)));
		assertTrue("Bookmark 1 should have been loaded", loadedBookmarks.contains(savedBookmarks.get(1)));

		//
		// test filter by member id
		//
		response = controller.read(accessToken("123", "bcsite"), "123");
		assertEquals("Should return 200 http status", 200, response.getStatusCodeValue());

		loadedBookmarks = Lists.toList(response.getBody());
		assertEquals("There should be 4 results for member 123", 4, loadedBookmarks.size());
		assertTrue("Bookmark 0 should have been loaded", loadedBookmarks.contains(savedBookmarks.get(0)));
		assertTrue("Bookmark 1 should have been loaded", loadedBookmarks.contains(savedBookmarks.get(1)));
		assertTrue("Bookmark 2 should have been loaded", loadedBookmarks.contains(savedBookmarks.get(2)));
		assertTrue("Bookmark 3 should have been loaded", loadedBookmarks.contains(savedBookmarks.get(3)));


	}

	@Test
	public void saveDuplicateTest()
	{
		final BookmarkRestController controller = newBookmarkRestController();

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		final ResponseEntity<Bookmark> responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken("123", "bcsite"),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId(),
				bookmark);

		final ResponseEntity<Bookmark> otherResponseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken("123", "bcsite"),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId(),
				bookmark);

		assertEquals("Status should be 200 ok", HttpStatus.OK, otherResponseEntity.getStatusCode());
		assertEquals("Instances should be equivalent", responseEntity.getBody(), otherResponseEntity.getBody());
	}

	@Test
	public void saveMalformedTest()
	{
		final BookmarkRestController controller = newBookmarkRestController();

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		//
		// id in payload does not match id in url.
		//
		ResponseEntity<Bookmark> responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken(bookmark.getMemberId() + "foo", bookmark.getContentSource()),
				bookmark.getMemberId() + "foo",
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId(),
				bookmark);

		assertEquals("id in payload does not match id in url.  Status should be 400 Bad Request", HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

		//
		// Content source in payload does not match content source in url.
		//
		responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken(bookmark.getMemberId(), "en_CA"),
				bookmark.getMemberId(),
				"en_CA",
				bookmark.getContentType(),
				bookmark.getContentId(),
				bookmark);

		assertEquals("Content source in payload does not match content source in url. Status should be 400 Bad Request", HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

		//
		// Content type in payload does not match content type in url.
		//
		responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken(bookmark.getMemberId(), bookmark.getContentSource()),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType() + "foo",
				bookmark.getContentId(),
				bookmark);

		assertEquals("Content type in payload does not match content type in url. Status should be 400 Bad Request", HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

		//
		// Content id in payload does not match content id in url.
		//
		responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken(bookmark.getMemberId(), bookmark.getContentSource()),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId() + "foo",
				bookmark);

		assertEquals("Content id in payload does not match content id in url. Status should be 400 Bad Request", HttpStatus.BAD_REQUEST, responseEntity.getStatusCode());

	}

	@Test
	public void saveBadAuthTest()
	{
		final BookmarkRestController controller = newBookmarkRestController();

		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");

		//
		// id in accessToken does not match id in url.
		//
		ResponseEntity<Bookmark> responseEntity = (ResponseEntity<Bookmark>)
			controller.save(
				accessToken(bookmark.getMemberId() + "foo", bookmark.getContentSource()),
				bookmark.getMemberId(),
				bookmark.getContentSource(),
				bookmark.getContentType(),
				bookmark.getContentId(),
				bookmark);

		assertEquals("id in accessToken does not match id in url. Status should be 401 Unauthorized", HttpStatus.UNAUTHORIZED, responseEntity.getStatusCode());

	}

	@Test
	public void saveHappyPathWithValidateTest() throws Exception
	{
		ObjectMapper mapper = new ObjectMapper();

		// Process mock annotations
		MockitoAnnotations.initMocks(this);

		final BookmarkRestController controller = new BookmarkRestController();
		this.mockMvc = MockMvcBuilders.standaloneSetup(controller).build();

		// set our in-memory data store so we can get results
		BookmarkDataStoreMap dataStore = new BookmarkDataStoreMap();
		BookmarkService bookmarkService = new BookmarkService();
		bookmarkService.setDataStore(dataStore);
		controller.setDataStore(dataStore);
		controller.setAccessTokenRequestValidator(
			new AccessTokenRequestValidator(
				new MockVerifyAlgorithmProducer(),
				new MockValidationKeyProducer(),
				"auth.babycenter.com"));
		controller.setBookmarkService(bookmarkService);

		//
		// happy path save
		//
		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(bookmark)))
			.andExpect(status().is2xxSuccessful());

		//
		// happy path read - check that result matches what we saved.
		//
		MvcResult theGetResult = this.mockMvc.perform(get("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON))
			.andExpect(status().is2xxSuccessful())
			.andReturn();

		//
		// check update and create date in the json, since they will not be deserialized (an artifact of the test)
		//
		assertTrue(JsonTestUtils.hasJsonField(theGetResult.getResponse().getContentAsString(), "createDate"));
		assertTrue(JsonTestUtils.hasJsonField(theGetResult.getResponse().getContentAsString(), "updateDate"));

		//
		// all other fields are deserialized and can be checked directly
		//
		Bookmark savedBookmark = mapper.readValue(theGetResult.getResponse().getContentAsString(), Bookmark.class);
		assertNotNull(savedBookmark.getId());
		assertEquals("Natural key fields should be saved.", bookmark.getMemberId(), savedBookmark.getMemberId());
		assertEquals("Natural key fields should be saved.", bookmark.getContentSource(), savedBookmark.getContentSource());
		assertEquals("Natural key fields should be saved.", bookmark.getContentType(), savedBookmark.getContentType());
		assertEquals("Natural key fields should be saved.", bookmark.getContentId(), savedBookmark.getContentId());

	}

	@Test
	public void saveValidationTest() throws Exception
	{
		ObjectMapper mapper = new ObjectMapper();

		// Process mock annotations
		MockitoAnnotations.initMocks(this);

		final BookmarkRestController controller = new BookmarkRestController();
		this.mockMvc = MockMvcBuilders.standaloneSetup(controller).build();

		// set our in-memory data store so we can get results
		controller.setDataStore(new BookmarkDataStoreMap());
		controller.setAccessTokenRequestValidator(
			new AccessTokenRequestValidator(
				new MockVerifyAlgorithmProducer(),
				new MockValidationKeyProducer(),
				"auth.babycenter.com"));

		//
		// test empty string in each natural key field - should return 400
		//
		final Bookmark bookmark = new Bookmark("123", "bcsite", "answer", "321");
		Bookmark badBookmark = new Bookmark(bookmark);
		badBookmark.setMemberId("");
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(badBookmark)))
			.andExpect(status().is(400));

		badBookmark = new Bookmark(bookmark);
		badBookmark.setContentSource("");
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(badBookmark)))
			.andExpect(status().is(400));

		badBookmark = new Bookmark(bookmark);
		badBookmark.setContentType("");
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(badBookmark)))
			.andExpect(status().is(400));

		badBookmark = new Bookmark(bookmark);
		badBookmark.setContentId("");
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(badBookmark)))
			.andExpect(status().is(400));

		//
		// member id is too long
		//
		badBookmark.setMemberId("123456789012345678901234567890");
		this.mockMvc.perform(post("/bookmark/v1/member/123456789012345678901234567890/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId(), bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(badBookmark)))
			.andExpect(status().is(400));

		//
		// missing auth header
		//
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(bookmark)))
			.andExpect(status().is(400));

		//
		// invalid auth header
		//
		this.mockMvc.perform(post("/bookmark/v1/member/123/content/bcsite/answer/321")
			.header(BookmarkRestController.AUTH_HEADER_NAME, accessToken(bookmark.getMemberId() + "foo", bookmark.getContentSource()))
			.contentType(MediaType.APPLICATION_JSON)
			.content(mapper.writeValueAsString(bookmark)))
			.andExpect(status().is(401));

	}

}
