package com.babycenter.bookmarkservice.model;

import com.babycenter.JsonTestUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.Date;

import org.junit.Test;

import static org.junit.Assert.*;
import static org.junit.Assert.assertNull;

/**
 * Created by emurphy on 3/17/17.
 */
public class BookmarkTest
{

	@Test
	public void testEquals()
	{
		final Date now = new Date();
		final Bookmark theBookmark = new Bookmark(1L, "AAA", "bcsite", "answer", "123", "Daryll", "OtherDaryll", new Date(now.getTime() - 24 * 60 * 60 * 1000L), now);
		Bookmark theOtherBookmark = new Bookmark(theBookmark); // copy constructor constructs new instance with copied fields

		assertTrue("The copied bookmark is a separate instance", theBookmark != theOtherBookmark);
		assertEquals("The instances with identical fields should be equal", theBookmark, theOtherBookmark);

		//
		// change one field at a time and note that they should not be equal
		//
		theOtherBookmark.setId(theBookmark.getId() + 1);
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setMemberId("BBB");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setContentSource("social");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setContentType("question");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setContentId("321");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setCreateUser("Bob");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setUpdateUser("Bob");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setCreateDate(new Date(theBookmark.getCreateDate().getTime() - 1000L));
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setUpdateDate(new Date(theBookmark.getUpdateDate().getTime() - 1000L));
		assertNotEquals(theBookmark, theOtherBookmark);
	}

	@Test
	public void testHashcode()
	{
		final Date now = new Date();
		final Bookmark theBookmark = new Bookmark(1L, "AAA", "bcsite", "answer", "123", "Daryll", "OtherDaryll", new Date(now.getTime() - 24 * 60 * 60 * 1000L), now);
		Bookmark theOtherBookmark = new Bookmark(theBookmark); // copy constructor constructs new instance with copied fields

		assertTrue("The copied bookmark is a separate instance", theBookmark != theOtherBookmark);
		assertEquals("The instances with identical fields should be equal", theBookmark.hashCode(), theOtherBookmark.hashCode());

		//
		// change one field at a time and note that they should not be equal
		//
		theOtherBookmark.setId(theBookmark.getId() + 1);
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setMemberId("BBB");
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setContentSource("social");
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setContentType("question");
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setContentId("321");
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setCreateUser("Bob");
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setUpdateUser("Bob");
		assertNotEquals(theBookmark, theOtherBookmark);

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setCreateDate(new Date(theBookmark.getCreateDate().getTime() - 1000L));
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());

		theOtherBookmark = new Bookmark(theBookmark);
		theOtherBookmark.setUpdateDate(new Date(theBookmark.getUpdateDate().getTime() - 1000L));
		assertNotEquals(theBookmark.hashCode(), theOtherBookmark.hashCode());
	}
	
	@Test
	public void jsonSerializationTest()
	{
		final Date now = new Date();
		final Date yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000L);
		final Bookmark theBookmark = new Bookmark(
			1L,
			"AAA",
			"bcsite",
			"answer",
			"123",
			"Daryll",
			"OtherDaryll",
			yesterday,
			now);
		
		
		final String json = JsonTestUtils.jsonSerialize(theBookmark, Bookmark.class);
		
		assertNotNull(json);
		
		//
		// should have serialized everything including createDate and updateDate
		//
		assertTrue(JsonTestUtils.hasJsonNumberValue(json, "id", theBookmark.getId()));
		assertTrue(JsonTestUtils.hasJsonStringValue(json, "memberId", theBookmark.getMemberId()));
		assertTrue(JsonTestUtils.hasJsonStringValue(json, "contentSource", theBookmark.getContentSource()));
		assertTrue(JsonTestUtils.hasJsonStringValue(json, "contentType", theBookmark.getContentType()));
		assertTrue(JsonTestUtils.hasJsonStringValue(json, "contentId", theBookmark.getContentId()));
		assertTrue(JsonTestUtils.hasJsonStringValue(json, "createUser", theBookmark.getCreateUser()));
		assertTrue(JsonTestUtils.hasJsonStringValue(json, "updateUser", theBookmark.getUpdateUser()));
		assertTrue(JsonTestUtils.hasJsonNumberValue(json, "createDate", theBookmark.getCreateDate().getTime()));
		assertTrue(JsonTestUtils.hasJsonNumberValue(json, "updateDate", theBookmark.getUpdateDate().getTime()));
		
	}
	
	@Test
	public void jsonDeserializationTest()
	{
		final Date now = new Date();
		final Date yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000L);
		final Bookmark theBookmark = new Bookmark(
			1L,
			"AAA",
			"bcsite",
			"answer",
			"123",
			"Daryll",
			"OtherDaryll",
			yesterday,
			now);
		
		
		final String json = JsonTestUtils.jsonSerialize(theBookmark, Bookmark.class);
		
		//
		// now deserialize and check results
		//
		Bookmark theOtherBookmark = JsonTestUtils.jsonDeserialize(json, Bookmark.class); // copy constructor constructs new instance with copied fields
		
		assertNotNull(theOtherBookmark);
		assertTrue("The copied bookmark is a separate instance", theBookmark != theOtherBookmark);
		
		//
		// date fields should not have been deserialized
		//
		// assertNull(theOtherBookmark.getCreateDate());
		assertNull(theOtherBookmark.getUpdateDate());
		
		//
		// all other fields should be deserialized
		//
		assertEquals(theBookmark.getId(), theOtherBookmark.getId());
		assertEquals(theBookmark.getMemberId(), theOtherBookmark.getMemberId());
		assertEquals(theBookmark.getContentSource(), theOtherBookmark.getContentSource());
		assertEquals(theBookmark.getContentType(), theOtherBookmark.getContentType());
		assertEquals(theBookmark.getContentId(), theOtherBookmark.getContentId());
		assertEquals(theBookmark.getCreateUser(), theOtherBookmark.getCreateUser());
		assertEquals(theBookmark.getUpdateUser(), theOtherBookmark.getUpdateUser());
	}
	
}
