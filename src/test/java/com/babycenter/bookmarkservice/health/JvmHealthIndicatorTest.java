package com.babycenter.bookmarkservice.health;

import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.endpoint.SystemPublicMetrics;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.Status;
import org.springframework.boot.actuate.metrics.Metric;

import java.util.Arrays;
import java.util.Collection;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

/**
 * Created by ksharma on 3/30/18.
 * Tests for {@link JvmHealthIndicator}.
 */
public class JvmHealthIndicatorTest {

    private static final Logger log = LoggerFactory.getLogger(JvmHealthIndicatorTest.class);

    private static final JvmHealthIndicator jvmHealthIndictor = new JvmHealthIndicator();

    // memory threshold to demonstrate the system is up
    private static final int normal_memory_threshold = 95;
    // memory threshold to demonstrate the system is down
    private static final int abnormal_memory_threshold = 1;

    private static final String JVM_TOTAL_MEMORY_KEY = "mem";
    private static final String JVM_FREE_MEMORY_KEY = "mem.free";
    private static final String JVM_USED_MEMORY_KEY = "mem.used";
    private static Collection<Metric<?>>systemMetrics = null;
    private long jvmTotalMemory = 100L;
    private long jvmAvailableMemory = 30L;


    @Before
    public void init() {
        Metric<Long> jvmTotalMemoryMetric = new Metric<Long>(JVM_TOTAL_MEMORY_KEY, jvmTotalMemory);
        Metric<Long> jvmAvailableMemoryMetric = new Metric<Long>(JVM_FREE_MEMORY_KEY, jvmAvailableMemory);
        systemMetrics = Arrays.asList(jvmTotalMemoryMetric, jvmAvailableMemoryMetric);
    }

    @Test
    public void doHealthCheck_returnsSystemUpIndicator_if_jvmUsedMemoryPercentage_lessThanThreshold() {
        SystemPublicMetrics metrics = mock(SystemPublicMetrics.class);
        given(metrics.metrics()).willReturn(systemMetrics);
        jvmHealthIndictor.setJvmMemoryThreshold(normal_memory_threshold);
        jvmHealthIndictor.setSystemMetric(metrics);
        Health health = this.jvmHealthIndictor.health();
        assertThat(health.getDetails().get(JVM_TOTAL_MEMORY_KEY)).isNotNull();
        assertThat(health.getDetails().get(JVM_USED_MEMORY_KEY)).isNotNull();
        assertThat(health.getStatus()).isEqualTo(Status.UP);
    }


    @Test
    public void doHealthCheck_returnsSystemDownIndicator_if_jvmUsedMemoryPercentage_moreThanThreshold() {
        SystemPublicMetrics metrics = mock(SystemPublicMetrics.class);
        given(metrics.metrics()).willReturn(systemMetrics);
        jvmHealthIndictor.setJvmMemoryThreshold(abnormal_memory_threshold);
        jvmHealthIndictor.setSystemMetric(metrics);
        Health health = this.jvmHealthIndictor.health();
        assertThat(health.getDetails().get(JVM_TOTAL_MEMORY_KEY)).isNotNull();
        assertThat(health.getDetails().get(JVM_USED_MEMORY_KEY)).isNotNull();
        assertThat(health.getStatus()).isEqualTo(Status.DOWN);
    }

}
