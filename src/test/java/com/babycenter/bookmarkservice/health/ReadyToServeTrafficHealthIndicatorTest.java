package com.babycenter.bookmarkservice.health;

import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.Status;

import static org.assertj.core.api.Assertions.assertThat;

import java.io.File;
import java.io.IOException;

/**
 * Created by ksharma on 3/27/18.
 * Tests for {@link ReadyToServeTrafficHealthIndicator}.
 */
public class ReadyToServeTrafficHealthIndicatorTest {

    private static final Logger log = LoggerFactory.getLogger(ReadyToServeTrafficHealthIndicatorTest.class);

    private final ReadyToServeTrafficHealthIndicator indicator = new ReadyToServeTrafficHealthIndicator();
    private File servingFile = null;

    @Before
    public void init() {
        try {
            this.servingFile = File.createTempFile("servingTmp", ".txt");
        } catch (IOException ex) {
            log.error("Couldn't create temp file", ex);
        }
    }

    @Test
    public void doHealthCheck_returnsSystemUpIndicator_if_servingFileExists(){
        this.indicator.setServingFileLocation(servingFile.getAbsolutePath());
        Health health = this.indicator.health();
        assertThat(health.getDetails().get("servingFile")).isNotNull();
        assertThat(health.getDetails().get("hello")).isNotNull();
    }

    @Test
    public void doHealthCheck_returnsSystemDownIndicator_if_servingFile_DoesNotExists(){
        this.indicator.setServingFileLocation(servingFile.getAbsolutePath()+"doesnt_exist");
        Health health = this.indicator.health();
        assertThat(health.getDetails().get("servingFile")).isNotNull();
        assertThat(health.getStatus()).isEqualTo(Status.DOWN);
    }

    @Test
    public void doHealthCheck_returnsSystemDownIndicator_if_defaultservingFile_doesNotExists(){
        this.indicator.setServingFileLocation(null);
        Health health = this.indicator.health();
        assertThat(health.getDetails().get("servingFile")).isNotNull();
        assertThat(health.getStatus()).isEqualTo(Status.DOWN);
    }

}
