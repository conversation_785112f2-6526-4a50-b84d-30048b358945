package com.babycenter;

import com.babycenter.bookmarkservice.model.Bookmark;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

/**
 * Created by emurphy on 3/31/17.
 */
public final class JsonTestUtils
{
	public static final <T> String jsonSerialize(final T bookmark, Class<T> clazz)
	{
		final ObjectMapper mapper = new ObjectMapper();
		
		String s = null;
		try
		{
			s = mapper.writeValueAsString(bookmark);
		}
		catch (JsonProcessingException e)
		{
			e.printStackTrace();
		}
		return s;
	}
	
	public static final <T> T jsonDeserialize(final String json, Class<T> clazz)
	{
		if (null != json)
		{
			final ObjectMapper mapper = new ObjectMapper();
			try
			{
				return mapper.readValue(json, clazz);
			}
			catch (IOException e)
			{
				e.printStackTrace();
			}
		}
		return null;
	}
	
	
	public static final boolean hasJsonStringValue(final String json, final String field, final String value)
	{
		return hasJsonValue(json, field, '\"' + value + '\"');
	}
	
	public static final boolean hasJsonNumberValue(final String json, final String field, final Number value)
	{
		return hasJsonValue(json, field, value.toString());
	}
	
	private static final boolean hasJsonValue(final String json, final String field, final String value)
	{
		final int theFieldIndex = json.indexOf('\"' + field + '\"');
		if (theFieldIndex > 0)
		{
			//
			// skip field and expect a colon
			//
			int theValueIndex = theFieldIndex + field.length() + 2;
			
			//
			// skip whitespace
			//
			while ((theValueIndex < json.length()) && Character.isWhitespace(json.charAt(theValueIndex)))
			{
				theValueIndex += 1;
			}
			if (theValueIndex < json.length())
			{
				//
				// must be a colon
				//
				if (':' == json.charAt(theValueIndex))
				{
					// skip the colon
					theValueIndex += 1;
					
					//
					// skip whitespace
					//
					while ((theValueIndex < json.length()) && Character.isWhitespace(json.charAt(theValueIndex)))
					{
						theValueIndex += 1;
					}
					if ((theValueIndex + value.length()) < json.length())
					{
						//
						// field must be next
						//
						return value.equals(json.substring(theValueIndex, theValueIndex + value.length()));
					}
				}
			}
		}
		
		return false;
	}
	
	public static final boolean hasJsonField(final String json, final String field)
	{
		final int theFieldIndex = json.indexOf('\"' + field + '\"');
		if (theFieldIndex > 0)
		{
			//
			// skip field and expect a colon
			//
			int theValueIndex = theFieldIndex + field.length() + 2;
			
			//
			// skip whitespace
			//
			while ((theValueIndex < json.length()) && Character.isWhitespace(json.charAt(theValueIndex)))
			{
				theValueIndex += 1;
			}
			if (theValueIndex < json.length())
			{
				//
				// must be a colon
				//
				return (':' == json.charAt(theValueIndex));
			}
		}
		
		return false;
	}
	
}
