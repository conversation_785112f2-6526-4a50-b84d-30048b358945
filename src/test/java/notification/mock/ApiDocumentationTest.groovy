package notification.mock

import notification.data.KeyEncrypter
import notification.pojos.Notification
import notification.pojos.Subscription
import notification.repositories.MemberActivityStatusRepository
import notification.repositories.NotificationRepository
import notification.repositories.SubscriptionRepository
import sun.misc.BASE64Encoder

import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.restdocs.JUnitRestDocumentation
import org.springframework.restdocs.payload.JsonFieldType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit4.SpringRunner
import org.springframework.test.context.web.WebAppConfiguration
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.context.WebApplicationContext

import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.documentationConfiguration
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post
import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@RunWith(SpringRunner.class)
@SpringBootTest
@WebAppConfiguration
@ActiveProfiles('sandbox-ext')
class ApiDocumentationTest {
	@Rule
	public final JUnitRestDocumentation restDocumentation = new JUnitRestDocumentation("target/generated-snippets")

	@Autowired
	private WebApplicationContext context
	
	@Autowired
	private SubscriptionRepository subscribeRepo
	
	@Autowired
	private NotificationRepository notificationRepo
	
	@Autowired
	private MemberActivityStatusRepository memberActivityStatusRepo

	private MockMvc mockMvc


	@Before
	void setUp() {
		this.mockMvc = MockMvcBuilders.webAppContextSetup(this.context)
				.apply(documentationConfiguration(this.restDocumentation))
				.build()
	}

	@Test
	void index() {
		this.mockMvc.perform(get("/").accept(MediaType.APPLICATION_JSON))
			.andExpect(status().isOk())
			.andDo(document("home"))
	}
	
	@Test
	void notificationsV1() {
		(1..5).each {
			Subscription sub1 = new Subscription('en_GB', 2L, it)
			subscribeRepo.save(sub1)
		}
		(1..5).each {
			Notification note = new Notification('en_GB', 1000 + it, it, 'comment', 1)
			note.snippet = 'Post ' + it
			notificationRepo.save(note)
		}
		String encrypted = KeyEncrypter.encode('en_GB,2')
		this.mockMvc.perform(get("/v1/user/notifications?userkey=$encrypted").accept(MediaType.APPLICATION_JSON))
		.andExpect(status().isOk())
		.andDo(document("notifications",
			responseFields(
			fieldWithPath("unreadCount").type(JsonFieldType.NUMBER).description("unread notifications"),
			fieldWithPath("notifications.[].snippet").description("payload snippet"),
			fieldWithPath("notifications.[].count").type(JsonFieldType.NUMBER).description("notification count for this content"),
			fieldWithPath("notifications.[].createDateTime").description("timestamp"),
			fieldWithPath("notifications.[].read").type(JsonFieldType.BOOLEAN).description("read/unread flag"),
			fieldWithPath("notifications.[].contentId").type(JsonFieldType.NUMBER).description("content id"),
			fieldWithPath("notifications.[].replyId").type(JsonFieldType.NUMBER).optional().description("comment reply id (required for reply)"),
			fieldWithPath("notifications.[].postId").type(JsonFieldType.NUMBER).optional().description("post id for comment reply, only required for reply on US social"),
			fieldWithPath("notifications.[].action").description("notification type comment/reply")
			)))
	}

	@Test
	void notificationsV2() {
		(1..5).each {
			Subscription sub1 = new Subscription('en_GB', 2L, it)
			subscribeRepo.save(sub1)
		}
		(1..5).each {
			Notification note = new Notification('en_GB', 1000 + it, it, 'comment', 1)
			note.snippet = 'Post ' + it
			notificationRepo.save(note)
		}
		String encoded = new BASE64Encoder().encodeBuffer('en_GB,2'.getBytes("UTF-8"));
		this.mockMvc.perform(get("/v2/user/notifications?userkey=$encoded")
				.header(HttpHeaders.AUTHORIZATION, "authvalue")
				.accept(MediaType.APPLICATION_JSON))
			.andExpect(status().isOk())
			.andDo(document("notifications",
			responseFields(
				fieldWithPath("unreadCount").type(JsonFieldType.NUMBER).description("unread notifications"),
				fieldWithPath("notifications.[].snippet").description("payload snippet"),
				fieldWithPath("notifications.[].count").type(JsonFieldType.NUMBER).description("notification count for this content"),
				fieldWithPath("notifications.[].createDateTime").description("timestamp"),
				fieldWithPath("notifications.[].read").type(JsonFieldType.BOOLEAN).description("read/unread flag"),
				fieldWithPath("notifications.[].contentId").type(JsonFieldType.NUMBER).description("content id"),
				fieldWithPath("notifications.[].replyId").type(JsonFieldType.NUMBER).optional().description("comment reply id (required for reply)"),
				fieldWithPath("notifications.[].postId").type(JsonFieldType.NUMBER).optional().description("post id for comment reply, only required for reply on US social"),
				fieldWithPath("notifications.[].action").description("notification type comment/reply")
			)))
	}

	@Test
	void acknowledgeV1() {
		String encrypted = KeyEncrypter.encode('en_GB,2')
		this.mockMvc.perform(get("/v1/user/acknowledge?userkey=$encrypted").accept(MediaType.APPLICATION_JSON))
		.andExpect(status().isOk())
		.andDo(document("acknowledge",
			responseFields(
			fieldWithPath("timestamp").type(JsonFieldType.NUMBER).description("timestamp of the acknowledgement")
			)))
	}

	@Test
	void acknowledgeV2() {
		String encoded = new BASE64Encoder().encodeBuffer('en_GB,2'.getBytes("UTF-8"));
		this.mockMvc.perform(post("/v2/user/acknowledge?userkey=$encoded")
			.header(HttpHeaders.AUTHORIZATION, "authvalue")
			.accept(MediaType.APPLICATION_JSON))
			.andExpect(status().isOk())
			.andDo(document("acknowledge",
			responseFields(
				fieldWithPath("unreadCount").type(JsonFieldType.NUMBER).description("unread notifications"),
				fieldWithPath("notifications.[].snippet").description("payload snippet"),
				fieldWithPath("notifications.[].count").type(JsonFieldType.NUMBER).description("notification count for this content"),
				fieldWithPath("notifications.[].createDateTime").description("timestamp"),
				fieldWithPath("notifications.[].read").type(JsonFieldType.BOOLEAN).description("read/unread flag"),
				fieldWithPath("notifications.[].contentId").type(JsonFieldType.NUMBER).description("content id"),
				fieldWithPath("notifications.[].replyId").type(JsonFieldType.NUMBER).optional().description("comment reply id (required for reply)"),
				fieldWithPath("notifications.[].postId").type(JsonFieldType.NUMBER).optional().description("post id for comment reply, only required for reply on US social"),
				fieldWithPath("notifications.[].action").description("notification type comment/reply")
			)))
	}

	@Test
	void readV2() {
		String encoded = new BASE64Encoder().encodeBuffer('en_GB,2'.getBytes("UTF-8"));
		this.mockMvc.perform(post("/v2/user/read?userkey=$encoded")
				.content("{ userId: 2, countryCode: 'en_GB', contentId: 1, action:'comment' }")
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
				.header(HttpHeaders.AUTHORIZATION, "authvalue")
				.accept(MediaType.APPLICATION_JSON))
			.andExpect(status().isOk())
			.andDo(document("read",
			responseFields(
				fieldWithPath("unreadCount").type(JsonFieldType.NUMBER).description("unread notifications"),
				fieldWithPath("notifications.[].snippet").description("payload snippet"),
				fieldWithPath("notifications.[].count").type(JsonFieldType.NUMBER).description("notification count for this content"),
				fieldWithPath("notifications.[].createDateTime").description("timestamp"),
				fieldWithPath("notifications.[].read").type(JsonFieldType.BOOLEAN).description("read/unread flag"),
				fieldWithPath("notifications.[].contentId").type(JsonFieldType.NUMBER).description("content id"),
				fieldWithPath("notifications.[].replyId").type(JsonFieldType.NUMBER).optional().description("comment reply id (required for reply)"),
				fieldWithPath("notifications.[].postId").type(JsonFieldType.NUMBER).optional().description("post id for comment reply, only required for reply on US social"),
				fieldWithPath("notifications.[].action").description("notification type comment/reply")
			)))
	}
}
