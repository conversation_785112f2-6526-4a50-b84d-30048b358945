package notification.repositories

import notification.pojos.ContentHashKey
import notification.pojos.MemberActivityStatus
import notification.pojos.Notification
import notification.pojos.NotificationId
import notification.pojos.Subscription
import notification.pojos.SubscriptionId
import notification.pojos.UserHashKey
import notification.pojos.UserHashKeyMarshaller

import com.google.gson.Gson
import com.google.gson.GsonBuilder

import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit4.SpringRunner

import static org.junit.Assert.*

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles('sandbox-ext')
class IntegrationTest {
	@Autowired
	private SubscriptionRepository subscribeRepo

	@Autowired
	private NotificationRepository notificationRepo

	@Autowired
	private MemberActivityStatusRepository memberActivityStatusRepo

	private UserHashKeyMarshaller marshaller = new UserHashKeyMarshaller()

	private Gson gson = new GsonBuilder()
		.setPrettyPrinting()
		.create()


	@Test
	void testSubscription() {
		(1..2).each {
			SubscriptionId id1 = SubscriptionId.newId('en_GB', it, 200L)
			if (subscribeRepo.exists(id1)) subscribeRepo.delete(id1)

			SubscriptionId id2 = SubscriptionId.newId('de_DE', 1000 + it, 300L)
			if (subscribeRepo.exists(id2)) subscribeRepo.delete(id2)
		}

		(1..2).each {

			Subscription sub1 = new Subscription('en_GB', it, 200L)
			subscribeRepo.save(sub1)

			println 'sub1 ' + sub1

			Subscription sub2 = new Subscription('de_DE', 1000 + it, 300L)
			subscribeRepo.save(sub2)

			println 'sub2 ' + sub2


			Subscription result1 = subscribeRepo.findOne(sub1.id)
			assertEquals result1, sub1

			println 'result1 ' + result1
			Subscription result2 = subscribeRepo.findOne(sub2.id)
			assertEquals result2, sub2

			//subscribeRepo.delete(result1)
			//subscribeRepo.delete(result2)
		}

		Pageable pageable = new PageRequest(0, 50)
		Page<Subscription> subscriptions1 = subscribeRepo.findByContentKey(new ContentHashKey('en_GB', 200L), pageable)
		assertEquals 2, subscriptions1.content.size()
		assertFalse subscriptions1.hasNext()

		pageable = new PageRequest(0, 1)
		subscriptions1 = subscribeRepo.findByContentKey(new ContentHashKey('en_GB', 200L), pageable)
		assertEquals 1, subscriptions1.content.size()
		assertTrue subscriptions1.hasNext()
		subscriptions1 = subscribeRepo.findByContentKey(new ContentHashKey('en_GB', 200L), subscriptions1.nextPageable())
		assertEquals 1, subscriptions1.content.size()
		assertFalse subscriptions1.hasNext()

		pageable = new PageRequest(0, 50)
		Page<Subscription> subscriptions2 = subscribeRepo.findByContentKey(new ContentHashKey('de_DE', 300L), pageable)
		assertEquals 2, subscriptions2.content.size()
		assertFalse subscriptions2.hasNext()

		pageable = new PageRequest(0, 1)
		subscriptions2 = subscribeRepo.findByContentKey(new ContentHashKey('de_DE', 300L), pageable)
		assertEquals 1, subscriptions2.content.size()
		assertTrue subscriptions2.hasNext()
		subscriptions2 = subscribeRepo.findByContentKey(new ContentHashKey('de_DE', 300L), subscriptions2.nextPageable())
		assertEquals 1, subscriptions2.content.size()
		assertFalse subscriptions2.hasNext()
	}

	@Test
	void testNotification() {
		(1..2).each {
			NotificationId id1 = NotificationId.newId('en_GB', it, 200L, 'comment')
			if(notificationRepo.exists(id1)) notificationRepo.delete(id1)

			NotificationId id2 = NotificationId.newId('de_DE', 1000 + it, 300L, 'comment')
			if(notificationRepo.exists(id2)) notificationRepo.delete(id2)
		}
		(1..2).each {

			Notification note1 = new Notification('en_GB', it, 200L, 'comment', 1)
			note1.snippet = 'Post ' + (100 + it)
			notificationRepo.save(note1)

			println 'note1 ' + note1


			Notification note2 = new Notification('de_DE', 1000 + it, 300L, 'comment', 1)
			note2.snippet = 'Post ' + (200 + it)
			notificationRepo.save(note2)

			println 'note2 ' + note2


			Notification result1 = notificationRepo.findOne(note1.id)
			assertEquals result1, note1

			println 'result1 ' + result1

			Notification result2 = notificationRepo.findOne(note2.id)
			assertEquals result2, note2
			println 'result2 ' + result2

			Pageable pageable = new PageRequest(0, 200)

			UserHashKey userKey1 = new UserHashKey('en_GB', it)
			List<Notification> notifications1 = notificationRepo.findByUserKey(userKey1, pageable).content
			assertEquals 1, notifications1.size()
			assertEquals result1, notifications1[0]
			assertEquals note1.snippet, notifications1[0].snippet

			notifications1 = notificationRepo.findByUserKeyHash(marshaller.marshall(userKey1))
			assertEquals 1, notifications1.size()
			assertEquals result1, notifications1[0]
			assertEquals note1.snippet, notifications1[0].snippet

			UserHashKey userKey2 = new UserHashKey('de_DE', 1000 + it)
			List<Notification> notifications2 = notificationRepo.findByUserKey(userKey2, pageable).content
			assertEquals 1, notifications2.size()
			assertEquals result2, notifications2[0]
			assertEquals note2.snippet, notifications2[0].snippet

			notifications2 = notificationRepo.findByUserKeyHash(marshaller.marshall(userKey2))
			assertEquals 1, notifications2.size()
			assertEquals result2, notifications2[0]
			assertEquals note2.snippet, notifications2[0].snippet


			//notificationRepo.delete(result1)
			//notificationRepo.delete(result2)
		}

		/*
		Pageable pageable = new PageRequest(0, 200)

		def date = new Date().parse("MM/dd/yyyy HH:mm:ss", '07/10/2015 14:00:00')
		Page<Notification> notifications = notificationRepo.findByCreateDateTimeLessThan(date, pageable)

		assertEquals 7, notifications.content.size()
		*/

	}

	@Test
	void testMemberActivityStatus() {
		UserHashKey key1 = new UserHashKey('en_CA', 2000L)
		if(memberActivityStatusRepo.exists(key1)) memberActivityStatusRepo.delete(key1)

		MemberActivityStatus activityStatus1 = new MemberActivityStatus('en_CA', 2000L, 1)
		memberActivityStatusRepo.save(activityStatus1)

		MemberActivityStatus result1 = memberActivityStatusRepo.findOne(new UserHashKey('en_CA', 2000L))
		println result1
	}

	@Test
	void testGenericNotifications() {
		def validData = [countryCode: 'en_US',
			userIds: [10001L, 10002L, 10003L],
			contentId: 10000L,
			genericContent: [type: 'AdmMessage',
						imgClass: 'icon_welcome',
						href: '/a/groups',
						message: '''Welcome to community'''
						]
			]

		Notification note1 = new Notification('en_US', 2000L, 10000L, 'generic', 1)
		note1.genericContent = validData.genericContent

		notificationRepo.save(note1)

		Notification note2 = notificationRepo.findOne(note1.id)

		assertEquals note2.genericContent, note1.genericContent
	}
}
