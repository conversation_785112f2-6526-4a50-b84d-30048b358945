package notification.db

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.amazonaws.services.dynamodbv2.document.Table
import com.amazonaws.services.dynamodbv2.model.AttributeDefinition
import com.amazonaws.services.dynamodbv2.model.CreateTableRequest
import com.amazonaws.services.dynamodbv2.model.KeySchemaElement
import com.amazonaws.services.dynamodbv2.model.KeyType
import com.amazonaws.services.dynamodbv2.model.ProvisionedThroughput
import com.amazonaws.services.dynamodbv2.model.ScalarAttributeType

import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit4.SpringRunner

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles('dev')
class BootstrapTableTest {
	@Autowired
	private DynamoDB dynamoDB
	
	@Test
	@Ignore
	void testCreateTable() {
		createTable()
	}
	
	private void createTable() {
		List<KeySchemaElement> keySchemaSubscription =[
			new KeySchemaElement()
			.withAttributeName('ContentHashKey')
			.withKeyType(KeyType.HASH),
			new KeySchemaElement()
			.withAttributeName('UserRangeKey')
			.withKeyType(KeyType.RANGE)
		]
		List<KeySchemaElement> keySchemaNotification =[
			new KeySchemaElement()
				.withAttributeName('UserHashKey')
				.withKeyType(KeyType.HASH),
			new KeySchemaElement()
				.withAttributeName('ContentRangeKey')
				.withKeyType(KeyType.RANGE)
		]
		List<KeySchemaElement> keySchemaCount =[
			new KeySchemaElement()
				.withAttributeName('UserHashKey')
				.withKeyType(KeyType.HASH)
		]

		def keySchemas = [
			'Subscription': keySchemaSubscription,
			'Notification': keySchemaNotification,
			'MemberActivityStatus': keySchemaCount
			]
		
		List<AttributeDefinition> attributeDefinitionsSubscription = [
			new AttributeDefinition()
			.withAttributeName('ContentHashKey')
			.withAttributeType(ScalarAttributeType.S),
			new AttributeDefinition()
				.withAttributeName('UserRangeKey')
				.withAttributeType(ScalarAttributeType.N)
		]
		List<AttributeDefinition> attributeDefinitionsNotification = [
			new AttributeDefinition()
				.withAttributeName('UserHashKey')
				.withAttributeType(ScalarAttributeType.S),
			new AttributeDefinition()
				.withAttributeName('ContentRangeKey')
				.withAttributeType(ScalarAttributeType.S)
		]
		List<AttributeDefinition> attributeDefinitionsCount = [
			new AttributeDefinition()
				.withAttributeName('UserHashKey')
				.withAttributeType(ScalarAttributeType.S)
		]
		def attributeDefinitions = [
			'Subscription': attributeDefinitionsSubscription,
			'Notification': attributeDefinitionsNotification,
			'MemberActivityStatus': attributeDefinitionsCount
			]
		
		['Subscription', 'Notification', 'MemberActivityStatus'].each {
			Table table = dynamoDB.getTable(it)
			if (!doesTableExist(table)) {
				CreateTableRequest request = new CreateTableRequest()
				.withTableName(it)
				.withKeySchema(keySchemas[it])
				.withProvisionedThroughput( new ProvisionedThroughput()
					.withReadCapacityUnits(25)
					.withWriteCapacityUnits(25)
				)
				
				request.attributeDefinitions = attributeDefinitions[it]
				
				table = dynamoDB.createTable(request)
				
				table.waitForActive()
				
				println 'Table ' + it + ' created successfully'
			}
			else {
				println 'Table ' + it + ' already exists'
			}
		}
	}
	
	boolean doesTableExist(Table table) {
		try {
			table?.describe()
		}
		catch (all) {
			false
		}
	}

}
