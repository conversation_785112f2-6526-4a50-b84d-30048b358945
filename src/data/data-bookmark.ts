import { RowDataPacket } from 'mysql2/promise';

export interface Bookmark {
  readonly id: number | undefined;
  readonly member_id: string;
  readonly content_source: string;
  readonly content_type: string;
  readonly content_id: string;
  readonly create_date: Date;
  readonly update_date: Date;
  readonly create_user: string | null;
  readonly update_user: string | null;
}

export type BookmarkRow = Bookmark & RowDataPacket;
