// DynamoDB-only notification data types

export interface NotificationId {
  userKey: UserHashKey;
  contentActionKey: ContentActionKey;
}

export interface UserHashKey {
  countryCode: string;
  userId: string;
}

export interface ContentActionKey {
  action: string;
  contentId: string;
  createDate: Date;
}

export interface Notification {
  id?: NotificationId;
  userKey: UserHashKey;
  contentActionKey: ContentActionKey;
  postId?: number;
  replyId?: number;
  snippet?: string;
  genericContent?: Record<string, unknown>;
  count: number;
  createDateTime: Date;
  ttl?: number;
}

export interface NotificationRequest {
  countryCode: string;
  userId: string;
  contentId: string;
  action: string;
}

export interface NotificationListRequest {
  countryCode: string;
  userId: string;
  pageIndex?: number;
  pageSize?: number;
}

export interface NotificationUpdateRequest {
  id: NotificationId;
  count?: number;
  snippet?: string;
  genericContent?: Record<string, unknown>;
}

export interface PaginatedNotificationResult {
  notifications: Notification[];
  totalCount: number;
  pageIndex: number;
  pageSize: number;
}

export enum NotificationAction {
  REPLY = 'reply',
  LIKE = 'like',
  MENTION = 'mention',
  FOLLOW = 'follow',
}
