import { AwilixContainer, createContainer } from 'awilix';
import * as express from 'express';
import { awilixRegisterApp, awilixRegisterRequest } from './awilix-register';
import { AppScopeRegister, RequestScopeRegister } from './container-scope';

export default function configAwilix(
  app: express.Application,
  appScopeRegister: AppScopeRegister,
  requestScopeRegister: Omit<RequestScopeRegister, 'request'>,
): AwilixContainer {
  const container = createContainer();

  awilixRegisterApp(container, appScopeRegister);
  awilixRegisterRequest(container, app, requestScopeRegister);

  return container;
}
