import { asFunction, asValue, AwilixContainer, Lifetime, Resolver } from 'awilix';
import express from 'express';
import { AppScopeRegister, RequestScopeRegister } from './container-scope';

/**
 * Registers all app-scoped dependencies.
 *
 * App-scoped services are resolved ONCE for the entire app lifetime. That means:
 * - First time resolving app-scoped dependency will evaluate the resolver;
 * - Subsequent usages of that dependency will share the same value.
 *
 * @param container Container where dependencies will be registered.
 * @param register Dependencies to be registered.
 */
export function awilixRegisterApp(container: AwilixContainer, register: AppScopeRegister): void {
  let key: keyof AppScopeRegister;
  for (key in register) {
    const value = register[key];
    container.register(
      key,
      // SINGLETON lifetime makes this be resolved once per container
      asFunction((di) => value(di), { lifetime: Lifetime.SINGLETON }),
    );
  }
}

/**
 * Adds a middleware that will register all request-scoped dependencies for every request.
 *
 * Request-scoped services are resolved ONCE per request. That means:
 * - First time using a request-scoped dependency per request will evaluate the resolver;
 * - Subsequent usages of the same dependency on the same request will reuse the value;
 * - Each request will resolve their own version of the dependency.
 *
 * @param container Container where dependencies will be registered (a sub-scope is created per request).
 * @param expressApp Express app that will get the middleware.
 * @param register Dependencies to be registered.
 */
export function awilixRegisterRequest(
  container: AwilixContainer,
  expressApp: express.Application,
  register: Omit<RequestScopeRegister, 'request'>,
): void {
  let key: keyof typeof register;
  for (key in register) {
    const value = register[key];
    container.register(
      key,
      // SCOPED lifetime makes this be resolved once per scope
      asFunction((di) => value(di), { lifetime: Lifetime.SCOPED }),
    );
  }
  expressApp.use((req, _res, next) => {
    const scope = container.createScope(); // Create a scope for each request
    req.di = scope.cradle; // DI of request is the scope
    req.register = (name: string, resolver: Resolver<unknown>): AwilixContainer =>
      scope.register(name, resolver); // Util to register to the scope
    req.register('request', asValue(req)); // Registers the current request to the scope
    return next();
  });
}
