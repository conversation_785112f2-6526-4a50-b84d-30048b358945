import express from 'express';
import { Pool } from 'mysql2/promise';
import BcAuthService from '../auth/bc/bc-auth-service';
import { ValidateUserTokenPayload } from '../auth/validate-user-token';
import { ConfigData } from '../config/environment';
import AppLogger from '../logs/app-logger';
import StatsdService from '../statsd/statsd-service';
import BookmarkService from '../services/bookmark/bookmark-service';
import ProfileEventService from '../services/profile-event/profile-event-service';
import NotificationService from '../services/notification/notification-service';

/**
 * Use 'ContainerScope' for full type.
 */
export interface AppScope {
  readonly configs: ConfigData;
  readonly metrics: StatsdService;
}

/**
 * Use 'ContainerScope' for full type.
 */
export interface RequestScope {
  readonly request: express.Request;
  readonly requestId: string;
  readonly logger: AppLogger;
  readonly user: ValidateUserTokenPayload;
  readonly bcAuthService: BcAuthService;
  readonly bookmarkService: BookmarkService;
  readonly notificationService: NotificationService;
  readonly profileEventService: ProfileEventService;
  readonly dbPool: Pool;
}
/*
    requestId: ({ request }) => request.requestId,
    logger,
    user,
    bcAuthService,
    bookmarkService: ( ) => bookmarkService,
    profileEventService: () => profileEventService,  // TODO: Make this not hardcoded
 */

export type ContainerScope = AppScope & RequestScope;

export type AppResolver<T> = (di: AppScope) => T;

export type AppScopeRegister = {
  [P in keyof AppScope]: AppResolver<AppScope[P]>;
};

export type RequestResolver<T> = (di: ContainerScope) => T;

export type RequestScopeRegister = {
  [P in keyof RequestScope]: RequestResolver<RequestScope[P]>;
};

export default ContainerScope;
