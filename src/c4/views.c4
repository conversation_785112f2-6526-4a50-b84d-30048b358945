views {
  // System Context View
  view index {
    title 'System Context Diagram'
    description 'High-level overview of the system and its interactions.'
    include
      bcWeb,
      wteWeb,
      endUser,
      fedGQL,
      // registry,
      mobile,
  }

  view afcService {
    title 'App First Content Service'
    description 'App First Content Service'
    include
      mobile.bcAndroid,
      mobile.bciOS,
      deToolchain.afcCMS,
      fedGQL.gateway.activeAFC,
      fedGQL.intGateway.publishAFC,
      fedGQL.afcSubgraph,
      fedGQL.intAfcSubgraph,
      fedGQL.afcServiceDB

  }

  view notificationSystem {
    title 'BabyCenter Notifications'
    description 'System Overview for BabyCenter Notifications (now part of consolidated Bookmark & Notification service)'

    include
      mobile.bciOS,
      mobile.bcAndroid,
      bcWeb.bcReactApp,
      // bcWeb.bcReactServer,
      bcWeb.bcGql,
      bcBookmarkNotification.*,
      bcWeb.bcCommunityEvents,
  	autoLayout TopBottom 154 60
	}

  view bookmarkSystem {
    title 'BabyCenter Bookmarks'
    description 'System Overview for BabyCenter Bookmark Service (now part of consolidated Bookmark & Notification service)'

    include
      mobile.bciOS,
      mobile.bcAndroid,
      bcWeb.bcReactApp,
      bcWeb.bcGql,
      bcBookmarkNotification.*,
      bcAuth.bcAuthService,
  	autoLayout TopBottom 154 60
  }

  view bookmarkNotificationConsolidated {
    title 'Consolidated Bookmark & Notification Service'
    description 'Unified service handling both bookmark and notification operations with shared infrastructure'

    include
      bcBookmarkNotification.*,
      bcAuth.bcAuthService,
      wteCommunityApi.communityApi,
  	autoLayout LeftRight 100 50
  }

  view prediktor {
    title 'Prediktor System'
    description 'Overview of the Prediktor system and its components.'

    include
      prediktor.*
  }
}