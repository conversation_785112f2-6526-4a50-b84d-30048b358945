model {
  system ion "Ion Platform" {
    description 'The ion web site builder and data exporting'

    container ionAuthoringTool 'Ion Authoring Tool' {
      description 'Ion website builder'
      style {
        shape browser
      }

      component viacordSurveyBcCreative 'ViaCord Survey BC Creative' {
        description 'Ion pages and form for BC viacord survey'
      }

      component viacordSurveyWteCreative 'ViaCord Survey WTE Creative' {
        description 'Ion pages and form for WTE viacord survey'
      }

      component aeroflowDirectStandaloneCreative 'Aeroflow Direct Standalone (Feeding Frenzy)' {
        description 'Ion pages and form for Aeroflow Direct standalone'
      }

      component currentSweepsCreative 'Current Sweeps (Baby Bump Giveaway)' {
        description 'Ion pages and form for Baby Bump Giveaway'
      }

      component babyBumpGiveawayThankYouCreative 'Baby Bump Giveaway - Thank You' {
        description 'Ion pages and form for Baby Bump Giveaway - Thank You'
      }
    }

    container ionDataRepository 'Ion Internal Data' {
      description 'Ion internal data repository'
    }

    container ionHostedSite 'Ion Hosted Pages' {
      description 'Website Pages hosted by Ion'
      style {
        shape browser
      }
    }

    container ionIntegrations 'Ion Integrations' {
      component ppdeExportToAirflowIntegration 'PPDE Export To Airflow' {
        description 'Ion CSV to FTP integration to /inbox'
      }

      component sweepsSkylerHistoricalIntegration 'Sweeps - Skyler (Historical)' {
        description 'Ion CSV to FTP ingration to /inbox/historical'
      }

      component viaCordProductionIntegration 'viaCord BC Production Integration' {
        description 'Ion JSON API call integration to Standalone Campaign REST API for BC viacord survey'
      }

      component viaCordWteProductionIntegration 'viaCord WTE Production Integration' {
        description 'Ion JSON API call integration to Standalone Campaign REST API for WTE viacord survey'
      }
    }

    container ionScriplets 'Ion Scriplets' {
      component aeroflowConsentFeb2025Scriplet 'Aeroflow consent new fields - Feb 2025' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }

      component aeroflowConsentSep2024Scriplet 'Aeroflow consent new fields - Sep 2024' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }

      // Presumably a zombie now because the Feb 2024 (V1 not V2) page used this
      component aeroflowConsentApr2024Scriplet 'Aeroflow consent new fields - Apr 2024' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }

      component leadApiSignupJan2024Scriplet 'Lead API - Signup - New consent - 01/09/24' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }

      component leadApiCervidilScriplet 'Lead API - Cervidil' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }

      component leadApiNaturalWayScriplet 'Lead API - Natural Way' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }

      component leadApiHuggiesScriplet 'Lead API - Huggies' {
        description 'Ion server scriplet for calling Standalone Campaign REST API'
      }
    }
  }

  ionAuthoringTool -> ionDataRepository 'saves page data'
  ionHostedSite -> ionDataRepository 'reads pages, writes form submissions'
  ionIntegrations -> ionDataRepository 'reads form fields'
  ionScriplets -> ionDataRepository 'reads form fields'

  // Page forms submitting data to scriplets
  // We don't store info here at the page level, so linking by the page's parent creative
  ionAuthoringTool.currentSweepsCreative -> ionScriplets.aeroflowConsentFeb2025Scriplet 'Invokes scriplet'
  ionAuthoringTool.currentSweepsCreative -> ionScriplets.leadApiCervidilScriplet 'Invokes scriplet'
  ionAuthoringTool.currentSweepsCreative -> ionScriplets.leadApiNaturalWayScriplet 'Invokes scriplet'
  ionAuthoringTool.currentSweepsCreative -> ionScriplets.leadApiHuggiesScriplet 'Invokes scriplet'
  ionAuthoringTool.aeroflowDirectStandaloneCreative -> ionScriplets.aeroflowConsentSep2024Scriplet 'Invokes scriplet'

  // Campaigns or pages submitting data to scriplets
  ionAuthoringTool.currentSweepsCreative -> ionIntegrations.ppdeExportToAirflowIntegration 'Invokes integration'
  ionAuthoringTool.currentSweepsCreative -> ionIntegrations.sweepsSkylerHistoricalIntegration 'Invokes integration'
  ionAuthoringTool.viacordSurveyBcCreative -> ionIntegrations.viaCordProductionIntegration 'Invokes integration'
  ionAuthoringTool.viacordSurveyWteCreative -> ionIntegrations.viaCordWteProductionIntegration 'Invokes integration'

  // Integrations pushing data
  ionIntegrations.ppdeExportToAirflowIntegration -> edhFtp.ftpServer 'Uploads CSV data to FTP endpoint (hourly)'
  ionIntegrations.sweepsSkylerHistoricalIntegration -> edhFtp.ftpServer 'Uploads CSV data to FTP endpoint (hourly)'
  ionIntegrations.viaCordProductionIntegration -> wteStandaloneCampaign.wteStandaloneCampaignApi 'Calls API endpoint /client/[id]'
  ionIntegrations.viaCordWteProductionIntegration -> wteStandaloneCampaign.wteStandaloneCampaignApi 'Calls API endpoint /client/[id]'

  // Scriplets pushing data
  ionScriplets.aeroflowConsentFeb2025Scriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns 'Calls API endpoint /client/[id]'
  ionScriplets.aeroflowConsentSep2024Scriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns 'Calls API endpoint /client/[id]'
  ionScriplets.aeroflowConsentApr2024Scriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns 'Calls API endpoint /client/[id]'
  ionScriplets.leadApiSignupJan2024Scriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLuserAddV2 'Calls API endpoint /sweeps/signup'
  ionScriplets.leadApiCervidilScriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns 'Calls API endpoint /client/[id]'
  ionScriplets.leadApiNaturalWayScriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns 'Calls API endpoint /client/[id]'
  ionScriplets.leadApiHuggiesScriplet -> wteStandaloneCampaign.wteStandaloneCampaignApi.wteLeadSweepsPublishToSns 'Calls API endpoint /client/[id]'
}