model {
  system wteWeb 'WTE Web' {
    style {
      shape browser
    }
    description 'Serves Website and product recommendations'
    container wteWebServer 'WTE Web Server' {
      description 'WTE Web Server, .NET'
      style {
        icon tech:net
      }
    }

    container community 'WTE Community' {
      component contentCreate 'Create Post/comment'
      component contentFlag 'Flag Post/comment'

    }
  }

  // Each model should include the relationships it *initiates*.
  wteWebServer -> fedGQL.gateway.skusByRetailerIds 'Gets price data for products'
  wteWeb -> registry.registryService 'Interacts with registry builder'

  contentCreate -> wteCommunityApi.wteCommunityLambda
  contentFlag -> wteAdmin.violationQueue

  // Registry Product Guides
  wteWebServer -> fedGQL.roundupProducts 'Reads roundup products'
}