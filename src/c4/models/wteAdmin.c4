model {
  wteAdmin = system 'WTE Internal Community Admin Tool' {
    style {
      shape browser
    }
    description 'Manages community user and violation queue'
    container wteAdminUI 'WTE Community Admin Tool' {
      description 'WTE Web Server, .NET'
      link https://admin.whattoexpect.com/
      style {
        icon tech:net
      }
      component userBan 'Ban user'
      component userBanFromGroup 'Ban user from a group'
      component markContentAsSpam 'Mark Content As Spam'
      component markContentAsHam 'Mark Content As Ham'


    }

    container violationQueue 'Violation Queue' {
      component contentReleased
      component contentDeleted
    }
  }
  
  // Each model should include the relationships it *initiates*.
  markContentAsSpam -> akismet
  markContentAsHam -> akismet
}