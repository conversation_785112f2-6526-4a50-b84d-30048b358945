model {
  system thinkific 'Thinkific' {
    description 'Thinkific system'

    container thinkificSite 'Thinkific Website' {
      description 'Thinkific hosted pages'
    }

    container thinkificApi 'Thinkific API' {
      description 'Thinkific backend API'
    }

    container thinkificApiGw 'API-GW for Thinkific' {
      description 'API-GW for Thinkific integrations'
      icon aws:api-gateway

      component coursesEnrollmentHandler 'Courses Enrollment Lambda' {
        description 'Lambda handler for Thinkific courses enrollment'
        icon aws:lambda
        link https://github.com/babycenter/ppde-airflow/tree/master/aws_lambdas/functions/bc-courses-enrollment-handler
      }

      component welcomeEmailHandler 'Courses Email Handler Lambda' {
        description 'Lambda handler for Courses Welcome Emails'
        icon aws:lambda
        link https://github.com/babycenter/ppde-airflow/tree/master/aws_lambdas/functions/bc-course-welcome-email-handler
      }
    }

    container coursesSqs 'Courses SQS' {
      #ghost
      description 'Courses Event Queue'
      icon aws:simple-queue-service
    }

  }

  thinkificSite -> coursesEnrollmentHandler 'Invokes through webhook'
  coursesEnrollmentHandler -> thinkificApi 'Gets product info'
  coursesEnrollmentHandler -> sailthru.sailthruApi.sailthruUserMethod 'Posts user enrollment/purchase'
  coursesEnrollmentHandler -> coursesSqs 'Sends course event'
  welcomeEmailHandler -> coursesSqs 'Polls for course event'
  welcomeEmailHandler -> sailthru.sailthruApi.sailthruSendHandler 'Sends courses welcome emails'
}