model {
  system prediktor 'Prediktor ECS' {
    style {
      icon aws:elastic-container-service
    }
    description 'Prediktor Elastic Container Service'
    container spam_en_v1 {
      description 'Spam - English (v1)'
      style {
        icon aws:elastic-container-service
      }
      link https://everydayhealth.atlassian.net/wiki/spaces/PP/pages/**********/Spam+Services
    }
    container spam_pt_v2 {
      description 'Spam - Portuguese (v2)'
      style {
        icon aws:elastic-container-service
      }
      link https://everydayhealth.atlassian.net/wiki/spaces/PP/pages/**********/Spam+Services
    }
    container spam_de_v1 {
      description 'Spam - German (v1)'
      style {
        icon aws:elastic-container-service
      }
      link https://everydayhealth.atlassian.net/wiki/spaces/PP/pages/**********/Spam+Services
    }
    container spam_es_v1 {
      description 'Spam - Español (v1)'
      style {
        icon aws:elastic-container-service
      }
      link https://everydayhealth.atlassian.net/wiki/spaces/PP/pages/**********/Spam+Services
    }
    container abuse_classifier_en_v1 {
      description 'Abuse Classifier - English (v1)'
      style {
        icon aws:elastic-container-service
      }
    }
    container babynamer {
      description 'babynamer service for babynames app'
      style {
        icon aws:elastic-container-service
      }
    }
    container babynamer_bcus {
      description 'babynamer service for babynames swiping game (BC-US)'
      style {
        icon aws:elastic-container-service
      }
    }
    container babynamer_bcuk {
      description 'babynamer service for babynames swiping game (BC-UK)'
      style {
        icon aws:elastic-container-service
      }
    }
    container related_posts_uk_v1 {
      description 'community related posts for BC-UK'
      style {
        icon aws:elastic-container-service
      }
    }
    container related_posts_us_v1 {
      description 'community related posts for BC-US'
      style {
        icon aws:elastic-container-service
      }
    }
    container au_community_expert_content_v1 {
      description 'community export content recommender for BC-AU'
      style {
        icon aws:elastic-container-service
      }
    }
    container br_community_expert_content_v1 {
      description 'community export content recommender for BC-BR'
      style {
        icon aws:elastic-container-service
      }
    }
    container ca_community_expert_content_v1 {
      description 'community export content recommender for BC-CA'
      style {
        icon aws:elastic-container-service
      }
    }
    container de_community_expert_content_v1 {
      description 'community export content recommender for BC-DE'
      style {
        icon aws:elastic-container-service
      }
    }
    container es_community_expert_content_v1 {
      description 'community export content recommender for BC-ES'
      style {
        icon aws:elastic-container-service
      }
    }
    container uk_community_expert_content_v1 {
      description 'community export content recommender for BC-UK'
      style {
        icon aws:elastic-container-service
      }
    }
    container subtopic_content_order_v1 {
      description 'Subtopic Content Order'
      style {
        icon aws:elastic-container-service
      }
    }
    container page_rec_page_v1 {
      description 'Content Recommender for BC-US'
      style {
        icon aws:elastic-container-service
      }
    }
    container wte_page_rec_page_v1 {
      description 'Content Recommender for WTE'
      style {
        icon aws:elastic-container-service
      }
    }
    container pagerec_v2 {
      description 'Community Recommended Posts for BC-US'
      style {
        icon aws:elastic-container-service
      }
    }
    container uk_pagerec_v2 {
      description 'Community Recommended Posts for BC-UK'
      style {
        icon aws:elastic-container-service
      }
    }
    container us_cohorts_v3 {
      description 'Cohort Service for BC-US'
      style {
        icon aws:elastic-container-service
      }
    }
    container ca_stage_prediction_v1 {
      description 'Stage Prediction Service for BC-CA'
      style {
        icon aws:elastic-container-service
      }
    }
    container uk_stage_prediction_v1 {
      description 'Stage Prediction Service for BC-UK'
      style {
        icon aws:elastic-container-service
      }
    }
    container us_stage_prediction_v1 {
      description 'Stage Prediction Service for BC-US'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_au_content_recommender {
      description 'WTGN content recommender for BC-AU'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_br_content_recommender {
      description 'WTGN content recommender for BC-BR'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_ca_content_recommender {
      description 'WTGN content recommender for BC-CA'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_de_content_recommender {
      description 'WTGN content recommender for BC-DE'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_es_content_recommender {
      description 'WTGN content recommender for BC-ES'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_uk_content_recommender {
      description 'WTGN content recommender for BC-UK'
      style {
        icon aws:elastic-container-service
      }
    }
    container bc_app_content_recommender_v1 {
      description 'Content recommender for BC Apps'
      style {
        icon aws:elastic-container-service
      }
    }
    container group_rec_v2 {
      description 'Group Recommender for BC-US'
      style {
        icon aws:elastic-container-service
      }
    }
  }
}