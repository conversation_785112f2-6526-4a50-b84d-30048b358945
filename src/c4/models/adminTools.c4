model {
  system adminTools 'Administrative Tools' {
    description 'Tools for managing the system'
    container dealsSheet 'Active Deals Spreadsheet' {
      description 'Google Sheet with active deals'
      style {
        icon tech:google
      }
    }

    container dealsTool 'Active Deals Admin Tool' {
      #proposed
      description 'Admin Frontend for Daily Deals'
      style {
        icon tech:react
      }
    }

    container productTool 'Product Catalog Admin Tool' {
      #proposed
      description 'Admin Frontend for Product Catalog'
      style {
        icon tech:react
      }
    }
  }

  dealsSheet -> fedGQL.intGateway.setActiveDeals 'Sets active Daily Deals'
  dealsTool -> fedGQL.intGateway.setActiveDeals 'Sets active Daily Deals'
  productTool -> fedGQL.intGateway 'reads/writes products'
}