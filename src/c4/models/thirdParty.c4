model {
  system thirdParty 'Third Party Vendors' {
    description 'Third party Vendor integrations'

    container sailthru {
      description 'Third party vendor for P&P Emails'
    }

    container localytics {
      description 'Third party vendor for BC Push Notifications'
    }

    container rewardsVendor {
      description 'Proposed Rewards/Loyalty Vendor'
      style {
        color amber
      }
    }
  }

  localytics -> endUser 'Sends push notifications'
  sailthru -> endUser 'Sends emails'
}