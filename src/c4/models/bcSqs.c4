model {
  system bcSqs 'BC SQS' {
    description 'BC SQS Queues'
    
    container bc-us-sailthru-member-data 'BC Sailthru Member Data SQS' {
      description 'bc-us-sailthru-member-data'
      link https://551062308343-bi77cvpm.us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F551062308343%2Fbc-us-sailthru-member-data
      style {
        icon aws:simple-queue-service
      }
    }
    
    container bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut 'BC Localytics SQS' {
      description 'bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut'
      link https://551062308343-bi77cvpm.us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F551062308343%2Fbc-localytics-BcLocalyticsQueue-pEGl7H6zpxut
      style {
        icon aws:simple-queue-service
      }
    }

    container bc-us-sailthru-transactional 'BC Sailthru Transactional SQS' {
      description 'bc-us-sailthru-transactional'
      link https://551062308343-bi77cvpm.us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F551062308343%2Fbc-us-sailthru-transactional
      style {
        icon aws:simple-queue-service
      }
    }

    container wte-registry-delete-baby-prod 'WTE Registry Delete Baby SQS' {
      description 'wte-registry-delete-baby-prod (BC Acct)'
      link https://551062308343-bi77cvpm.us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F551062308343%2Fwte-registry-delete-baby-prod
      style {
        icon aws:simple-queue-service
      }
    }
  }
}