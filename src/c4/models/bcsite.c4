model {
  system bcsite 'BcSite CMS/CS' {
    description 'CMS and Customer Service site'

    container bcsiteServer 'BcSite Tomcat' {
      description 'BcSite server'
      style {
        icon tech:tomcat
      }
    }

    container bcCmsSync 'BcSite CMS Syncer' {
      description 'syncs CMS data to BC GQL'
      style {
        icon aws:lambda
      }
    }
  }

  bcsiteServer -> bcAuth.bcAuthServer 'authenticates against'
  bcCmsSync -> bcsiteServer 'reads CMS changes'
  bcCmsSync -> bcWeb.bcGql 'writes CMS changes'
}