model {
  system bcBookmarkNotification 'BC Bookmark & Notification Service' {
    description 'Consolidated BabyCenter Bookmark and Notification Service - single TypeScript/Node.js service handling both domains'

    container bookmarkNotificationApi 'Bookmark & Notification API Server' {
      description 'Unified TypeScript/Node.js API server handling both bookmark and notification operations'
      technology 'Node.js 22, TypeScript, Express.js'
      style {
        icon aws:ec2
      }
    }

    container bookmarkDb 'Bookmark Database' {
      description 'MySQL database storing bookmark data'
      technology 'MySQL 8.0'
      style {
        icon aws:rds
      }
    }

    container notificationDb 'Notification Database' {
      description 'DynamoDB storing notification data'
      technology 'AWS DynamoDB'
      style {
        icon aws:dynamo-db
      }
    }

    container subscriptionDb 'Subscription Database' {
      description 'DynamoDB storing subscription data'
      technology 'AWS DynamoDB'
      style {
        icon aws:dynamo-db
      }
    }

    container communityEventsQueue 'Community Events Queue' {
      description 'SQS queue for community notification events'
      technology 'AWS SQS'
      style {
        icon aws:simple-queue-service
      }
    }

    container subscriptionEventsQueue 'Subscription Events Queue' {
      description 'SQS queue for subscription events'
      technology 'AWS SQS'
      style {
        icon aws:simple-queue-service
      }
    }

    container communityEventsProcessor 'Community Events Processor' {
      description 'Lambda function processing community events'
      technology 'AWS Lambda, TypeScript'
      style {
        icon aws:lambda
      }
    }

    container subscriptionEventsProcessor 'Subscription Events Processor' {
      description 'Lambda function processing subscription events'
      technology 'AWS Lambda, TypeScript'
      style {
        icon aws:lambda
      }
    }

    container bookmarkEvents 'Bookmark Events SNS' {
      description 'SNS topic for bookmark events'
      technology 'AWS SNS'
      style {
        icon aws:simple-notification-service
      }
    }

    container profileEventService 'Profile Event Service' {
      description 'Service layer for publishing bookmark and subscription events'
      technology 'TypeScript Service Layer'
      style {
        icon aws:lambda
      }
    }
  }

  // API relationships to databases
  bookmarkNotificationApi -> bookmarkDb 'reads/writes bookmarks' 'MySQL Protocol'
  bookmarkNotificationApi -> notificationDb 'reads/writes notifications' 'DynamoDB API'
  bookmarkNotificationApi -> subscriptionDb 'reads subscription data' 'DynamoDB API'

  // Event processing relationships
  communityEventsQueue -> communityEventsProcessor 'triggers processing' 'SQS Event'
  subscriptionEventsQueue -> subscriptionEventsProcessor 'triggers processing' 'SQS Event'
  
  communityEventsProcessor -> notificationDb 'creates notifications' 'DynamoDB API'
  communityEventsProcessor -> subscriptionDb 'queries subscribers' 'DynamoDB API'
  subscriptionEventsProcessor -> subscriptionDb 'manages subscriptions' 'DynamoDB API'

  // Event publishing
  bookmarkNotificationApi -> profileEventService 'publishes events' 'Internal API'
  profileEventService -> bookmarkEvents 'publishes bookmark events' 'SNS'
  profileEventService -> subscriptionEventsQueue 'publishes subscription events' 'SQS Message'

  // External integrations
  bookmarkNotificationApi -> bcAuth 'validates JWT tokens' 'HTTPS'
  wteCommunityApi -> communityEventsQueue 'publishes community events' 'SQS Message'
}
