model {
  push = system 'Push' {
    description 'Allows creation of WTE Web pages'

    component cms 'CMS' {
      description 'Allows Editors to edit pages'
    }
    component contentAPI 'Content API' {
      description 'Exposes page/product data for website'
    }
    component cmsDB 'CMS DB' {
      description 'RDS (?) db for Push CMS content'
      technology 'MSSQL'
    }
  }

  // Each model should include the relationships it *initiates*.
  cms -> contentAPI 'publishes pages'
  contentAPI -> cmsDB 'stores/retrieves data'
}