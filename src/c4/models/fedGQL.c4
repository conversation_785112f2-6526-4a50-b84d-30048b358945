model {
  fedGQL = system 'Federated GraphQL' {
    description 'Federated GraphQL System with several subgraphs'
    container gateway 'Federated Gateway' {
      description 'Federated Gateway for public resources'
      style {
        icon tech:graphql
      }

      component activeDeals 'activeDeals Query' {
        description 'Gets Active Daily Deals'
      }

      component skusByRetailerIds 'skusByRetailerIds Query' {
        description 'Gets sku information for a retailer'
      }

      component activeAFC 'AFC Content Query' {
        description 'Gets published AFC Content'
        style {
          color amber
        }
      }

      component roundupProducts 'roundupProducts Query' {
        description 'Gets roundup products by subcategory'
      }
    }

    container intGateway 'Internal Federated Gateway' {
      description 'Federated Gateway for internal resources'
      style {
        icon tech:graphql
      }

      component setActiveDeals 'setActiveDeals Mutation' {
        description 'Sets Active Deals'
      }

      component publishAFC 'publishAFC Mutation' {
        description 'Publishes App First Content'
        style {
          color amber
        }
      }

      component insertBatchSkus 'insertBatchSkus Mutation' {
        description 'Inserts products from Content API'
        style {
          icon tech:graphql
        }
      }

      component upsertRoundupProducts 'upsertRoundupProducts Mutation' {
        description 'Upserts Roundup Products from Content API'
        style {
          icon tech:graphql
        }
      }

      component updatePrice 'updatePrice Mutation' {
        description 'Updates prices for integrated retailers'
        style {
          icon tech:graphql
        }
      }
    }

    container productCatalog 'Product Catalog' {
      description 'Product Catalog Subgraph'
      style {
        icon tech:graphql
      }
    }

    container intProductCatalog 'Internal Product Catalog' {
      description 'Internal Product Catalog Subgraph'
      style {
        icon tech:graphql
      }
    }

    container dailyDeals 'Daily Deals' {
      description 'Daily Deals Subgraph'
      style {
        icon tech:graphql
      }
    }

    container intDailyDeals 'Internal Daily Deals' {
      description 'Internal Daily Deals Subgraph'
      style {
        icon tech:graphql
      }
    }

    container afcSubgraph 'AFC Subgraph' {
      description 'App First Content Subgraph'
      style {
        icon tech:graphql
        color amber
      }
    }

    container intAfcSubgraph 'Internal AFC Subgraph' {
      description 'Internal App First Content Subgraph'
      style {
        icon tech:graphql
        color amber
      }
    }

    container afcServiceDB 'AFC Service DB' {
      description 'AFC Service DB. RDS? DDB?'
      style {
        color amber
      }
    }

    container dailyDealsDB 'Daily Deals DB' {
      description 'Daily Deals RDS Mysql Database'
      style {
        icon aws:rds
      }
    }

    container productCatalogDB 'Product Catalog DB' {
      description 'Product Catalog RDS Mysql Database'
      style {
        icon aws:rds
      }
    }
  }

  // Each model should include the relationships it *initiates*.
  intGateway -> intDailyDeals 'Forwards Daily Deals requests'
  intGateway -> intProductCatalog 'Forwards internal PC requests'
  // gateway -> productCatalog 'Forwards PC requests'
  // gateway -> dailyDeals 'Forwards Daily Deals requests'
  skusByRetailerIds -> productCatalog 'Gets sku information'
  activeDeals -> dailyDeals 'Forwards activeDeals query'
  setActiveDeals -> intDailyDeals 'Forwards setActiveDeals mutation'
  dailyDeals -> dailyDealsDB 'reads/writes Deals data'
  intDailyDeals -> dailyDealsDB 'reads/writes Deals data'
  productCatalog -> productCatalogDB 'reads/writes product data'
  intProductCatalog -> productCatalogDB 'reads/writes product data'

  activeAFC -> afcSubgraph 'Forwards to subgraph'
  afcSubgraph -> afcServiceDB 'Reads data'
  publishAFC -> intAfcSubgraph 'Forwards to subgraph'
  intAfcSubgraph -> afcServiceDB 'Writes data'

  // product guides
  gateway.roundupProducts -> productCatalog 'Forwards roundupProducts query'
  intGateway.upsertRoundupProducts -> productCatalog 'Forwards upsertRoundupProducts mutation'
  intGateway.updatePrice -> productCatalog 'Forwards updatePrice mutation'
}