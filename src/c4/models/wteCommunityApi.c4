model {
  system wteCommunityApi 'WTE Community API' {

    description 'Community Service for Website and Apps'

    container communityApi 'Community API' {
      description 'Main community API handling posts, comments, and reactions'
      technology 'Node.js, REST API'
      style {
        icon aws:ec2
      }
    }

    container wteCommunityLambda 'WTE Community Lambdas' {
      component contentCheck 'Checks Post/comment'
    }
    

  }

  // Each model should include the relationships it *initiates*.
  contentCheck -> akismet
  contentCheck -> perspective
  contentCheck -> wteAdmin.violationQueue
  
  // Community events publishing
  communityApi -> bcBookmarkNotification.communityEventsQueue 'publishes community events' 'SQS Message'
}