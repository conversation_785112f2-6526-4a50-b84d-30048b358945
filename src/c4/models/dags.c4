model {
  system dags 'Airflow DAGs' {
    style {
      icon tech:airflow
    }
    description 'Airflow DAGs for scheduled processes'
    container dagCMSReplicator {
      description 'Replicates from CMS to Redshift'
      style {
        icon tech:airflow
      }
    }
    container dagProductProcessor 'Product Loading DAG'{
      description 'Processes product data'
      style {
        icon tech:airflow
      }
    }
    container dagRegistryProductClassifier 'Registry Product Categorizing DAG'{
      description 'Classifies products for registry'
      style {
        color amber
        icon tech:airflow
      }
    }
    container dagScribbleLido 'Scribble Lido DAG' {
      description 'Processes CSV leads and ingests them into Redshift'
      style {
        icon tech:airflow
      }
    }
    container bc-community-events-delete-ppde 'Community Content Delete SQS' {
      description 'bc-community-events-delete-ppde'
      style {
        icon aws:simple-queue-service
        shape queue
      }
    }

    container bc_community_delete_sqs 'BC Community Delete DAG' {
      description 'bc_community_delete_sqs'
      style {
        icon tech:airflow
      }
    }

    container upsert_roundup_products 'upsertRoundupProducts DAG' {
      description 'upsert_roundup_products'
      style {
        icon tech:airflow
      }
    }

    container upsert_roundup_page_products 'Roundup Product page-loading DAG' {
      description 'upsert_roundup_page_products'
      style {
        icon tech:airflow
      }
    }
  }

  // Each model should include the relationships it *initiates*.
  dags -> deToolchain.redshift 'Write to and read from redshift'
  // dags -> fedGQL.intGateway 'Submits product data'
  dags -> retailers 'Get product information and prices'
  dags -> registry 'Retrieves product info'
  dags -> registry.registryRDS 'Retrieves popular products'
  dags -> push 'Gets product data'
  dags -> fedGQL 'Writes product data'

  dagCMSReplicator -> push.cmsDB 'Check for new records'
  dagCMSReplicator -> deToolchain.redshift 'Writes replicated records'

  dagProductProcessor -> deToolchain.redshift 'Reads products'
  dagProductProcessor -> push.contentAPI 'Reads product data'
  dagProductProcessor -> deToolchain.classifier 'Retrieves product categories'
  dagProductProcessor -> fedGQL.intGateway 'Submits categorized products'
  dagProductProcessor -> fedGQL.intGateway.insertBatchSkus 'Inserts products'

  dagRegistryProductClassifier -> registry.registryRDS 'Reads registry data'
  dagRegistryProductClassifier -> deToolchain.classifier 'Retrieves product categories'
  dagRegistryProductClassifier -> fedGQL.intGateway 'Submits categorized products'

  dagScribbleLido -> edhFtp.ftpServer 'Reads from the FTP upload server'
  dagScribbleLido -> deToolchain.redshift 'Batch upload FTP leads to redshift'

  bc_community_delete_sqs -> bc-community-events-delete-ppde 'Polls SQS'

  // Registry Product Guides
  upsert_roundup_page_products -> push.contentAPI 'Gets Roundup Products'
  upsert_roundup_page_products -> deToolchain.redshift 'Writes roundup products to db'
  upsert_roundup_products -> deToolchain.redshift 'Reads roundup products from db'
  upsert_roundup_products -> fedGQL.upsertRoundupProducts 'Sends product data to product catalog'
}