model {
  system wteLeadSweeps 'WTE Lead Sweeps' {
    description 'WTE Lead Sweeps lambdas, triggered by SQS'

    container wteLeadSweepsSubmitClientCervidil 'wte-lead-sweeps-submit-client-cervidil' {
      description 'WTE Lead Sweeps Cervidil Lambda'
      style {
        icon aws:lambda
      }
    }

    container wteLeadSweepsSubmitClientAeroflow 'wte-lead-sweeps-submit-client-aeroflow' {
      description 'WTE Lead Sweeps Aeroflow Lambda'
      style {
        icon aws:lambda
      }
    }

    container wteLeadSweepsSubmitClientHuggiesUsa 'wte-lead-sweeps-submit-client-huggiesusa' {
      description 'WTE Lead Sweeps Cervidil Lambda'
      style {
        icon aws:lambda
      }
    }

    container wteLeadSweepsAeroflowSqs 'wte-lead-sweeps-aeroflow' {
      description 'WTE Lead Sweeps Cervidil Queue'
      style {
        icon aws:simple-queue-service
        shape queue
      }
    }

    container wteLeadSweepsCervidilSqs 'wte-lead-sweeps-cervidil' {
      description 'WTE Lead Sweeps Cervidil Queue'
      style {
        icon aws:simple-queue-service
        shape queue
      }
    }
    
    container wteLeadSweepsHuggiesSqs 'wte-lead-sweeps-huggies' {
      description 'WTE Lead Sweeps Huggies Queue'
      style {
        icon aws:simple-queue-service
        shape queue
      }
    }

    container wteLeadLogMysqlDb 'Lead-Log-MySql DB' {
      description 'WTE Lead Log MySQL DB'
      style {
        icon aws:rds
        shape cylinder
      }
    }
  }

  // SQS is triggered by SNS
  wteSns.wteLeadSweeps -> wteLeadSweeps.wteLeadSweepsAeroflowSqs 'Publishes to SQS'
  wteSns.wteLeadSweeps -> wteLeadSweeps.wteLeadSweepsCervidilSqs 'Publishes to SQS'
  wteSns.wteLeadSweeps -> wteLeadSweeps.wteLeadSweepsHuggiesSqs 'Publishes to SQS'

  // Lambdas are triggered by polling SQS 
  wteLeadSweeps.wteLeadSweepsSubmitClientAeroflow -> wteLeadSweeps.wteLeadSweepsAeroflowSqs 'Polls SQS'
  wteLeadSweeps.wteLeadSweepsSubmitClientCervidil -> wteLeadSweeps.wteLeadSweepsCervidilSqs 'Polls SQS'
  wteLeadSweeps.wteLeadSweepsSubmitClientHuggiesUsa -> wteLeadSweeps.wteLeadSweepsHuggiesSqs 'Polls SQS'

  // Lambdas read/write to the MySQL DB
  wteLeadSweeps.wteLeadSweepsSubmitClientAeroflow -> wteLeadSweeps.wteLeadLogMysqlDb 'Read/writes to MySQL'
  wteLeadSweeps.wteLeadSweepsSubmitClientCervidil -> wteLeadSweeps.wteLeadLogMysqlDb 'Read/writes to MySQL'
  wteLeadSweeps.wteLeadSweepsSubmitClientHuggiesUsa -> wteLeadSweeps.wteLeadLogMysqlDb 'Read/writes to MySQL'

  // Lambdas push to external APIs 
  wteLeadSweeps.wteLeadSweepsSubmitClientAeroflow -> thirdPartyLeadApis.aeroflowLeadsApi 'Pushes to external API'
  wteLeadSweeps.wteLeadSweepsSubmitClientCervidil -> thirdPartyLeadApis.cervidilLeadsApi 'Pushes to external API'
  wteLeadSweeps.wteLeadSweepsSubmitClientHuggiesUsa -> thirdPartyLeadApis.huggiesLeadsApi 'Pushes to external API'
}