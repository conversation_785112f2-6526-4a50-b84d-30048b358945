model {
  system snowplow 'Snowplow Data Collection' {
    container collector 'Snowplow Collector' {
      description 'Self hosted Snowplow Collector'
    }
    container rawKinesis 'Snowplow Raw Kinesis Stream' {
      description 'Snowplow Raw Kinesis Stream'
    }
    container enrichmentContainer 'Snowplow Enrichment Container' {
      description 'Snowplow Enrichment Container'
    }
    container enrichedKinesis 'Snowplow Enriched Kinesis Stream' {
      description 'Snowplow Enriched Kinesis Stream'
    }
    container lambda 'Snowplow Lambda' {
      description 'Snowplow Lambda'
    }
    container s3 'Snowplow S3 Bucket' {
      description 'Snowplow S3 Bucket'
    }
    container DAG 'Snowplow DAG' {
      description 'Snowplow DAG'
    }
  }

  collector -> rawKinesis 'Publishes raw event to stream'
  rawKinesis -> enrichmentContainer 'Triggers enrichment process'
  enrichmentContainer -> enrichedKinesis 'Publishes enriched event to steram'
  enrichedKinesis -> lambda 'Triggers lambda'
  lambda -> s3 'Writes delimited files to lambda'
  DAG -> s3 'Reads delimited files'
  DAG -> deToolchain.redshift 'Writes snowplow data to Redshift'

  // proposed
  lambda -> thirdParty.rewardsVendor 'Filters and sends behavioral data'

}
