model {
  system bcSns 'BC SNS' {
    description 'BC SNS Topics'
    style {
      icon aws:simple-notification-service
    }

    
    container bcUsMemberEvents 'bc-us-member-events' {
      description 'BabyCenter US Member Events SNS Topic (ue1)'
      style {
        icon aws:simple-notification-service
      }

      component emailConfirmed 'EmailConfirmed' {
        description 'EmailConfirmed msg'
      }
      component newMember 'NewMember' {
        description 'NewMember msg'
      }
      component passwordReset 'PasswordReset' {
        description 'PasswordReset msg'
      }
      component communityComment 'CommunityComment' {
        description 'CommunityComment msg'
      }
      component directMessage 'DirectMessage' {
        description 'DirectMessage msg'
      }
      component passANote 'PassANote' {
        description 'PassANote msg'
      }
      component groupInvite 'GroupInvite' {
        description 'GroupInvite msg'
      }
      component newQuestion 'NewQuestion' {
        description 'NewQuestion msg'
      }
      component newAnswer 'NewAnswer' {
        description 'NewAnswer msg'
      }
      component oneOffEmail 'OneOffEmail' {
        description 'OneOffEmail msg'
      }
      component memberChange 'MemberChange' {
        description 'MemberChange msg'
      }
      component emailChange 'EmailChange' {
        description 'EmailChange msg'
      }
      component memberLogin 'MemberLogin' {
        description 'MemberLogin msg'
      }
      component memberDelete 'MemberDelete' {
        description 'MemberDelete msg'
      }
      component newScreenName 'NewScreenName' {
        description 'NewScreenName msg'
      }
      component subscriptionChange 'SubscriptionChange' {
        description 'SubscriptionChange msg'
      }
      component newBaby 'NewBaby' {
        description 'NewBaby msg'
      }
      component babyChange 'BabyChange' {
        description 'BabyChange msg'
      }
      component babyDelete 'BabyDelete' {
        description 'BabyDelete msg'
      }
      component postSubscription 'PostSubscription' {
        description 'PostSubscription msg'
      }
      component groupMembership 'GroupMembership' {
        description 'GroupMembership msg'
      }
    }

    // TODO document intl flow as well, if needed
    container bcIntlMemberEvents 'BC Intl Member Events' {
      description 'BabyCenter Intl Member Events SNS Topic (ue1)'
      style {
        icon aws:simple-notification-service
      }
    }
  }

  bcUsMemberEvents -> serviceLambdas.bc-member-events-monitor 'triggers lambda'
  bcIntlMemberEvents -> serviceLambdas.bc-member-events-monitor 'triggers lambda'
  emailConfirmed -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  newMember -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  passwordReset -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  communityComment -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  directMessage -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  passANote -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  groupInvite -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  newQuestion -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  oneOffEmail -> serviceLambdas.sailthru-transactional-email 'triggers lambda'
  memberChange -> serviceLambdas.bc-update-thinkific-ts 'triggers lambda'
  emailChange -> serviceLambdas.bc-update-thinkific-ts 'triggers lambda'

  memberLogin -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  newMember -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  memberChange -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  memberDelete -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  emailChange -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  newScreenName -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  subscriptionChange -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  newBaby -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  babyChange -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  babyDelete -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  postSubscription -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  groupMembership -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'
  emailConfirmed -> bcSqs.bc-us-sailthru-member-data 'publishes to queue'

  bcUsMemberEvents -> bcSqs.bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut 'publishes to queue'

  passwordReset -> bcSqs.bc-us-sailthru-transactional 'publishes to queue'

  babyDelete -> bcSqs.wte-registry-delete-baby-prod 'publishes to queue'

  bcUsMemberEvents -> serviceLambdas.bc-loyaltyRewards-lambda 'triggers lambda'

}