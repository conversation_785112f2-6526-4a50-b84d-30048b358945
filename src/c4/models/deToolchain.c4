model {
  deToolchain = system 'DE Toolchain' {
    description 'Processes product data'

    container redshift 'Redshift' {
      style {
        icon aws:redshift
      }
      description 'Data Warehouse'
    }

    container classifier 'LLM Categorization' {
      description 'Assigns categories to products based on their descriptions'
      style {
        color amber
        icon tech:fast-api
      }
    }

    container afcCMS 'AFC CMS' {
      description 'CMS frontend for App First Content'
      style {
        color amber
      }
    }

    container registry-update-from-retailer 'Update Registry Lambda' {
      description 'registry-update-from-retailer'
      style {
        icon aws:lambda
      }
    }

    container targetRegistryAPI 'Target Registry API' {
      description 'Target Real-time Registry API'
    }

    container registryUpdateSQS 'Registry Update SQS' {
      description 'registry-marketplace-product-update'
    }

    container registryProductUpdates 'Registry Product Updates Lambda' {
      description 'registry-retailer-product-updates-handler'
      style {
        icon aws:lambda
      }
    }

    container genai-community-insights-post-tagging-v2 'GenAI Community Insights Post Tagging' {
      description 'genai-community-insights-post-tagging-v2'
      style {
        icon aws:lambda
      }
    }
  }

  afcCMS -> fedGQL.publishAFC 'publishes active AFC'
  registry-update-from-retailer -> registry.registryRDS 'reads and updates several tables'
  registry-update-from-retailer -> registry.registryRedis 'extends user data, adds registry to set'
  registry-update-from-retailer -> targetRegistryAPI 'gets registry data from Target'
  registry-update-from-retailer -> registry.registryRetailerIntegrationEventsSns 'publishes RetailerProductInfoUpdated message'
  registry-update-from-retailer -> registryUpdateSQS 'sends to queue'
  registryProductUpdates -> registryUpdateSQS 'reads from queue'
  registryProductUpdates -> registry.registryRDS 'updates retailer_product'
  registryProductUpdates -> registry.registryRetailerIntegrationEventsSns 'publishes RetailerProductInfoUpdated message'
}
