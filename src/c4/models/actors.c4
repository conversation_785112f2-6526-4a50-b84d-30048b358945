model {
  editor = actor 'Editor' {
    description 'Creates and manages content on sites'
    style {
      multiple true
    }
  }
  endUser = actor 'End User' {
    description 'Browses and interacts on sites'
    style {
      multiple true
    }
  }
  marketer = actor 'Marketer' {
    description 'Constructs marketing campaigns'
  }
  moderator = actor 'Moderator' {
    description 'Monitors community activities, removes bad content and ban misbehaving users'
    style {
      multiple true
    }
  }

  unknown = actor 'Unknown' {
    description 'Please fill in after further research'
    style {
      color red
    }
  }

  // Each model should include the relationships it *initiates*.
  marketer -> ion.ionAuthoringTool 'Authors pages'
  marketer -> thirdParty.localytics 'Constructs BC push campaigns'
  marketer -> thirdParty.sailthru 'Constructs email campaigns'

  marketer -> thirdParty.rewardsVendor 'Defines rewards campaigns'

  
  editor -> push.cms 'Authors pages'
  editor -> adminTools.dealsSheet 'Defines Daily Deals'
  editor -> adminTools.dealsTool 'Defines Daily Deals'
  editor -> adminTools.productTool 'Administers products'
  editor -> bcsite.bcsiteServer 'Authors pages'

  endUser -> bcWeb.bcReactApp 'Visits babycenter site'
  endUser -> wteWeb.wteWebServer 'Views whattoexpect.com'
  endUser -> mobile.wteAndroid 'Uses WTE Android App'
  endUser -> mobile.wteiOS 'Uses WTE iOS App'
  endUser -> mobile.bcAndroid 'Uses BC Android App'
  endUser -> mobile.bciOS 'Uses BC iOS App'
  endUser -> ion.ionHostedSite 'Submits forms'

  // WTE community actions
  endUser -> wteWeb.contentCreate 'Creates a WTE post/discussion or comment/reply'
  endUser -> wteWeb.contentFlag 'Flags post/comment for violation'
  moderator -> wteAdmin.wteAdminUI.userBan 'Bans user from WTE community'
  moderator -> wteAdmin.wteAdminUI.userBanFromGroup 'Bans user from a WTE group'
  moderator -> wteAdmin.violationQueue.contentReleased  'Marks post/comment safe and releases from violation queue'
  moderator -> wteAdmin.violationQueue.contentDeleted 'Deletes post/comment and removes from violation queue'
  moderator -> wteAdmin.wteAdminUI.markContentAsSpam
  moderator -> wteAdmin.wteAdminUI.markContentAsHam

  // BC community actions
  endUser -> bcWeb.bcGql.contentCreate 'Creates a BC Post/Comment'
  endUser -> bcWeb.bcGql.contentAbuseReport 'Flags a BC Post/Comment'
  endUser -> bcWeb.bcGql.userAbuseReport 'Flags a BC User Profile'
  moderator -> bcWeb.bcGql.contentDelete 'Deletes a BC Post/Comment'
  moderator -> bcWeb.bcGql.userBan 'Bans a BC User'
  moderator -> bcWeb.bcReactApp 'Monitors community activities'

  unknown -> bcSqs.wte-registry-delete-baby-prod 'Nothing seems to consume this?'
}
