model {
  system bcWeb 'BC Web' {
    description 'BabyCenter Web'

    container bcReactApp 'BC React App' {
      description 'BabyCenter React App, browser side'
      style {
        shape browser
        icon tech:react
      }

      component communityAdminTools 'BC Community Admin Tools' {
        description 'BabyCenter Admin Pages'
        style {
          shape browser
        }
      }
    }

    container bcReactServer 'BC React Server' {
      description 'BabyCenter React Server'
      style {
        icon tech:nextjs
      }
    }

    container bcGql 'BC GraphQL Server' {
      description 'BabyCenter GraphQL Server'
      style {
        icon tech:graphql
      }
      component moderationQueue 'BC Community Moderation Queue'
      component contentCreate 'Creates Post/Comment'
      component contentAbuseReport 'Flags Post/Comment'
      component userAbuseReport 'Flags User Profile'
      component contentDelete 'Deletes Post/Comment'
      component userBan 'Bans User'
      component postCommentService 'Validate post/comment content' {
        description: 'Blocks content creation based on the siteconfig(postCommentDenyRegexPattern) RegEx rules'
      }
    }

    container bcGqlCommunityDb 'Community DB' {
      description 'Community MySQL Database'
      style {
        icon tech:mysql
        shape cylinder
      }
    }

    container bcGqlExpertDb 'Expert DB' {
      description 'Expert CMS MySQL Database'
      style {
        icon tech:mysql
        shape cylinder
      }
    }

    container bcGqlRedis 'Redis' {
      description 'Redis cache and store'
      style {
        icon tech:redis
        shape cylinder
      }
    }

    container bcImageService 'BC Image Service' {
      description 'BabyCenter Image Service'
      link https://github.com/babycenter/bc-image-service 'GitHub'
      style {
        icon aws:api-gateway
      }
    }

    container bcCommunityEvents 'BC Community Events' {
      description 'BabyCenter Community Events SNS Topic (uw2)'
      style {
        icon aws:simple-notification-service
      }
      component postCreated 'PostCreated' {
        description 'PostCreated msg'
      }
      component postEdited 'PostEdited' {
        description 'PostEdited msg'
      }
      component commentCreated 'CommentCreated' {
        description 'CommentCreated msg'
      }
      component commentEdited 'CommentEdited' {
        description 'CommentEdited msg'
      }
      component contentDeleted 'ContentDeleted' {
        description 'ContentDeleted msg'
      }
    }

    container bcAkamaiPurgeTopic 'BC Akamai Purge Topic' {
      description 'BabyCenter bc-akamai-BcAkamaiPurgeTopic-BDWGHOQTMZ4L SNS Topic (uw2)'
      style {
        icon aws:simple-notification-service
      }
    }

  }

  // Each model should include the relationships it *initiates*.
  bcReactApp -> registry.registryService 'Interacts with registry builder'
  bcReactApp -> bcReactServer 'Requests SSR pages'
  bcReactApp -> bcImageService 'Uploads images'
  bcReactApp -> bcGql 'Reads/writes data'
  bcReactApp -> bcNotifications.notificationService 'Gets/reads notifications'
  bcReactServer -> bcGql 'Requests data from server-side'
  bcGql -> bcAuth.bcAuthServer 'Authenticates users'

  // Registry integrations
  bcReactApp -> registry.wte-registry-get-registry 'getRegistryInfo'
  bcReactApp -> registry.wte-registry-get-public-registries 'getPublicRegistryList'
  bcReactApp -> registry.wte-registry-get-registry-shared 'getSharedRegistryInfo'
  bcReactApp -> registry.wte-registry-create-profile 'sendResponses'
  bcReactApp -> registry.wte-registry-create-profile-anonymous 'sendResponsesAnonymous'
  bcReactApp -> registry.wte-registry-update-profile 'updateResponses'
  bcReactApp -> registry.wte-registry-add-checklist-subcategory 'addSubcategory'
  bcReactApp -> registry.wte-registry-delete-checklist-subcategory 'removeSubcategory'
  bcReactApp -> registry.wte-registry-set-checklist-subcategory 'checkedDataToRegistryService'
  bcReactApp -> registry.wte-registry-update-registry 'updateRegistry'
  bcReactApp -> registry.wte-registry-get-retailers 'getLinkedRetailers'
  bcReactApp -> registry.wte-registry-retailer-link 'addLinkedRegistry'
  bcReactApp -> registry.wte-registry-retailer-unlink 'removeLinkedRegistry'
  bcReactApp -> registry.wte-registry-get-registry-default 'getAnonymousProfileWithDefaultChecklist'
  bcReactApp -> registry.wte-registry-create-profile-empty 'createEmptyProfile'
  bcReactApp -> registry.wte-registry-add-checklist-subcategory-all 'populateDefaultChecklist'
  bcReactApp -> registry.wte-registry-create-profile-default 'populateDefaultChecklist'
  bcReactApp -> registry.wte-registry-update-user-product-content 'updateProductUserContent'

  // SNS integrations
  bcGql -> bcCommunityEvents 'Publishes community events'
  bcCommunityEvents.postCreated -> deToolchain.genai-community-insights-post-tagging-v2 'triggers lambda'
  bcCommunityEvents.postEdited -> deToolchain.genai-community-insights-post-tagging-v2 'triggers lambda'
  bcCommunityEvents -> bcNotifications.bc-community-notification-svc 'publishes notification traffic'
  bcCommunityEvents.postCreated -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue 'publishes to SQS'
  bcCommunityEvents.postEdited -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue 'publishes to SQS'
  bcCommunityEvents.commentCreated -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue 'publishes to SQS'
  bcCommunityEvents.commentEdited -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue 'publishes to SQS'
  bcCommunityEvents.contentDeleted -> dags.bc-community-events-delete-ppde 'Publishes to SQS'

  bcGql -> bcSns.bcUsMemberEvents 'Publishes backend events'
  bcGql -> bcSns.bcIntlMemberEvents 'Publishes backend events'

  // GQL standalone campaign integrations
  bcGql -> wteStandaloneCampaign.wteStandaloneCampaignApi 'Posts API data'

  // GQL database ownership
  bcGql -> bcGqlCommunityDb 'Read/writes to database'
  bcGql -> bcGqlExpertDb 'Read/writes to database'
  bcGql -> bcGqlRedis 'Read/writes to database'

  // Community Moderation Process
  //contentCreate -> bcCommunityEvents
  contentAbuseReport -> moderationQueue
  userAbuseReport -> moderationQueue
  contentCreate -> postCommentService
  postCommentService -> bcCommunityEvents

  // Registry Product Guides
  bcReactServer -> fedGQL.roundupProducts 'Reads roundup products'

  bcReactApp -> snowplow.collector 'Publishes frontend events'

}