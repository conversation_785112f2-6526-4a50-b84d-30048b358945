model {
  system wteStandaloneCampaign 'WTE Standalone Campaign' {
    description 'WTE Standalone Campaign API'
    
    container wteStandaloneCampaignApi 'WTE Standalone Campaign API Gateway' {
      description 'API Gateway for WTE Standalone Campaign API'
      style {
        icon: aws:api-gateway
      }

      component wteLeadSweepsPublishToSns 'wte-lead-sweeps-publish-to-sns' {
        description '/client/[id] endpoint'
        style {
          icon aws:lambda
        }
      }

      component wteLuserAddV2 'wte-luser-add-v2' {
        description '/sweeps/signup endpoint'
        style {
          icon aws:lambda
        }
      }
    }
  }

  wteStandaloneCampaign.wteLeadSweepsPublishToSns -> wteSns.wteLeadSweeps 'Publishes to SNS'
  wteStandaloneCampaign.wteLuserAddV2 -> wteSns.wteUserEvents 'Publishes to SNS'
  wteStandaloneCampaign.wteLuserAddV2 -> wteUserApi.wteUserDDB 'Writes to DDB'
}