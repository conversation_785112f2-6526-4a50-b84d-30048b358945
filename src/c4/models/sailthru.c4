model {
  system sailthru 'Sailthru' {
    description 'Sailthru System'

    container sailthruApi 'Sailthru API' {
      description 'Sailthru API Endpoints'

      component sailthruSendHandler 'Sailthru Send Handler' {
        description 'Sailthru Handler for Sending Emails'
      }

      component sailthruUserMethod 'Sailthru User Endpoint' {
        description 'Sailthru User Method/Endpoint'
      }
    }
  }
}