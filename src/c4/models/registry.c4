model {
  registry = system 'Registry Service' {
    description 'Stores user registry data'

    container registryService 'Registry Service API-GW' {
      description 'NodeJS/Typescript REST API'

      component wte-registry-add-checklist-subcategory 'wte-registry-add-checklist-subcategory' {
        description 'POST /profile/checklist/subcategory/{id}'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-add-checklist-subcategory-all 'wte-registry-add-checklist-subcategory-all' {
        description 'POST /profile/checklist/subcategory/all'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-create-profile 'wte-registry-create-profile' {
        description 'POST /profile'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-create-profile-anonymous 'wte-registry-create-profile-anonymous' {
        description 'POST /profile/anonymous'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-create-profile-default 'wte-registry-create-profile-default' {
        description 'POST /profile/default'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-create-profile-empty 'wte-registry-create-profile-empty' {
        description 'POST /profile/empty'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-delete-checklist-subcategory 'wte-registry-delete-checklist-subcategory' {
        description 'DELETE /profile/checklist/subcategory/{id}'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-delete-registry-data 'wte-registry-delete-registry-data' {
        #ghost
        description 'wte-registry-delete-registry-data'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-public-registries 'wte-registry-get-public-registries' {
        description 'GET /public/registries'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-registry 'wte-registry-get-registry' {
        description 'GET /registryinfo'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-registry-default 'wte-registry-get-registry-default' {
        description 'GET /profile/anonymous'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-registry-personalized-url 'wte-registry-get-registry-personalized-url' {
        description 'GET /registryinfo/personalized-url'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-registry-shared 'wte-registry-get-registry-shared' {
        description 'GET /registryinfo/shared'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-registry-summary 'wte-registry-get-registry-summary' {
        description 'GET /registryinfo/summary'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-retailers 'wte-registry-get-retailers' {
        description 'GET /retailers'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-get-user-linked-registry-product 'wte-registry-get-user-linked-registry-product' {
        description 'GET /profile/products/{id}'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-purge-cache 'wte-registry-purge-cache' {
        description 'wte-registry-purge-cache'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-retailer-link 'wte-registry-retailer-link' {
        description 'PUT /retailers/{id}/link'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-retailer-link-short-url 'wte-registry-retailer-link-short-url' {
        #ghost
        description 'wte-registry-retailer-link-short-url'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-retailer-unlink 'wte-registry-retailer-unlink' {
        description 'PUT /retailers/unlink'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-send-by-email 'wte-registry-send-by-email' {
        description 'POST /registry/{id}/send-by-email'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-set-checklist-subcategory 'wte-registry-set-checklist-subcategory' {
        description 'PUT /profile/checklist/subcategory/{id}'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-stop-sharing-registry 'wte-registry-stop-sharing-registry' {
        #ghost
        description 'wte-registry-stop-sharing-registry'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-update-personalized-url 'wte-registry-update-personalized-url' {
        description 'PATCH /registry/{id}/personalized-url'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-update-profile 'wte-registry-update-profile' {
        description 'PUT /profile'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-update-registry 'wte-registry-update-registry' {
        description 'PUT /registry/{id}'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-update-registry-sort-order 'wte-registry-update-registry-sort-order' {
        description 'PUT /registry/{id}/sort-order'
        style {
          icon aws:lambda
        }
      }

      component wte-registry-update-user-product-content 'wte-registry-update-user-product-content' {
        description 'PUT /profile/products/{id}/user-content'
        style {
          icon aws:lambda
        }
      }
    }

    container registryRDS 'Registry Database' {
      description 'MySQL RDS for Registry'
      style {
        shape storage
        icon aws:rds
      }
    }

    container registryDDB 'Registry DynamoDB' {
      description 'DynamoDB for Registry'
      style {
        shape storage
        icon aws:dynamo-db
      }
    }

    container eventsSnsArn 'Registry Events SNS' {
      description 'wte-registry-events'
      style {
        shape queue
        icon aws:simple-notification-service
      }
      component apiCachePopulated 'Cache Populated msg' {
        #ghost
        description 'APICachePopulated - while called,
         it appears this does nothing other than get logged'
      }
      component retailerRegistryMissing 'Retailer Registry Missing msg' {
        description 'RetailerRegistryMissing'
      }
      component retailerLinked 'Retailer Linked msg' {
        #ghost
        description 'RetailerLinked'
      }
      component retailerUnlinked 'Retailer Unlinked msg' {
        #ghost
        description 'RetailerUnlinked'
      }
    }

    container registryRefreshSns 'Registry Refresh Products SNS' {
      #ghost
      description 'wte-registry-refresh-products'
      style {
        shape queue
        icon aws:simple-notification-service
      }
    }

    container registryRetailerIntegrationEventsSns 'Registry Retailer Integration SNS' {
      description 'wte-registry-retailer-integration-events'
      style {
        shape queue
        icon aws:simple-notification-service
      }
      component userLinkedRegistryStopped 'UserLinkedRegistryStopped msg' {
        description 'UserLinkedRegistryStopped'
      }
    }

    container registryRedis 'Registry Redis' {
      description 'Registry Elasticache Redis'
      style {
        shape storage
        icon aws:elasti-cache
      }
    }

  }

  // Each model should include the relationships it *initiates*.
  registryService -> registryRDS 'reads and writes data'
  registry -> retailers 'Retrieves registry information'

  wte-registry-get-registry -> registryDDB 'gets/saves registry/profile info
                                            gets roundups data'
  wte-registry-get-registry -> bcAuth.bcAuthServer 'gets BC user due date'
  wte-registry-get-registry -> wteUserApi.wteUserDDB 'gets WTE user due date'
  wte-registry-get-registry -> registryRDS 'gets registry info'
  wte-registry-get-registry -> eventsSnsArn 'publishes APICachePopulated message
                                             publishes RetailerRegistryMissing message'
  wte-registry-get-registry -> eventsSnsArn.apiCachePopulated 'publishes APICachePopulated message'
  wte-registry-get-registry -> eventsSnsArn.retailerRegistryMissing 'publishes RetailerRegistryMissing message'                                        
  wte-registry-get-registry -> registryRedis 'checks for missing registries'
  eventsSnsArn.retailerRegistryMissing -> deToolchain.registry-update-from-retailer 'triggers lambda'
  wte-registry-purge-cache -> registryDDB 'purges cache'
  registryRetailerIntegrationEventsSns -> registry.wte-registry-purge-cache 'triggers lambda'
  registry.registryService -> fedGQL.gateway 'saves product data'
}