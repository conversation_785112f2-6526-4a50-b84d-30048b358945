model {
  system wteUserLeadgen 'WTE User Leadgen' {
    description 'WTE User Leadgen'
    
    container wteUserLeadgen 'wte-user-leadgen' {
      description 'WTE User Leadgen'
      style {
        icon: aws:lambda
      }
    }

    container wteUserLeadgenSqs 'wte-user-ddb-leadgen' {
      description 'WTE User Leadgen SQS'
      style {
        icon: aws:simple-queue-service
        shape: queue
      }
    }
  }

  wteSns.wteUserEvents -> wteUserLeadgen.wteUserLeadgenSqs 'Publishes SNS event'

  wteUserLeadgen.wteUserLeadgen -> wteUserLeadgen.wteUserLeadgenSqs 'Polls SQS'
}