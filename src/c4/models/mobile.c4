model {
  mobile = system 'Mobile Apps' {
    description 'Serves mobile content'
    style {
      shape mobile
    }
    container wteAndroid 'WTE Android App' {
      description 'WTE Android App'
      style {
        icon tech:android
        shape mobile
      }
    }

    container wteiOS 'WTE iOS App' {
      description 'WTE iOS App'
      style {
        icon tech:swift
        shape mobile
      }
    }

    container bciOS 'BC iOS App' {
      description 'BC iOS App'
      style {
        icon tech:swift
        shape mobile
      }
    }

    container bcAndroid 'BC Android App' {
      description 'BC Android App'
      style {
        icon tech:android
        shape mobile
      }
    }

    container bcBabynamesApp 'BC Babynames App' {
      description 'BC Babynames App'
      link https://github.com/babycenter/bc-ios-babynames 'GitHub'
      style {
        icon tech:swift
        shape mobile
      }
    }
  }

  // Each model should include the relationships it *initiates*.
  wteAndroid -> fedGQL.gateway.activeDeals 'Gets Daily Deals'
  wteiOS -> fedGQL.gateway.activeDeals 'Gets Daily Deals'
  bcAndroid -> fedGQL.gateway.activeAFC 'Gets App First Content'
  bciOS -> fedGQL.gateway.activeAFC 'Gets App First Content'
  bciOS -> bcWeb.bcGql 'Sends/receives data'
  // bciOS -> bcWeb.bcReactServer 'Loads WebView'
  bciOS -> bcNotifications.notificationService 'Gets/reads notifications'
  // bcAndroid -> bcWeb.bcReactServer 'Loads WebView'
  bcAndroid -> bcNotifications.notificationService 'Gets/reads notifications'
  bcAndroid -> bcWeb.bcGql 'Sends/receives data'

  bciOS -> fedGQL.gateway.activeAFC
  bcAndroid -> fedGQL.gateway.activeAFC

  bciOS -> thirdParty.localytics 'Updates profile data'
  bcAndroid -> thirdParty.localytics 'Updates profile data'

  bciOS -> snowplow.collector 'Sends events'
  bcAndroid -> snowplow.collector 'Sends events'
}