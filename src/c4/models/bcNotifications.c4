// This model has been merged into bcBookmarks.c4 as bcBookmarkNotification
// The bookmark and notification services are now a single consolidated service

// This file is kept for backward compatibility with existing views
// All containers and relationships are now defined in bcBookmarks.c4

model {
  // Alias for backward compatibility - references the unified service
  bcNotifications = bcBookmarkNotification
}
