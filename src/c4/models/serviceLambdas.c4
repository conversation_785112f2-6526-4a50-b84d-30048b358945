model {
  system serviceLambdas 'Service Team Lambdas' {
    container bc-check-content-toxicity-perspective-api-queue 'BC Toxicity SQS' {
      description 'bc-check-content-toxicity-perspective-api-queue'
      link https://551062308343-i45g3llr.us-east-1.console.aws.amazon.com/sqs/v3/home?region=us-east-1#/queues/https%3A%2F%2Fsqs.us-east-1.amazonaws.com%2F551062308343%2Fbc-check-content-toxicity-perspective-api-queue
      style {
        icon aws:simple-queue-service
      }
    }

    container bc-lambda-check-content-toxicity 'BC Content Toxicity Lambda' {
      description 'bc-lambda-check-content-toxicity'
      link https://github.com/babycenter/bc-lambda-check-content-toxicity
      style {
        icon aws:lambda
      }
    }

    container bc-parsed-from-toxicity-perspective-api 'Toxicity SNS topic' {
      description 'bc-parsed-from-toxicity-perspective-api'
      link https://551062308343-i45g3llr.us-west-2.console.aws.amazon.com/sns/v3/home?region=us-west-2#/topic/arn:aws:sns:us-west-2:551062308343:bc-parsed-from-toxicity-perspective-api
      style {
        icon aws:simple-notification-service
      }
    }

    container bc-lambda-abuse-report 'BC Abuse Report Lambda' {
      description 'bc-lambda-abuse-report flags post/comment'
      link https://github.com/babycenter/bc-lambda-abuse-report
      style {
        icon aws:lambda
      }
    }

    container bc-lambda-abuse-report-BcAbuseReportQueue 'BC Abuse Report SQS' {
      description 'bc-lambda-abuse-report-BcAbuseReportQueue-7zqBH57awAw7'
      link https://551062308343-i45g3llr.us-west-2.console.aws.amazon.com/sqs/v3/home?region=us-west-2#/queues/https%3A%2F%2Fsqs.us-west-2.amazonaws.com%2F551062308343%2Fbc-lambda-abuse-report-BcAbuseReportQueue-7zqBH57awAw7
      style {
        icon aws:simple-queue-service
      }
    }

    container bc-member-events-monitor 'BC Member Events Monitoring Lambda' {
      description 'bc-member-events-monitor (No github repo)'
      style {
        icon aws:lambda
      }
    }

    container sailthru-transactional-email 'Sailthru Transactional Email Lambda' {
      description 'sailthru-transactional-email (PPDE)'
      link https://github.com/babycenter/ppde-sailthru-lambda
      style {
        icon aws:lambda
      }
    }

    container bc-update-thinkific-ts 'BC Update Thinkific Lambda' {
      description 'bc-update-thinkific-ts'
      link https://github.com/babycenter/bc-lambda-thinkific-ts
      style {
        icon aws:lambda
      }
    }

    container bc-us-memberprofile_lambda 'Sailthru MemberProfile Lambda' {
      description 'bc-us-memberprofile_lambda (PPDE)'
      link https://github.com/babycenter/ppde-sailthru-lambda
      style {
        icon aws:lambda
      }
    }

    container bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt 'BC Localytics Lambda' {
      description 'bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt'
      link https://github.com/babycenter/bc-localytics-lambda
      style {
        icon aws:lambda
      }
    }

    container bc-loyaltyRewards-lambda 'Proposed Loyalty/Rewards Lambda' {
      description 'Proposed Loyalty/Rewards Lambda'
      style {
        icon aws:lambda
        color amber
      }
    }

    container bc-rewardsReader-lambda 'Proposed Rewards Reader Lambda' {
      description '(triggered by hook or DAG)'
      style {
        icon aws:lambda
        color amber
      }  
    }
  }

  bc-lambda-check-content-toxicity -> bc-check-content-toxicity-perspective-api-queue 'polls SQS'
  bc-member-events-monitor -> cloudWatch.memberEventsDashboard 'publishes metrics'
  bc-lambda-check-content-toxicity -> bc-parsed-from-toxicity-perspective-api 'publishes SNS message for bad content'
  bc-lambda-check-content-toxicity -> prediktor 'checks for spam'
  bc-lambda-check-content-toxicity -> akismet 'checks for spam'
  bc-lambda-check-content-toxicity -> perspective 'checks for toxicity'
  bc-parsed-from-toxicity-perspective-api -> bc-lambda-abuse-report-BcAbuseReportQueue 'publishes to SQS'
  bc-lambda-abuse-report -> bc-lambda-abuse-report-BcAbuseReportQueue 'polls SQS'

  bc-check-content-toxicity-perspective-api-queue -> bcWeb.bcGql.moderationQueue 'Adds to moderation queue' {
    navigateTo toxicityAbuse
  }

  sailthru-transactional-email -> bcSqs.bc-us-sailthru-transactional 'consumes'
  sailthru-transactional-email -> unknown 'please fill in'

  bc-us-memberprofile_lambda -> bcSqs.bc-us-sailthru-member-data 'consumes'
  bc-us-memberprofile_lambda -> unknown 'please fill in'

  bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt -> bcSqs.bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut 'consumes (is triggered by)'
  bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt -> thirdParty.localytics 'updates profile data'

  // proposed
  bc-loyaltyRewards-lambda -> thirdParty.rewardsVendor 'Filters and sends behavioral data'
  bc-rewardsReader-lambda -> thirdParty.rewardsVendor 'Queries for completed Rewards'
  bc-rewardsReader-lambda -> thirdParty.localytics 'Updates profile'
  bc-rewardsReader-lambda -> thirdParty.sailthru 'Updates profile'
}