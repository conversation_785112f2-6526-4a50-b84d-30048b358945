/*
  We want to follow the C4 model for our architecture diagrams.
  So in addition to 'actor', which is just a person, we have
    'system', 'container', and 'component'.

  Remember a 'container' is a deployable unit, like a web server or database.
*/
specification {
  element actor {
    style {
      textSize large
      size medium
      padding xsmall
      shape person
    }
  }
  element system {
    style {
      textSize large
      size medium
      padding xsmall
    }
  }
  element container {
    style {
      textSize large
      size medium
      padding xsmall
    }
  }
  element component {
    style {
      textSize large
      size medium
      padding xsmall
    }
  }

  tag deprecated
  // 'ghost' means *potentially* uncalled or defunct
  // if confirmed, it should just be deleted from code, 
  // and then from here
  tag ghost
  tag proposed
}
