views {
  /**
   * @likec4-generated(v1)
   * iKRoYXNo2Sg4N2JlYzkzY2FlNGFkYjU1ZjU5ODQ1ZmFkNDBjNzcyYWQ4OTNjN2E3qmF1dG9MYXlvdXSBqWRpcmVjdGlvbqJUQqF4CKF5AKV3aWR0aM0Nw6ZoZWlnaHTNBg6lbm9kZXOPp2VuZFVzZXKCoWKUzOIAzQFAzLShY8K3YmNXZWIuYmNDb21tdW5pdHlFdmVudHOCoWKUzQLCzQPezQaazQEZoWPDqW1vZGVyYXRvcoKhYpTNBfUAzQF1zLShY8LZPnNlcnZpY2VMYW1iZGFzLmJjLWNoZWNrLWNvbnRlbnQtdG94aWNpdHktcGVyc3BlY3RpdmUtYXBpLXF1ZXVlgqFilMyhzQVazQH6zLShY8KrYmNXZWIuYmNHcWyCoWKUCM0BD80ISM0CT6Fjw9kjYmNXZWIuYmNDb21tdW5pdHlFdmVudHMucG9zdENyZWF0ZWSCoWKUzQLqzQQbzQFAzLShY8LZImJjV2ViLmJjQ29t
   * bXVuaXR5RXZlbnRzLnBvc3RFZGl0ZWSCoWKUzQSYzQQbzQFAzLShY8LZJmJjV2ViLmJjQ29tbXVuaXR5RXZlbnRzLmNvbW1lbnRDcmVhdGVkgqFilM0GRs0EG80BQMy0oWPC2SViY1dlYi5iY0NvbW11bml0eUV2ZW50cy5jb21tZW50RWRpdGVkgqFilM0H9M0EG80BQMy0oWPCuWJjV2ViLmJjR3FsLmNvbnRlbnRDcmVhdGWCoWKUzQOMzQFMzQFAzLShY8K+YmNXZWIuYmNHcWwuY29udGVudEFidXNlUmVwb3J0gqFilDDNAUzNAUDMtKFjwrtiY1dlYi5iY0dxbC51c2VyQWJ1c2VSZXBvcnSCoWKUzQHezQFMzQFAzLShY8K5YmNXZWIuYmNHcWwuY29udGVudERlbGV0ZYKhYpTNBTrNAUzNAUDMtKFjwrNiY1dlYi5iY0dxbC51c2VyQmFugqFilM0G6M0BTM0BQMy0oWPC
   * u2JjV2ViLmJjR3FsLm1vZGVyYXRpb25RdWV1ZYKhYpTNAb/NAoLNAX7MtKFjwqVlZGdlc42mNmJrcjAzgqFshKF4zQG8oXnNAQ6ld2lkdGjMlqZoZWlnaHQSoXCakszizIeSzI7MppJCzNKSd80BB5LMhc0BFZLNA0LNAQmSzQNVzQEPks0Des0BG5LNA5/NATCSzQO/zQFGpzFtbHEwdW6DomNwkYKheMtAiNM7T5aO9KF5y0CT7SNyNyNyoWyEoXjNAtmhec0FHqV3aWR0aHCmaGVpZ2h0EqFwlJLNCcHNAzaSzQn6zQNhks0KP80DlJLNCnnNA7+nMXU4czJnaYOiY3CRgqF4y0CRbszMzMzNoXnLQJOpHJAtFE6hbISheM0DnaF5zQUlpXdpZHRocKZoZWlnaHQSoXCUks0K+M0DNpLNCvjNA1+SzQr4zQOQks0K+M0Du6Y4ZGZhZjiDomNwkYKheMtAliLMzMzMzaF5y0CTnRVS
   * Y6PQoWyEoXjNBHShec0FJKV3aWR0aHCmaGVpZ2h0EqFwlJLNDC/NAzaSzQv2zQNhks0Lsc0DlJLNC3fNA7+nMXFteW80OYOiY3CRgqF4y0Ca1szMzMzNoXnLQJOWdaXMUYmhbISheM0FSqF5zQUkpXdpZHRocKZoZWlnaHQSoXCXks0Ny80DNpLNDbLNA0WSzQ2XzQNTks0Nfc0DXpLNDQPNA5SSzQx2zQPAks0L/80D4KcxNWxkd2Z3g6JjcJGCoXjLQH7EbkbkbkahectAktZmZmZmZqFshKF4zQIOoXnNBEild2lkdGjMqKZoZWlnaHQSoXCUks0J+80D+ZLNCEfNA7iSzQTUzQM2ks0DR80C+6cxcDRjNnV4gqFshKF4zQEroXnM+6V3aWR0aMyHpmhlaWdodBKhcJSSzQFSzLSSzQE6zOCSzQEdzQEWks0BBc0BQ6Y2anJvNjmCoWyEoXjNAf2hecz8pXdpZHRoc6ZoZWlnaHQS
   * oXCUks0Bxsy0ks0B6Mzgks0CEc0BF5LNAjTNAUSnMTF6aDh6dIKhbISheM0GOaF5zPmld2lkdGjMlKZoZWlnaHQSoXCXks0Gasy0ks0GW8zHks0GTMzcks0GP8zwks0GLs0BCpLNBh3NASiSzQYOzQFDpzEyYXNhdzWCoWyEoXjNBxmhecz8pXdpZHRoRaZoZWlnaHQSoXCUks0G6sy0ks0HB8zgks0HKs0BFpLNB0jNAUSmamd2ZDlzgaFwlJLNAUzNAgCSzQGCzQInks0Bws0CVZLNAfrNAnymYm5raTNzgaFwlJLNAn7NAgCSzQJ+zQIlks0Cfs0CUZLNAn7NAnimdHhiazlygqJjcJGCoXjLQJPYy4cnwGahectAhKGZmZmZmqFwmpLNBMzNAfCSzQTezQH2ks0E8c0B/JLNBQPNAgCSzQZ9zQJbks0G/M0BxZLNCGvNAkWSzQhvzQJHks0IdM0CSJLNCHjNAko=
   */
  view communityModerationSystem {
    title 'BC Community Moderation'
    include
      endUser -> bcWeb.bcGql.contentCreate,
      bcWeb.bcGql.contentCreate -> bcWeb.bcGql.postCommentService,
      bcWeb.bcGql -> bcWeb.bcCommunityEvents,
      bcWeb.bcCommunityEvents.postCreated -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue,
      bcWeb.bcCommunityEvents.postEdited -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue,
      bcWeb.bcCommunityEvents.commentCreated -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue,
      bcWeb.bcCommunityEvents.commentEdited -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue,
      serviceLambdas.bc-check-content-toxicity-perspective-api-queue -> bcWeb.bcGql.moderationQueue,
      endUser -> bcWeb.bcGql.contentAbuseReport,
      endUser -> bcWeb.bcGql.userAbuseReport,
      moderator -> bcWeb.bcGql.contentDelete,
      moderator -> bcWeb.bcGql.userBan,
      bcWeb.bcGql.contentAbuseReport -> bcWeb.bcGql.moderationQueue,
      bcWeb.bcGql.userAbuseReport -> bcWeb.bcGql.moderationQueue,
  }
}