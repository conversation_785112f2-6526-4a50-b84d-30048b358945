views {
  dynamic view toxicityAbuse {
    title 'BC Toxicity/Abuse messages'
    description 'BabyCenter Toxicity/Abuse message flow'
    include
      bcWeb.bcCommunityEvents,
      serviceLambdas.bc-check-content-toxicity-perspective-api-queue,
      serviceLambdas.bc-lambda-check-content-toxicity,
      prediktor,
      akismet,
      perspective,
      serviceLambdas.bc-parsed-from-toxicity-perspective-api,
      serviceLambdas.bc-lambda-abuse-report,
      serviceLambdas.bc-lambda-abuse-report-BcAbuseReportQueue
    bcWeb.bcCommunityEvents -> serviceLambdas.bc-check-content-toxicity-perspective-api-queue 'SNS Topic to SQS' {
      notes 'Create/Edit posts/comments events'
    }
    serviceLambdas.bc-check-content-toxicity-perspective-api-queue -> serviceLambdas.bc-lambda-check-content-toxicity 'Triggers lamba' {
      notes 'Lambda processes multiple messages'
    }
    serviceLambdas.bc-lambda-check-content-toxicity -> prediktor 'Calls Prediktor Service' {
      notes 'Sends post and comment text for evaluation. Uses spam_[lang]_[v1|v2] models'
    }
    serviceLambdas.bc-lambda-check-content-toxicity -> akismet 'Calls Akismet API' {
      notes 'If passes Prediktor check, sends to Akismet'
    }
    serviceLambdas.bc-lambda-check-content-toxicity -> perspective 'Calls Perspective API' {
      notes 'If passes Akismet check, sends to Perspective API'
    }
    serviceLambdas.bc-lambda-check-content-toxicity -> serviceLambdas.bc-parsed-from-toxicity-perspective-api 'Sends to abuse report topic'{
      notes 'If post/comment is spam or toxic, send to abuse report topic'
    }
    serviceLambdas.bc-parsed-from-toxicity-perspective-api -> serviceLambdas.bc-lambda-abuse-report-BcAbuseReportQueue {
      notes 'SNS to SQS'
    }
    serviceLambdas.bc-lambda-abuse-report -> serviceLambdas.bc-lambda-abuse-report-BcAbuseReportQueue {
      notes 'Triggers lambda'
    }
    bcWeb.bcGql -> serviceLambdas.bc-lambda-abuse-report-BcAbuseReportQueue 'Internal GQL schema'{
      notes 'Calls createAbuseReport mutation'
    }
  }
}