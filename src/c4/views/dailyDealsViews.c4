views {

  view dailyDealsSystem {
    title 'Daily Deals System'
    include endUser -> mobile with {
      title 'Browses Daily Deals'
    }
    include 
      mobile,
      fedGQL,
      editor,
      adminTools,
      editor -> adminTools with {
        title 'Defines Daily Deals'
      },
      adminTools -> fedGQL with {
        title 'Sets active Daily Deals'
      }

    exclude
      mobile.bcAndroid,
      mobile.bciOS
  }

  view dailyDealsContainers {
    title 'Daily Deals Containers'
    include
      editor,
      adminTools.dealsSheet,
      fedGQL.gateway,
      fedGQL.intGateway,
      fedGQL.dailyDeals,
      fedGQL.intDailyDeals,
      fedGQL.dailyDealsDB,
      endUser,
      mobile.wteAndroid,
      mobile.wteiOS
  }
}