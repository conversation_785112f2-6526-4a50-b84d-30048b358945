views {
  style element.tag = #proposed {
    color amber
  }

  view categorizationContext {
    title 'Categorization Context'
    description 'High-level overview of Categorization effort'
    include
      deToolchain,
      dags,
      fedGQL.intGateway,
      registry.registryRDS,
      deToolchain.redshift
  }

  view deCategorization {
    title 'Categorization Details - DE'
    description 'Dag Flow for DE categorization'
    include
      dags.dagCMSReplicator,
      dags.dagProductProcessor,
      push.cmsDB,
      push.contentAPI,
      deToolchain.redshift,
      deToolchain.classifier,
      fedGQL.intGateway
    autoLayout LeftRight
  }

  view deProductLoading {
    title 'Product Loading Details - DE'
    description 'Dag Flow for DE product loading'
    include
      dags.dagProductProcessor,
      push.contentAPI,
      deToolchain.redshift,
      deToolchain.classifier,
      fedGQL.intGateway
    autoLayout LeftRight
  }

  view deRegistryClassifying {
    title 'Registry Product Classifying - DE'
    description 'Dag Flow for DE registry product classification'
    include
      dags.dagRegistryProductClassifier,
      registry.registryRDS,
      deToolchain.classifier,
      fedGQL.intGateway
  }

  view productCatalogSystem {
    title 'Product Catalog Usage'
    description 'System overview for Product Catalog'
    include
      endUser,
      wteWeb,
      fedGQL,

    autoLayout LeftRight
  }

  view productCatalogContainers {
    title 'Product Catalog Container View'
    description 'Containers for Product Catalog'
    include
      dags.dagProductProcessor,
      fedGQL.intGateway,
      fedGQL.intProductCatalog,
      fedGQL.productCatalogDB,
      wteWeb.wteWebServer,
      fedGQL.gateway,
      fedGQL.productCatalog,
  }

  view productCatalogNow {
    title 'Current PC Flow'
    description 'Current Product Catalog Flow'
    include
      editor -> push with {
        title 'Writes product data'
      },
      endUser,
      wteWeb,
      adminTools.dealsSheet,
      fedGQL,
      push.contentAPI,
      push.cms,
      dags.dagProductProcessor -> fedGQL with {
        title 'inserts products'
      },
      dags.dagProductProcessor,
      mobile -> fedGQL with {
        title 'gets Daily Deals'
      },
      fedGQL.productCatalog

    autoLayout LeftRight
  }

  view productCatalogProposed {
    title 'Proposed PC Flow'
    description 'Proposed Product Catalog Flow'
    include
      editor,
      endUser,
      wteWeb,
      adminTools.dealsSheet,
      adminTools.productTool,
      fedGQL,
      mobile ->fedGQL with {
        title 'gets Daily Deals'
      },
      fedGQL.productCatalog,
      registry,
      registry -> fedGQL with {
        color amber
      }

    autoLayout LeftRight
  }
}