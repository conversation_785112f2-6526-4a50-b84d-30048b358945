views {
  view productGuideSystem {
    title 'Registry Product Guides'
    description 'System Overview for Registry Product Guides'
    include
      editor,
      push.cms,
      push.contentAPI,
      dags.upsert_roundup_page_products,
      deToolchain.redshift,
      dags.upsert_roundup_products,
    //   bcWeb.bcReactServer,
      fedGQL.intGateway.upsertRoundupProducts,
      fedGQL.productCatalog,
      wteWeb.wteWebServer,
      fedGQL.gateway.roundupProducts,

    autoLayout LeftRight
  }
}