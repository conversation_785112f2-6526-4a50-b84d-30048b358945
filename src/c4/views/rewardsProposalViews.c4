views {

  view rewardsFlow {
    title 'Loyalty/Rewards Events'
    description 'Sample Data Flow'

    include
      bcWeb with {
        title 'BC Clients'
        description 'BabyCenter Mobile & Web'
      },
      bcSns,
      snowplow,
      serviceLambdas.bc-loyaltyRewards-lambda,
      thirdParty.rewardsVendor,
      serviceLambdas.bc-rewardsReader-lambda,
      thirdParty.localytics,
      thirdParty.sailthru,
      endUser
    
    exclude
      endUser -> bcWeb
	}
}