views {
  view bcWeb {
    title 'BabyCenter Website'
    description 'System overview for BabyCenter Website'
    include
      endUser,
      // bcWeb,
      bcWeb.bcReactApp,
      bcWeb.bcReactServer,
      bcWeb.bcGql,
      mobile.bcAndroid,
      mobile.bciOS,
      // bcAuth
  }

  view bcNotificationEvents {
    title 'BabyCenter Notification Events'
    description 'BabyCenter Notification Events SNS traffic'
    include
      bcWeb.bcGql,
      bcWeb.bcCommunityEvents,
      bcNotifications,
  }

  view bcCommunityEvents {
    title 'BabyCenter Community Events'
    description 'BabyCenter Community Events SNS traffic'
    include
      bcWeb.bcGql,
      bcWeb.bcCommunityEvents,
      bcWeb.bcCommunityEvents.*,
    //   bcNotifications.bc-community-notification-svc,
      deToolchain.genai-community-insights-post-tagging-v2,
      dags.bc-community-events-delete-ppde,
      dags.bc_community_delete_sqs,
      serviceLambdas.bc-check-content-toxicity-perspective-api-queue,
      serviceLambdas.bc-lambda-check-content-toxicity

    autoLayout LeftRight
  }

  view bcMemberEvents {
    title 'BabyCenter Member Events'
    description 'BabyCenter Member Events SNS traffic'
    include
      bcWeb.bcGql,
      bcSns.bcUsMemberEvents,
      bcSns.bcIntlMemberEvents,
      serviceLambdas.bc-member-events-monitor,
      cloudWatch.memberEventsDashboard
  }

  view beRegistration-localytics-write {
    title 'BabyCenter Registration'
    description 'Flow of Registration data'
    include 
      endUser -> mobile with {
        title 'Registers with site'
      },
      mobile -> thirdParty.localytics with {
        title 'Updates profile data'
      },
      endUser -> bcWeb with {
        title 'Registers with site'

      },
      bcWeb -> bcAuth.bcAuthServer with {
        title 'Registers user data'
      },
      bcAuth.bcAuthServer,
      bcSns.bcUsMemberEvents,
      bcSqs.bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut,
      serviceLambdas.bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt,
      thirdParty.localytics,

    exclude
      bcWeb -> bcSns.bcUsMemberEvents,
      thirdParty.localytics -> endUser
  }

  view beRegistration-localytics-read {
    title 'BabyCenter Registration'
    description 'Flow of Registration data'
    include 
      marketer,
      endUser,
      thirdParty.localytics,

    exclude
      bcWeb -> bcSns.bcUsMemberEvents
  }

  view beRegistration-sailthru {
    title 'BabyCenter Registration'
    description 'Flow of Registration data'
    include 
      endUser -> bcWeb with {
        title 'Registers with site'

      },
      // endUser -> bcWeb.bcGql with {
      //   title 'Mutates data'
      // },
      bcWeb -> bcAuth.bcAuthServer with {
        title 'Registers user data'
      },
      // bcWeb.bcReactApp,
      // bcWeb.bcReactServer,
      // bcWeb.bcGql,
      bcAuth.bcAuthServer,
      bcSns.bcUsMemberEvents,
      // bcSns.bcUsMemberEvents.*,
      serviceLambdas.sailthru-transactional-email,
      bcSqs.bc-us-sailthru-member-data,
      serviceLambdas.bc-us-memberprofile_lambda,
      // bcSqs.bc-localytics-BcLocalyticsQueue-pEGl7H6zpxut,
      // serviceLambdas.bc-localytics-BcLocalyticsLambda-sJIszJeDMfvt,

      bcSqs.bc-us-sailthru-transactional,

      // bcSqs.wte-registry-delete-baby-prod,

      unknown
  }
}