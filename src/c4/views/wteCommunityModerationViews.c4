views {
  view wteCommunityModeration {
    title 'WTE Community Moderation'
    description 'Community Moderation Flow'
    include 
      endU<PERSON>, moderator, 
      wteWeb.community.*, 
      wteAdmin.*,
      wteCommunityApi.*,
      wteCommunityApi.wteCommunityLambda.contentCheck -> akismet,
      wteCommunityApi.wteCommunityLambda.contentCheck -> perspective,
      wteCommunityApi.wteCommunityLambda.contentCheck -> wteAdmin.violationQueue,
      wteWeb.contentCreate -> wteCommunityApi.wteCommunityLambda,
      moderator -> wteAdmin.contentReleased,
      moderator -> wteAdmin.contentDeleted,
      moderator -> wteAdmin.wteAdminUI.userBan,
      moderator -> wteAdmin.wteAdminUI.userBanFromGroup
  }
}
