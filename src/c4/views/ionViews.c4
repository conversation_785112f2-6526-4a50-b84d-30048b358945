views {
  view ionDataFlow {
    title: 'Ion Data Flow'
    description: 'Data flow from a Services/DE perspective'
    include
      ion.*,
      dags.dagScribbleLido,
      deToolchain.redshift,
      edhFtp.ftpServer,
      wteStandaloneCampaign.wteStandaloneCampaignApi,
      ion.ionScriplets -> wteStandaloneCampaign.wteStandaloneCampaignApi with {
        title 'Calls API endpoints'
      }

    exclude
      ion.ionDataRepository,
      ion.ionHostedSite
  }

  view ionIntegrationScriplets {
    title: 'Ion Integrations and Scriplets'
    description: 'Zoomed in look in how data propagates within Ion'
    include
      // Creatives
      ion.aeroflowDirectStandaloneCreative,
      ion.currentSweepsCreative,
      ion.babyBumpGiveawayThankYouCreative,
      ion.viacordSurveyBcCreative,
      ion.viacordSurveyWteCreative,
      // Integrations
      ion.ppdeExportToAirflowIntegration,
      ion.sweepsSkylerHistoricalIntegration,
      ion.viaCordProductionIntegration,
      ion.viaCordWteProductionIntegration,
      // Scriplets
      ion.aeroflowConsentFeb2025Scriplet,
      ion.aeroflowConsentSep2024Scriplet,
      ion.leadApiCervidilScriplet,
      ion.leadApiHuggiesScriplet,
      ion.leadApiNaturalWayScriplet,

    autoLayout LeftRight
  }

  view ionProblem {
    title: 'Ion Problem'
    description: 'Illustrates our problems'
    include
      marketer,
      endUser,
      ion.ionAuthoringTool,
      ion.ionDataRepository,
      ion.ionHostedSite,
      ion.ionIntegrations,
      ion.ionScriplets,
      edhFtp,
      wteStandaloneCampaign,
      ion.ionScriplets -> wteStandaloneCampaign with {
        title 'calls standalone campaign api'
      },
      // dags.dagScribbleLido,
      // deToolchain.redshift,
      // wteSns,
      // wteLeadSweeps

    exclude
      ion.ionAuthoringTool -> ion.ionScriplets,
      ion.ionAuthoringTool -> ion.ionIntegrations

    autoLayout LeftRight
  }

  view ionFtp {
    title 'Ion FTP'
    description 'Ion FTP Flow'
    include
      dags,
      dags.dagScribbleLido,
      ion.ionIntegrations.* -> edhFtp,
      edhFtp.*,
      deToolchain.redshift
  }

  view ionUserEvents {
    title 'Ion User Events'
    description 'Ion User Events for Leadgen'
    include
      wteStandaloneCampaign,
      wteUserApi.*,
      wteSns.wteUserEvents,
      wteUserLeadgen.*
  }

  view ionSns {
    title: 'Ion SNS'
    description: 'Ion SNS Communication'
    include
      wteStandaloneCampaign,
      wteSns.wteLeadSweeps,
      wteLeadSweeps.*,

    autoLayout LeftRight
  }
}