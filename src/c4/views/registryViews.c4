views {
  style element.tag = #ghost {
    color muted
  }

  view registrySystem {
    title 'Registry System'
    description 'System overview for Registry Builder'
    include
      endUser,
      bcWeb,
      wteWeb,
      registry,
      retailers,
  }

  view registryService {
    title 'Registry Service'
    description 'All registry endpoints'
    include
      registry,
      registry.registryService,
      registry.registryService.*
  }

  view registrySns {
    title 'Registry SNS Comms'
    description 'Registry SNS Communication'
    include
      registry,
      registry.eventsSnsArn,
      registry.eventsSnsArn.*
  }

  view registryProfile {
    title 'Get Registry'
    description 'Get Registry - Complete'

    include
      registry.wte-registry-get-registry,
      registry.registryDDB,
      registry.registryRDS,
      registry.eventsSnsArn.*,
      registry.registryRedis,
      deToolchain.registry-update-from-retailer,
      deToolchain.targetRegistryAPI,
      registry.registryRetailerIntegrationEventsSns,
      deToolchain.registryUpdateSQS,
      deToolchain.registryProductUpdates,
      registry.wte-registry-purge-cache,
      bcAuth,
      wteUserApi.wteUserDDB,
      
    autoLayout LeftRight
  }

  view registryGetRegistry {
    title 'Get Registry - Services'
    description 'Get Registry - Services view'
    include
      registry.wte-registry-get-registry,
      registry.registryDDB,
      registry.registryRDS,
      registry.registryRedis,
      registry.eventsSnsArn,
      deToolchain,
      registry.eventsSnsArn.*,
      registry.eventsSnsArn.retailerRegistryMissing -> deToolchain.registry-update-from-retailer

    exclude
      registry.eventsSnsArn.retailerLinked,
      registry.eventsSnsArn.retailerUnlinked
    
    autoLayout LeftRight
  }

  view registryDeSnsSqs {
    title 'Registry DE SNS/SQS'
    description 'SNS/SQS Registry Messages flow for DE'
    include
      registry.eventsSnsArn,
      deToolchain.registry-update-from-retailer,
      deToolchain.registryUpdateSQS,
      deToolchain.targetRegistryAPI,
      deToolchain.registryProductUpdates,
  }

  view registryDeMessages {
    title 'Registry DE Messages'
    description 'SNS/SQS Registry Messages of DE'
    include
      deToolchain.registry-update-from-retailer,
      registry.registryRetailerIntegrationEventsSns,
      deToolchain.registryUpdateSQS,
      deToolchain.targetRegistryAPI,
      deToolchain.registryProductUpdates,
      registry.wte-registry-purge-cache,
      registry.registryDDB,
  }

  view registryDeRepositories {
    title 'Registry DE Repository Comms'
    description 'Registry DE Repository Communications'
    include
      registry.eventsSnsArn,
      registry.eventsSnsArn.retailerRegistryMissing,
      deToolchain.registry-update-from-retailer,
      deToolchain.registryProductUpdates,
      registry.registryRedis,
      registry.registryRDS,
  }

  view bcRegistry {
    title 'BC Web Registry calls'
    description 'BC Registry integration'
    include
      bcWeb.bcReactApp,
      registry.registryService._

    autoLayout LeftRight 400 10
  }

  view registryArchitecture {
    title 'Registry Architecture'
    description 'Services Diagram'
    link https://everydayhealth.atlassian.net/wiki/spaces/PP/pages/**********/Registry+API
    include
      bcWeb,
      registry.registryService.wte-registry-get-registry,
      registry.registryDDB,
      registry.registryRDS,
      registry.registryRedis,
      registry.eventsSnsArn.retailerRegistryMissing,
      deToolchain.registry-update-from-retailer,
      registry.registryRetailerIntegrationEventsSns,
      deToolchain.registryUpdateSQS,
      deToolchain.targetRegistryAPI,
      deToolchain.registryProductUpdates,
      registry.wte-registry-purge-cache

    autoLayout LeftRight
  }
}