views {
  view bookmarkService {
    title 'BC Bookmark Service Architecture'
    description 'Overview of the BabyCenter Bookmark Service showing API, database, and event publishing'
    
    include bcBookmarks.*
    include bcAuth.bcAuthService
    include bcNotifications.subscriptionEventsQueue
    
    autoLayout topBottom
    
    style {
      bcBookmarks.bookmarkApi {
        color #1f77b4
      }
      bcBookmarks.bookmarkDb {
        color #ff7f0e
      }
      bcBookmarks.bookmarkEvents {
        color #2ca02c
      }
    }
  }
  
  view notificationService {
    title 'BC Notification Service Architecture'
    description 'Overview of the BabyCenter Notification Service showing API, databases, SQS queues, and Lambda processors'
    
    include bcNotifications.*
    include bcAuth.bcAuthService
    include wteCommunityApi.communityApi
    include bcBookmarks.bookmarkApi
    
    autoLayout topBottom
    
    style {
      bcNotifications.notificationApi {
        color #1f77b4
      }
      bcNotifications.notificationDb {
        color #ff7f0e
      }
      bcNotifications.subscriptionDb {
        color #ff7f0e
      }
      bcNotifications.communityEventsQueue {
        color #d62728
      }
      bcNotifications.subscriptionEventsQueue {
        color #d62728
      }
      bcNotifications.communityEventsProcessor {
        color #9467bd
      }
      bcNotifications.subscriptionEventsProcessor {
        color #9467bd
      }
    }
  }
  
  view bookmarkNotificationIntegration {
    title 'Bookmark & Notification Service Integration'
    description 'How the Bookmark and Notification services work together for community post subscriptions'
    
    include bcBookmarks.*
    include bcNotifications.*
    include bcAuth.bcAuthService
    include wteCommunityApi.communityApi
    
    autoLayout leftRight
    
    style {
      bcBookmarks {
        color #1f77b4
      }
      bcNotifications {
        color #ff7f0e
      }
      bcAuth.bcAuthService {
        color #2ca02c
      }
      wteCommunityApi.communityApi {
        color #d62728
      }
    }
  }
  
  view eventFlow {
    title 'Notification Event Flow'
    description 'End-to-end flow of how community events create notifications'
    
    include wteCommunityApi.communityApi
    include bcNotifications.communityEventsQueue
    include bcNotifications.communityEventsProcessor
    include bcNotifications.subscriptionDb
    include bcNotifications.notificationDb
    include bcNotifications.notificationApi
    include actors.member
    
    autoLayout leftRight
    
    style {
      wteCommunityApi.communityApi {
        color #d62728
      }
      bcNotifications.communityEventsQueue {
        color #ff7f0e
      }
      bcNotifications.communityEventsProcessor {
        color #9467bd
      }
      bcNotifications.subscriptionDb {
        color #8c564b
      }
      bcNotifications.notificationDb {
        color #8c564b
      }
      bcNotifications.notificationApi {
        color #1f77b4
      }
      actors.member {
        color #2ca02c
      }
    }
  }
}
