// src/components/Layout.tsx
import Link from 'next/link';
import React from 'react';
import styles from './Layout.module.css';

interface LayoutProps {
    children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
    return (
        <div className={styles.container}>
            <aside className={styles.sidebar}>
                <nav>
                    <ul>
                        <li>
                            <Link href="/">Home</Link>
                        </li>
                        <li>
                            <Link href="https://arcdoc.babycenter.com/viewer/index.html">Arc Viewer</Link>
                        </li>
                        <li>
                            Systems
                        </li>
                        <ul>
                            <li>
                                <Link href="/systems/registry">Registry</Link>
                            </li>
                        </ul>
                        <li>
                            Arc Designs
                        </li>
                        <ul>
                            <li>
                                <Link href="/designs/ion">Ion</Link>
                            </li>
                            <li>
                                <Link href="/designs/pc-categorization">Product Categories</Link>
                            </li>
                            <li>
                                <Link href="/designs/25Q1-SmartMessaging">Smart Messaging</Link>
                            </li>
                        </ul>
                    </ul>
                </nav>
            </aside>
            <main className={styles.content}>
                {children}
            </main>
        </div>
    );
};

export default Layout;
