import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';

export default async function fetchAwsSecret(secretName: string): Promise<string | undefined> {
  const awsSecretsRegion = secretName.split(':')[3];
  const client = new SecretsManagerClient({ region: awsSecretsRegion });
  const response = await client.send(
    new GetSecretValueCommand({
      SecretId: secretName,
    }),
  );
  if (!response) {
    return undefined;
  }
  return response.SecretString;
}
