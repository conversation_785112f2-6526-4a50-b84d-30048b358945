import * as winston from 'winston';
import 'winston-daily-rotate-file';
import * as expressWinston from 'express-winston';
import express from 'express';
import AppLogger from './app-logger';
import { RequestScopeRegister } from '../awilix/container-scope';
import { ConfigData } from '../config/environment';

export default function logs(
  app: express.Application,
  configs: ConfigData,
): { logger: RequestScopeRegister['logger']; mainLogger: winston.Logger } {
  const logLevel = configs.logLevel.toLowerCase();
  const format = winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
  );

  const loggerOptions: expressWinston.LoggerOptions = {
    transports: [
      new winston.transports.Console({
        level: logLevel,
      }),
      new winston.transports.DailyRotateFile({
        dirname: './logs',
        filename: 'bc-bookmarks-api-%DATE%.log',
        datePattern: 'YYYY-MM-DD-HH',
        zippedArchive: false, // zipped archive = true will not delete files
        maxSize: '100m',
        utc: true,
        maxFiles: '10',
        level: logLevel,
      }),
    ],
    format,
  };
  const mainLogger = winston.createLogger({
    ...loggerOptions,
    level: logLevel,
  });

  // Express Logger
  if (!process.env.DEBUG) {
    loggerOptions.requestWhitelist = ['requestId', 'envName', 'userAgent', 'remoteIp', 'referer'];
  }
  app.use(expressWinston.logger(loggerOptions));

  // Prevents the process from shutting down if a fire-and-forget async function fails.
  process.on('unhandledRejection', (e) => {
    mainLogger.error(`Unhandled rejections: ${e}`);
  });

  // Prevents process from shutting down if a handler finishes with an uncaught exception.
  app.use(async (_req, res, next) => {
    try {
      return await next();
    } catch (e) {
      mainLogger.error(`Unhandled exception: ${e}`);
      res.sendStatus(500);
    }
  });

  return {
    logger: ({ request }) => new AppLogger(request, mainLogger),
    mainLogger,
  };
}
