import * as winston from 'winston';
import express from 'express';

export default class AppLogger {
  #request: express.Request;
  #logger: winston.Logger;

  constructor(request: express.Request, logger: winston.Logger) {
    this.#request = request;
    this.#logger = logger;
  }

  newMeta(): Record<string, unknown> {
    return {
      req: {
        remoteIp: this.#request.remoteIp,
        userAgent: this.#request.userAgent,
        referer: this.#request.referer,
        requestId: this.#request.requestId,
        envName: this.#request.envName,
      },
    };
  }

  debug(message: string): void {
    this.#logger.log({
      meta: this.newMeta(),
      level: 'debug',
      message,
    });
  }

  debugMeta(message: string, meta: Record<string, unknown>): void {
    this.#logger.log({
      meta: { ...meta, ...this.newMeta() },
      level: 'debug',
      message,
    });
  }

  info(message: string): void {
    this.#logger.log({
      meta: this.newMeta(),
      level: 'info',
      message,
    });
  }

  infoMeta(message: string, meta: Record<string, unknown>): void {
    this.#logger.log({
      meta: { ...meta, ...this.newMeta() },
      level: 'info',
      message,
    });
  }

  warn(message: string): void {
    this.#logger.log({
      meta: this.newMeta(),
      level: 'warn',
      message,
    });
  }

  warnMeta(message: string, meta: Record<string, unknown>): void {
    this.#logger.log({
      meta: { ...meta, ...this.newMeta() },
      level: 'warn',
      message,
    });
  }

  error(message: string): void {
    this.#logger.log({
      meta: this.newMeta(),
      level: 'error',
      message,
    });
  }

  errorMeta(message: string, meta: Record<string, unknown>): void {
    this.#logger.log({
      meta: { ...meta, ...this.newMeta() },
      level: 'error',
      message,
    });
  }
}
