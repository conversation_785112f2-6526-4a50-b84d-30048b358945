services:
  api:
    image: ${ECR_REPOSITORY}:${IMAGE_TAG}
    environment:
      # Prevent any migration attempts during health checks
      - SKIP_MIGRATIONS=true
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASS=password
      - DB_NAME=bookmarks
      - ENVIRONMENT=dev
      - PORT=4000
      - BC_AUTH_SIGNATURE=file:keys/dev_key_public.pem
      - BC_AUTH_BASE_URL=https://qa-auth-service-internal.babycenter.com
      - DYNAMODB_ENDPOINT=http://dynamodb-local:8000
      - DYNAMODB_REGION=us-east-1
      - DYNAMODB_TABLE_NAME=notification
      - US_MEMBERS_EVENTS_SNS_ARN=arn:aws:sns:us-east-1:123456789012:test-topic
