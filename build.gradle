group 'bookmarkservice'
version '1.0-SNAPSHOT'

buildscript {
	ext {
		springBootVersion = '1.5.2.RELEASE'
		springCloudVersion = '1.2.0.RELEASE'
	}
	repositories {
		jcenter()
		maven { url "http://repo.spring.io/snapshot" }
		maven { url "http://repo.spring.io/milestone" }
	}
	dependencies {
		classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
	}
}

apply plugin: 'idea'
apply plugin: 'java'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'org.springframework.boot'

compileJava {
	sourceCompatibility = '1.8'
	targetCompatibility = '1.8'
}

jar {
	baseName = 'bookmarkservice'
	version =  '0.1.0-SNAPSHOT'
}

repositories {
	jcenter()
	maven { url "http://repo.spring.io/snapshot" }
	maven { url "http://repo.spring.io/milestone" }
}

dependencies {
	compile("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
	compile("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}")
	compile("org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}")
	compile("org.springframework.boot:spring-boot-starter-security:${springBootVersion}")
	
	compile("org.springframework.boot:spring-boot-starter-cache:${springBootVersion}")
//	compile group: 'net.sf.ehcache', name: 'ehcache', version: '2.10.3'
//	compile group: 'org.hibernate', name: 'hibernate-ehcache', version: '5.2.8.Final'
//	compile group: 'org.hibernate', name: 'hibernate-java8', version: '5.2.8.Final'
	

	compile('org.apache.httpcomponents:httpcore:4.4.13')
	// https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient
	compile group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5'
	
	compile('com.auth0:java-jwt:3.1.0')
	compile('joda-time:joda-time:2.9.7')
	
	compile('com.timgroup:java-statsd-client')
	
	compile("com.mysql:mysql-connector-j:8.4.0")
	compile('org.apache.commons:commons-lang3:3.4')

	compile 'software.amazon.awssdk:sns:2.10.20'

	testCompile("com.tngtech.java:junit-dataprovider:1.9.3")
	testCompile("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
	testCompile group: 'com.google.code.gson', name: 'gson', version: '2.3.1'

}
