{"tables": [{"tableName": "notification", "keySchema": [{"AttributeName": "UserHashKey", "KeyType": "HASH"}, {"AttributeName": "ContentRangeKey", "KeyType": "RANGE"}], "attributeDefinitions": [{"AttributeName": "UserHashKey", "AttributeType": "S"}, {"AttributeName": "ContentRangeKey", "AttributeType": "S"}, {"AttributeName": "DateNumber", "AttributeType": "N"}], "globalSecondaryIndexes": [{"IndexName": "DateNumber-index", "KeySchema": [{"AttributeName": "DateNumber", "KeyType": "HASH"}, {"AttributeName": "ContentRangeKey", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}}], "billingMode": "PAY_PER_REQUEST", "ttlAttribute": "ttl"}]}