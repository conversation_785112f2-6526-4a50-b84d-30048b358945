#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const {
  CreateTableCommand,
  DescribeTableCommand,
  UpdateTimeToLiveCommand,
  BatchWriteItemCommand,
} = require('@aws-sdk/client-dynamodb');
const { marshall } = require('@aws-sdk/util-dynamodb');

// Configuration for DynamoDB client
const config = {
  region: 'local',
  endpoint: process.env.DYNAMODB_ENDPOINT || 'http://localhost:8000',
  credentials: {
    accessKeyId: 'local',
    secretAccessKey: 'local',
  },
};

const client = new DynamoDBClient(config);

// Base paths
const tablesDir = path.resolve(__dirname, 'tables');
const seedDir = path.resolve(__dirname, 'seed');

/**
 * Create a DynamoDB table based on configuration
 */
async function createTable(tableConfig) {
  const {
    tableName,
    keySchema,
    attributeDefinitions,
    globalSecondaryIndexes,
    billingMode,
    ttlAttribute,
  } = tableConfig;

  console.log(`Setting up table: ${tableName}`);

  // Check if table exists
  try {
    await client.send(new DescribeTableCommand({ TableName: tableName }));
    console.log(`Table ${tableName} already exists`);
    return true;
  } catch (err) {
    if (err.name !== 'ResourceNotFoundException') {
      console.error(`Error checking table ${tableName}:`, err);
      return false;
    }
    // Table doesn't exist, continue with creation
  }

  try {
    // Create table with the specified configuration
    await client.send(
      new CreateTableCommand({
        TableName: tableName,
        KeySchema: keySchema,
        AttributeDefinitions: attributeDefinitions,
        BillingMode: billingMode || 'PAY_PER_REQUEST',
        GlobalSecondaryIndexes: globalSecondaryIndexes || [],
      }),
    );

    console.log(`Table ${tableName} created, waiting for active status...`);

    // Wait for table to become active
    let isActive = false;
    let attempts = 0;

    while (!isActive && attempts < 20) {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      try {
        const response = await client.send(new DescribeTableCommand({ TableName: tableName }));

        if (response.Table?.TableStatus === 'ACTIVE') {
          isActive = true;
          console.log(`Table ${tableName} is now active`);
        }
      } catch (err) {
        console.log(`Waiting for table ${tableName} to become active...`);
      }

      attempts++;
    }

    // Set up TTL if specified
    if (ttlAttribute) {
      console.log(`Setting TTL attribute ${ttlAttribute} for table ${tableName}`);
      await client.send(
        new UpdateTimeToLiveCommand({
          TableName: tableName,
          TimeToLiveSpecification: {
            AttributeName: ttlAttribute,
            Enabled: true,
          },
        }),
      );
    }

    return true;
  } catch (err) {
    console.error(`Failed to create table ${tableName}:`, err);
    return false;
  }
}

/**
 * Load seed data into a table
 */
async function seedTable(tableName, items) {
  if (!items || items.length === 0) {
    console.log(`No seed data found for table: ${tableName}`);
    return;
  }

  console.log(`Seeding table ${tableName} with ${items.length} items...`);

  // Process in batches of 25 (DynamoDB's limit)
  for (let i = 0; i < items.length; i += 25) {
    const batch = items.slice(i, i + 25);
    const putRequests = batch.map((item) => ({
      PutRequest: {
        Item: marshall(item),
      },
    }));

    const params = {
      RequestItems: {
        [tableName]: putRequests,
      },
    };

    try {
      await client.send(new BatchWriteItemCommand(params));
      console.log(`Batch ${Math.floor(i / 25) + 1} written successfully`);
    } catch (err) {
      console.error(`Failed to seed batch ${Math.floor(i / 25) + 1}:`, err);
    }
  }

  console.log(`Finished seeding table: ${tableName}`);
}

/**
 * Main function to setup all tables
 */
async function setupTables() {
  // Read all table configuration files
  const tableFiles = fs.readdirSync(tablesDir).filter((file) => file.endsWith('.json'));

  if (tableFiles.length === 0) {
    console.log('No table configuration files found');
    return;
  }

  for (const file of tableFiles) {
    const filePath = path.join(tablesDir, file);
    const tableName = file.replace('.json', '');

    try {
      const configData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

      for (const tableConfig of configData.tables) {
        const success = await createTable(tableConfig);

        if (success) {
          // Check for seed data
          const seedFilePath = path.join(seedDir, `${tableName}.json`);
          if (fs.existsSync(seedFilePath)) {
            try {
              const seedData = JSON.parse(fs.readFileSync(seedFilePath, 'utf8'));
              if (seedData.items && seedData.items.length > 0) {
                await seedTable(tableConfig.tableName, seedData.items);
              }
            } catch (err) {
              console.error(`Failed to load seed data for ${tableName}:`, err);
            }
          }
        }
      }
    } catch (err) {
      console.error(`Error processing table config ${file}:`, err);
    }
  }
}

// Execute the setup
setupTables()
  .then(() => console.log('DynamoDB setup completed'))
  .catch((err) => {
    console.error('DynamoDB setup failed:', err);
    process.exit(1);
  });
