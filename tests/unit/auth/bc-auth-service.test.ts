import { StatsD } from 'hot-shots';
import BcAuthService from '../../../src/auth/bc/bc-auth-service';
import AppLogger from '../../../src/logs/app-logger';
import StatsdService from '../../../src/statsd/statsd-service';

describe('BcAuthService', () => {
  describe('validate', () => {
    let service: BcAuthService;
    const mockStatsd = new StatsdService(new StatsD({ mock: true }));

    beforeEach(() => {
      const stubLogger = {
        debug: () => {},
        info: () => {},
        error: () => {},
      } as unknown as AppLogger;
      service = new BcAuthService(
        mockStatsd,
        stubLogger,
        '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd\noLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw\nlf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux\nxw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl\n9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq\nyb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe\n+QIDAQAB\n-----END PUBLIC KEY-----',
        'https://qa-auth.babycenter.com',
      );
    });

    test('invalid token', async () => {
      const token =
        'INVALID-******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      const now = new Date('2024-08-23T20:12:00.000Z').getTime();
      const result = service.validate('qa-bookmark-service.babycenter.com', token, now);
      expect(result).toStrictEqual({ valid: false, reason: 'other' });
    });

    test('invalid audience', async () => {
      const token =
        '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      const now = new Date('2024-08-23T20:34:00.000Z').getTime();
      const result = service.validate('qa-bookmark-service.babycenter.com', token, now);
      expect(result).toStrictEqual({ valid: false, reason: 'audience' });
    });

    test('expired token', async () => {
      const token =
        '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      const now = new Date('2024-08-24T20:12:00.000Z').getTime();
      const result = service.validate('qa-bookmark-service.babycenter.com', token, now);
      expect(result).toStrictEqual({ valid: false, reason: 'expired' });
    });

    test('valid token', async () => {
      const token =
        '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      const now = new Date('2024-08-23T20:37:30.000Z').getTime();
      const result = service.validate('qa-bookmark-service.babycenter.com', token, now);
      expect(result).toStrictEqual({
        valid: true,
        user: {
          globalAuthId: '8aGFIhnNTsDkW07R',
          scope: ['siteUser'],
        },
      });
    });

    test('valid token, unknown domain', async () => {
      const token =
        '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
      const now = new Date('2024-08-23T20:37:30.000Z').getTime();
      const result = service.validate('shiny-new-service.babycenter.com', token, now);
      expect(result).toStrictEqual({
        valid: true,
        user: {
          globalAuthId: '8aGFIhnNTsDkW07R',
          scope: ['siteUser'],
        },
      });
    });
  });
});
