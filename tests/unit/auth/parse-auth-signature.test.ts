import {
  SignatureType,
  parseAuthSignatureTypeValue,
} from '../../../src/auth/bc/resolve-bc-auth-signature';
import { ConfigData } from '../../../src/config/environment';

const ALLOWED_TYPES_PER_ENV: Record<ConfigData['envName'], SignatureType[]> = {
  dev: [SignatureType.FILE, SignatureType.TEXT, SignatureType.AWS_SECRET],
  qa: [SignatureType.FILE, SignatureType.TEXT, SignatureType.AWS_SECRET],
  stag: [SignatureType.AWS_SECRET],
  prod: [SignatureType.AWS_SECRET],
};

const NOT_ALLOWED_TYPES_PER_ENV: Record<ConfigData['envName'], SignatureType[]> = {
  dev: [],
  qa: [],
  stag: [SignatureType.FILE, SignatureType.TEXT],
  prod: [SignatureType.FILE, SignatureType.TEXT],
};

describe('parseAuthSignatureTypeValue', () => {
  for (const env of Object.keys(ALLOWED_TYPES_PER_ENV) as ConfigData['envName'][]) {
    const allowedTypes = ALLOWED_TYPES_PER_ENV[env];
    for (const allowedType of allowedTypes) {
      it(`parses ${allowedType} type for ${env}`, () => {
        const expectedValue = 'foo:bar:baz:spam:eggs';
        const signature = `${allowedType}:${expectedValue}`;
        const { type, value } = parseAuthSignatureTypeValue(signature, env);
        expect(type).toEqual(allowedType);
        expect(value).toEqual(expectedValue);
      });
    }
  }

  for (const env of Object.keys(NOT_ALLOWED_TYPES_PER_ENV) as ConfigData['envName'][]) {
    const notAllowedTypes = NOT_ALLOWED_TYPES_PER_ENV[env];
    for (const notAllowedType of notAllowedTypes) {
      it(`throws when parsing ${notAllowedType} type for ${env}`, () => {
        const value = 'foo:bar:baz:spam:eggs';
        const signature = `${notAllowedType}:${value}`;
        let thrownException = false;
        try {
          parseAuthSignatureTypeValue(signature, env);
        } catch (e) {
          thrownException = true;
        }
        expect(thrownException).toBeTruthy();
      });
    }
  }
});
