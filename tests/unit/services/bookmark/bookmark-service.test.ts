import { Field<PERSON>acket, OkPacket, Pool, PoolConnection, RowDataPacket } from 'mysql2/promise';
import BookmarkService, {
  BookmarkNotFoundException,
  TooManyBookmarksException,
} from '../../../../src/services/bookmark/bookmark-service';
import ProfileEventService from '../../../../src/services/profile-event/profile-event-service';
import AppLogger from '../../../../src/logs/app-logger';
import BcUser from '../../../../src/auth/bc/bc-user';
import { BookmarkRow } from '../../../../src/data/data-bookmark';
import {
  BookmarkRequest,
  ListPaginatedBookmarkParams,
} from '../../../../src/handlers/bookmarks-handlers';

// Mock implementations
jest.mock('../../../../src/services/profile-event/profile-event-service');

describe('BookmarkService', () => {
  let bookmarkService: BookmarkService;
  let mockDbPool: jest.Mocked<Pool>;
  let mockLogger: jest.Mocked<AppLogger>;
  let mockProfileEventService: jest.Mocked<ProfileEventService>;

  const mockUser: BcUser = {
    globalAuthId: 'test-user-id',
    memberId: 123456,
    email: '<EMAIL>',
    scope: ['siteUser'],
  };

  const mockBookmarkRequest: BookmarkRequest = {
    memberId: 'test-user-id',
    contentSource: 'bcsite',
    contentType: 'article',
    contentId: '12345',
  };

  beforeEach(() => {
    // Mock the database pool and connection
    mockDbPool = {
      query: jest.fn(),
      getConnection: jest.fn().mockResolvedValue({
        beginTransaction: jest.fn().mockResolvedValue(undefined),
        commit: jest.fn().mockResolvedValue(undefined),
        rollback: jest.fn().mockResolvedValue(undefined),
        query: jest.fn(),
        release: jest.fn(),
      }),
    } as unknown as jest.Mocked<Pool>;

    // Mock the logger
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debugMeta: jest.fn(),
      infoMeta: jest.fn(),
      errorMeta: jest.fn(),
      warnMeta: jest.fn(),
    } as unknown as jest.Mocked<AppLogger>;

    // Mock the profile event service
    mockProfileEventService = new ProfileEventService(
      'test-arn',
      mockLogger,
    ) as jest.Mocked<ProfileEventService>;
    mockProfileEventService.sendBookmarkEvent = jest.fn().mockResolvedValue(true);
    mockProfileEventService.sendSubscriptionEvent = jest.fn().mockResolvedValue(true);

    // Initialize the bookmark service
    bookmarkService = new BookmarkService(mockDbPool, mockProfileEventService, mockLogger);
  });

  afterEach(() => {
    // Clear all mocks and timers
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  afterAll(() => {
    // Force cleanup of any lingering handles
    jest.clearAllTimers();
    jest.restoreAllMocks();
  });

  describe('countBookmarks', () => {
    it('should return the count of bookmarks', async () => {
      // Mock the query result
      const mockQueryResult = [
        [
          {
            count: 5,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      mockDbPool.query.mockResolvedValueOnce(mockQueryResult);

      // Call the method and check the result
      const result = await bookmarkService.countBookmarks(mockBookmarkRequest);

      expect(result).toBe(5);
      expect(mockDbPool.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('saveBookmark', () => {
    it('should save a bookmark successfully', async () => {
      // Mock the count query to return 0 (no bookmarks yet)
      const mockCountResult = [
        [
          {
            count: 0,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      // Mock the insert query to return 1 affected row
      const mockInsertResult = [
        {
          affectedRows: 1,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      // Set up the mocks
      mockDbPool.query
        .mockResolvedValueOnce(mockCountResult)
        .mockResolvedValueOnce(mockInsertResult);

      // Call the method
      await bookmarkService.saveBookmark(
        {
          ...mockBookmarkRequest,
          createDate: null,
        },
        mockUser,
      );

      // Verify the events were sent
      expect(mockProfileEventService.sendBookmarkEvent).toHaveBeenCalledWith(
        mockUser,
        {
          ...mockBookmarkRequest,
          createDate: null,
        },
        'create',
      );

      // Verify the query was called twice (count + insert)
      expect(mockDbPool.query).toHaveBeenCalledTimes(2);
    });

    it('should throw TooManyBookmarksException when the limit is reached', async () => {
      // Mock the count query to return maximum allowed bookmarks
      const mockCountResult = [
        [
          {
            count: BookmarkService.BOOKMARK_LIMIT_BY_CONTENT_SOURCE_AND_TYPE,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      // Set up the mock
      mockDbPool.query.mockResolvedValueOnce(mockCountResult);

      // Call the method and expect it to throw
      await expect(
        bookmarkService.saveBookmark(
          {
            ...mockBookmarkRequest,
            createDate: null,
          },
          mockUser,
        ),
      ).rejects.toThrow(TooManyBookmarksException);

      // Verify the event was not sent
      expect(mockProfileEventService.sendBookmarkEvent).not.toHaveBeenCalled();
    });

    it('should send community post subscription event for community content', async () => {
      // Mock the count query to return 0 (no bookmarks yet)
      const mockCountResult = [
        [
          {
            count: 0,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      // Mock the insert query to return 1 affected row
      const mockInsertResult = [
        {
          affectedRows: 1,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      // Set up the mocks
      mockDbPool.query
        .mockResolvedValueOnce(mockCountResult)
        .mockResolvedValueOnce(mockInsertResult);

      // Create a community content bookmark
      const communityBookmark = {
        ...mockBookmarkRequest,
        contentSource: 'community:en_US',
        contentType: 'post',
        createDate: null,
      };

      // Call the method
      await bookmarkService.saveBookmark(communityBookmark, mockUser);

      // Verify both events were sent
      expect(mockProfileEventService.sendBookmarkEvent).toHaveBeenCalledWith(
        mockUser,
        expect.objectContaining({
          contentSource: 'community:en_US',
          contentType: 'post',
        }),
        'create',
      );

      expect(mockProfileEventService.sendSubscriptionEvent).toHaveBeenCalledWith(
        mockUser,
        expect.objectContaining({
          contentSource: 'community:en_US',
          contentType: 'post',
        }),
        true,
      );
    });

    it('should throw an error if the save failed', async () => {
      // Mock the count query to return 0 (no bookmarks yet)
      const mockCountResult = [
        [
          {
            count: 0,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      // Mock the insert query to return 0 affected rows (failed)
      const mockInsertResult = [
        {
          affectedRows: 0,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      // Set up the mocks
      mockDbPool.query
        .mockResolvedValueOnce(mockCountResult)
        .mockResolvedValueOnce(mockInsertResult);

      // Call the method and expect it to throw
      await expect(
        bookmarkService.saveBookmark(
          {
            ...mockBookmarkRequest,
            createDate: null,
          },
          mockUser,
        ),
      ).rejects.toThrow('Bookmark not saved');
    });
  });

  describe('deleteBookmark', () => {
    it('should delete a bookmark successfully', async () => {
      // Mock the delete query to return 1 affected row
      const mockDeleteResult = [
        {
          affectedRows: 1,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      // Set up the mock
      mockDbPool.query.mockResolvedValueOnce(mockDeleteResult);

      // Call the method
      await bookmarkService.deleteBookmark(mockBookmarkRequest, mockUser);

      // Verify the event was sent
      expect(mockProfileEventService.sendBookmarkEvent).toHaveBeenCalledWith(
        mockUser,
        mockBookmarkRequest,
        'delete',
      );

      // Verify the query was called
      expect(mockDbPool.query).toHaveBeenCalledTimes(1);
    });

    it('should throw BookmarkNotFoundException when the bookmark does not exist', async () => {
      // Mock the delete query to return 0 affected rows (not found)
      const mockDeleteResult = [
        {
          affectedRows: 0,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      // Set up the mock
      mockDbPool.query.mockResolvedValueOnce(mockDeleteResult);

      // Call the method and expect it to throw
      await expect(bookmarkService.deleteBookmark(mockBookmarkRequest, mockUser)).rejects.toThrow(
        BookmarkNotFoundException,
      );

      // Verify the event was not sent
      expect(mockProfileEventService.sendBookmarkEvent).not.toHaveBeenCalled();
    });

    it('should send community post subscription event for community content', async () => {
      // Mock the delete query to return 1 affected row
      const mockDeleteResult = [
        {
          affectedRows: 1,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      // Set up the mock
      mockDbPool.query.mockResolvedValueOnce(mockDeleteResult);

      // Create a community content bookmark
      const communityBookmark = {
        ...mockBookmarkRequest,
        contentSource: 'community:en_US',
        contentType: 'post',
      };

      // Call the method
      await bookmarkService.deleteBookmark(communityBookmark, mockUser);

      // Verify both events were sent
      expect(mockProfileEventService.sendBookmarkEvent).toHaveBeenCalledWith(
        mockUser,
        expect.objectContaining({
          contentSource: 'community:en_US',
          contentType: 'post',
        }),
        'delete',
      );

      expect(mockProfileEventService.sendSubscriptionEvent).toHaveBeenCalledWith(
        mockUser,
        expect.objectContaining({
          contentSource: 'community:en_US',
          contentType: 'post',
        }),
        false,
      );
    });
  });

  describe('batchDeleteBookmarks', () => {
    it('should delete multiple bookmarks successfully', async () => {
      const contentIds = ['101', '102', '103'];

      // Mock the direct query on pool (this method doesn't use transactions)
      const mockDeleteResult = [
        {
          affectedRows: contentIds.length,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      mockDbPool.query.mockResolvedValueOnce(mockDeleteResult);

      // Call the method
      const result = await bookmarkService.batchDeleteBookmarks(
        mockBookmarkRequest.memberId,
        mockBookmarkRequest.contentSource,
        mockBookmarkRequest.contentType,
        contentIds,
        mockUser,
      );

      // Verify the result
      expect(result.deletedCount).toBe(contentIds.length);
      expect(result.errors).toHaveLength(0);

      // Verify events were sent (one per content ID)
      expect(mockProfileEventService.sendBookmarkEvent).toHaveBeenCalledTimes(contentIds.length);
    });

    it('should return empty result for empty content IDs array', async () => {
      // Call the method with empty array
      const result = await bookmarkService.batchDeleteBookmarks(
        mockBookmarkRequest.memberId,
        mockBookmarkRequest.contentSource,
        mockBookmarkRequest.contentType,
        [],
        mockUser,
      );

      // Verify the result
      expect(result.deletedCount).toBe(0);
      expect(result.errors).toHaveLength(0);

      // Verify no database calls were made
      expect(mockDbPool.getConnection).not.toHaveBeenCalled();
    });

    it('should handle database error correctly', async () => {
      const contentIds = ['101', '102', '103'];
      const mockError = new Error('Database error');

      // Mock the direct query to fail
      mockDbPool.query.mockRejectedValueOnce(mockError);

      // Call the method and expect it to throw
      await expect(
        bookmarkService.batchDeleteBookmarks(
          mockBookmarkRequest.memberId,
          mockBookmarkRequest.contentSource,
          mockBookmarkRequest.contentType,
          contentIds,
          mockUser,
        ),
      ).rejects.toThrow(mockError);

      // Verify no events were sent
      expect(mockProfileEventService.sendBookmarkEvent).not.toHaveBeenCalled();
    });
  });

  describe('listBookmarks', () => {
    it('should return null when no bookmarks are found', async () => {
      // Mock the query result with empty array
      const mockQueryResult = [[]] as unknown as [BookmarkRow[], FieldPacket[]];

      mockDbPool.query.mockResolvedValueOnce(mockQueryResult);

      // Call the method
      const result = await bookmarkService.listBookmarks(mockBookmarkRequest);

      // Verify the result
      expect(result).toBeNull();
      expect(mockDbPool.query).toHaveBeenCalledTimes(1);
    });

    it('should return bookmarks when found', async () => {
      // Mock the query result with data
      const mockBookmarks = [
        {
          id: 1,
          member_id: mockBookmarkRequest.memberId,
          content_source: mockBookmarkRequest.contentSource,
          content_type: mockBookmarkRequest.contentType,
          content_id: mockBookmarkRequest.contentId,
          create_date: new Date(),
          update_date: new Date(),
          create_user: 'system',
          update_user: 'system',
        },
        {
          id: 2,
          member_id: mockBookmarkRequest.memberId,
          content_source: mockBookmarkRequest.contentSource,
          content_type: mockBookmarkRequest.contentType,
          content_id: '67890',
          create_date: new Date(),
          update_date: new Date(),
          create_user: 'system',
          update_user: 'system',
        },
      ] as BookmarkRow[];

      const mockQueryResult = [mockBookmarks] as unknown as [BookmarkRow[], FieldPacket[]];

      mockDbPool.query.mockResolvedValueOnce(mockQueryResult);

      // Call the method
      const result = await bookmarkService.listBookmarks(mockBookmarkRequest);

      // Verify the result
      expect(result).toHaveLength(2);
      expect(result?.[0].content_id).toBe(mockBookmarkRequest.contentId);
      expect(result?.[1].content_id).toBe('67890');
      expect(mockDbPool.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('listBookmarksPaginated', () => {
    it('should return paginated bookmarks', async () => {
      const pageParams: ListPaginatedBookmarkParams = {
        pageIndex: 0,
        pageSize: 10,
      };

      // Mock the count query
      const mockCountResult = [
        [
          {
            count: 25,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      // Mock the bookmarks query
      const mockBookmarks = [
        {
          id: 1,
          member_id: mockBookmarkRequest.memberId,
          content_source: mockBookmarkRequest.contentSource,
          content_type: mockBookmarkRequest.contentType,
          content_id: mockBookmarkRequest.contentId,
          create_date: new Date(),
          update_date: new Date(),
          create_user: 'system',
          update_user: 'system',
        },
        {
          id: 2,
          member_id: mockBookmarkRequest.memberId,
          content_source: mockBookmarkRequest.contentSource,
          content_type: mockBookmarkRequest.contentType,
          content_id: '67890',
          create_date: new Date(),
          update_date: new Date(),
          create_user: 'system',
          update_user: 'system',
        },
      ] as BookmarkRow[];

      const mockBookmarksResult = [mockBookmarks] as unknown as [BookmarkRow[], FieldPacket[]];

      // Set up the mocks
      mockDbPool.query
        .mockResolvedValueOnce(mockCountResult)
        .mockResolvedValueOnce(mockBookmarksResult);

      // Call the method
      const result = await bookmarkService.listBookmarksPaginated(mockBookmarkRequest, pageParams);

      // Verify the result
      expect(result.totalItems).toBe(25);
      expect(result.pageIndex).toBe(pageParams.pageIndex);
      expect(result.pageSize).toBe(pageParams.pageSize);
      expect(result.items).toHaveLength(2);
      expect(mockDbPool.query).toHaveBeenCalledTimes(2);
    });

    it('should return empty result when no bookmarks are found', async () => {
      const pageParams: ListPaginatedBookmarkParams = {
        pageIndex: 0,
        pageSize: 10,
      };

      // Mock the count query to return 0
      const mockCountResult = [
        [
          {
            count: 0,
          },
        ],
      ] as unknown as [RowDataPacket[], FieldPacket[]];

      // Set up the mock
      mockDbPool.query.mockResolvedValueOnce(mockCountResult);

      // Call the method
      const result = await bookmarkService.listBookmarksPaginated(mockBookmarkRequest, pageParams);

      // Verify the result
      expect(result.totalItems).toBe(0);
      expect(result.items).toBeNull();
      expect(result.pageIndex).toBe(pageParams.pageIndex);
      expect(result.pageSize).toBe(pageParams.pageSize);
      expect(mockDbPool.query).toHaveBeenCalledTimes(1);
    });
  });

  describe('getBookmark', () => {
    it('should return a bookmark when found', async () => {
      const mockBookmarkRow = {
        id: 1,
        member_id: 'test-user-id',
        content_source: 'bcsite',
        content_type: 'article',
        content_id: '12345',
        create_date: new Date(),
        create_user: 'system',
        update_date: new Date(),
        update_user: 'system',
      } as BookmarkRow;

      const mockResult = [[mockBookmarkRow]] as unknown as [BookmarkRow[], FieldPacket[]];
      mockDbPool.query.mockResolvedValueOnce(mockResult);

      const result = await bookmarkService.getBookmark(mockBookmarkRequest);

      expect(result).toEqual(mockBookmarkRow);
      expect(mockDbPool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM bookmark'),
        ['test-user-id', 'bcsite', 'article', '12345'],
      );
    });

    it('should return null when bookmark not found', async () => {
      const mockResult = [[]] as unknown as [BookmarkRow[], FieldPacket[]];
      mockDbPool.query.mockResolvedValueOnce(mockResult);

      const result = await bookmarkService.getBookmark(mockBookmarkRequest);

      expect(result).toBeNull();
    });
  });

  describe('listAllBookmarksByMember', () => {
    it('should return all bookmarks for a member', async () => {
      const mockBookmarkRows = [
        {
          id: 1,
          member_id: 'test-user-id',
          content_source: 'bcsite',
          content_type: 'article',
          content_id: '12345',
          create_date: new Date(),
          create_user: 'system',
          update_date: new Date(),
          update_user: 'system',
        },
        {
          id: 2,
          member_id: 'test-user-id',
          content_source: 'community:en_US',
          content_type: 'post',
          content_id: '67890',
          create_date: new Date(),
          create_user: 'system',
          update_date: new Date(),
          update_user: 'system',
        },
      ] as BookmarkRow[];

      const mockResult = [mockBookmarkRows] as unknown as [BookmarkRow[], FieldPacket[]];
      mockDbPool.query.mockResolvedValueOnce(mockResult);

      const result = await bookmarkService.listAllBookmarksByMember('test-user-id');

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        content_source: 'bcsite',
        content_type: 'article',
        content_id: '12345',
      });
      expect(result[1]).toMatchObject({
        content_source: 'community:en_US',
        content_type: 'post',
        content_id: '67890',
      });
    });
  });

  describe('listBookmarksByMemberAndSource', () => {
    it('should return bookmarks for a member and content source', async () => {
      const mockBookmarkRows = [
        {
          id: 1,
          member_id: 'test-user-id',
          content_source: 'bcsite',
          content_type: 'article',
          content_id: '12345',
          create_date: new Date(),
          create_user: 'system',
          update_date: new Date(),
          update_user: 'system',
        },
      ] as BookmarkRow[];

      const mockResult = [mockBookmarkRows] as unknown as [BookmarkRow[], FieldPacket[]];
      mockDbPool.query.mockResolvedValueOnce(mockResult);

      const result = await bookmarkService.listBookmarksByMemberAndSource('test-user-id', 'bcsite');

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        content_source: 'bcsite',
        content_type: 'article',
        content_id: '12345',
      });
    });
  });

  describe('batchSaveBookmarks', () => {
    it('should save multiple bookmarks successfully', async () => {
      const bookmarks = [
        {
          memberId: 'test-user-id',
          contentSource: 'bcsite',
          contentType: 'article',
          contentId: '1',
          createDate: null,
        },
        {
          memberId: 'test-user-id',
          contentSource: 'bcsite',
          contentType: 'article',
          contentId: '2',
          createDate: null,
        },
      ];

      // Mock count queries for each content source/type combination
      const mockCountResult = [[{ count: 5 }]] as unknown as [RowDataPacket[], FieldPacket[]];
      mockDbPool.query.mockResolvedValueOnce(mockCountResult);

      // Mock the connection and transaction
      const mockConnection = {
        beginTransaction: jest.fn().mockResolvedValue(undefined),
        commit: jest.fn().mockResolvedValue(undefined),
        rollback: jest.fn().mockResolvedValue(undefined),
        query: jest.fn().mockResolvedValue([{ affectedRows: 2 }]),
        release: jest.fn(),
      } as unknown as jest.Mocked<PoolConnection>;

      mockDbPool.getConnection.mockResolvedValueOnce(mockConnection);

      const result = await bookmarkService.batchSaveBookmarks('test-user-id', bookmarks, mockUser);

      expect(result.savedCount).toBe(2);
      expect(result.errors).toHaveLength(0);
      expect(mockConnection.beginTransaction).toHaveBeenCalled();
      expect(mockConnection.commit).toHaveBeenCalled();
      expect(mockConnection.release).toHaveBeenCalled();
    });

    it('should throw TooManyBookmarksException when limit exceeded', async () => {
      const bookmarks = [
        {
          memberId: 'test-user-id',
          contentSource: 'bcsite',
          contentType: 'article',
          contentId: '1',
          createDate: null,
        },
      ];

      // Mock count query to return exactly the limit (200)
      // Adding 1 more will exceed the limit
      const mockCountResult = [[{ count: 200 }]] as unknown as [RowDataPacket[], FieldPacket[]];
      mockDbPool.query.mockResolvedValueOnce(mockCountResult);

      await expect(
        bookmarkService.batchSaveBookmarks('test-user-id', bookmarks, mockUser),
      ).rejects.toThrow(TooManyBookmarksException);
    });

    it('should return errors for mismatched member IDs', async () => {
      const bookmarks = [
        {
          memberId: 'wrong-user-id',
          contentSource: 'bcsite',
          contentType: 'article',
          contentId: '1',
          createDate: null,
        },
      ];

      const result = await bookmarkService.batchSaveBookmarks('test-user-id', bookmarks, mockUser);

      expect(result.savedCount).toBe(0);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('does not match');
    });
  });

  describe('batchDeleteBookmarks at bottom', () => {
    it('should delete multiple bookmarks successfully', async () => {
      const contentIds = ['1', '2', '3'];

      // Mock the direct query on pool
      const mockDeleteResult = [
        {
          affectedRows: 3,
        },
      ] as unknown as [OkPacket, FieldPacket[]];

      mockDbPool.query.mockResolvedValueOnce(mockDeleteResult);

      const result = await bookmarkService.batchDeleteBookmarks(
        'test-user-id',
        'bcsite',
        'article',
        contentIds,
        mockUser,
      );

      expect(result.deletedCount).toBe(3);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle empty content IDs array', async () => {
      const result = await bookmarkService.batchDeleteBookmarks(
        'test-user-id',
        'bcsite',
        'article',
        [],
        mockUser,
      );

      expect(result.deletedCount).toBe(0);
      expect(result.errors).toHaveLength(0);
    });
  });
});
