import NotificationService, {
  NotificationNotFoundException,
  TooManyNotificationsException,
} from '../../../../src/services/notification/notification-service';
import AppLogger from '../../../../src/logs/app-logger';
import { DynamoDBConfig } from '../../../../src/config/environment';
import { NotificationBackend } from '../../../../src/services/notification/notification-backend-interface';

// Mock the DynamoDB backend
jest.mock('../../../../src/services/notification/notification-dynamodb-backend');

describe('NotificationService', () => {
  let notificationService: NotificationService;
  let mockDynamoConfig: DynamoDBConfig;
  let mockLogger: jest.Mocked<AppLogger>;

  beforeEach(() => {
    mockDynamoConfig = {
      region: 'us-east-1',
      tableName: 'test-notifications',
      ttlDays: 7,
      endpoint: 'http://localhost:8000',
    };

    mockLogger = {
      newMeta: jest.fn(),
      debug: jest.fn(),
      info: jest.fn(),
      infoMeta: jest.fn(),
      warn: jest.fn(),
      warnMeta: jest.fn(),
      error: jest.fn(),
      errorMeta: jest.fn(),
      debugMeta: jest.fn(),
    } as unknown as jest.Mocked<AppLogger>;

    notificationService = new NotificationService(mockDynamoConfig, mockLogger);
  });

  afterEach(() => {
    // Clear all mocks and timers
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  afterAll(() => {
    // Force cleanup of any lingering handles
    jest.clearAllTimers();
    jest.restoreAllMocks();
  });

  describe('createNotification', () => {
    it('should create a notification successfully', async () => {
      // Mock the backend's createNotification and getNotification methods
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.createNotification = jest.fn().mockResolvedValueOnce(true);

      const mockNotification = {
        userKey: { countryCode: 'US', userId: 'user123' },
        contentActionKey: { action: 'COMMENT', contentId: 'content456' },
        postId: 789,
        snippet: 'Test comment',
        count: 1,
        createDateTime: new Date(),
      };
      mockBackend.getNotification = jest.fn().mockResolvedValueOnce(mockNotification);

      const result = await notificationService.createNotification(
        'US',
        'user123',
        'content456',
        'COMMENT',
        {
          postId: 789,
          snippet: 'Test comment',
        },
      );

      expect(result).toMatchObject(mockNotification);
      expect(mockBackend.createNotification).toHaveBeenCalledWith(
        'US',
        'user123',
        'content456',
        'COMMENT',
        {
          postId: 789,
          snippet: 'Test comment',
        },
      );
      expect(mockBackend.getNotification).toHaveBeenCalledWith(
        'US',
        'user123',
        'content456',
        'COMMENT',
      );
    });

    it('should throw TooManyNotificationsException when user has too many notifications', async () => {
      // Mock the backend's createNotification method to return false (limit reached)
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.createNotification = jest.fn().mockResolvedValueOnce(false);

      await expect(
        notificationService.createNotification('US', 'user123', 'content456', 'COMMENT'),
      ).rejects.toThrow(TooManyNotificationsException);

      expect(mockBackend.createNotification).toHaveBeenCalledWith(
        'US',
        'user123',
        'content456',
        'COMMENT',
        undefined,
      );
    });
  });

  describe('getNotification', () => {
    it('should return notification when found', async () => {
      const mockNotification = {
        userKey: { countryCode: 'US', userId: 'user123' },
        contentActionKey: {
          action: 'COMMENT',
          contentId: 'content456',
        },
        postId: 789,
        snippet: 'Test comment',
        count: 1,
      };

      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.getNotification = jest.fn().mockResolvedValueOnce(mockNotification);

      const result = await notificationService.getNotification(
        'US',
        'user123',
        'content456',
        'COMMENT',
      );

      expect(result).toMatchObject({
        userKey: { countryCode: 'US', userId: 'user123' },
        contentActionKey: {
          action: 'COMMENT',
          contentId: 'content456',
        },
        postId: 789,
        snippet: 'Test comment',
        count: 1,
      });
    });

    it('should throw NotificationNotFoundException when notification not found', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.getNotification = jest.fn().mockResolvedValueOnce(null);

      await expect(
        notificationService.getNotification('US', 'user123', 'content456', 'COMMENT'),
      ).rejects.toThrow(NotificationNotFoundException);
    });
  });

  describe('updateNotification', () => {
    it('should update notification successfully', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.updateNotification = jest.fn().mockResolvedValueOnce(true);

      const mockUpdatedNotification = {
        userKey: { countryCode: 'US', userId: 'user123' },
        contentActionKey: { action: 'COMMENT', contentId: 'content456' },
        postId: 789,
        snippet: 'Updated snippet',
        count: 0, // marked as read
        createDateTime: new Date(),
      };
      mockBackend.getNotification = jest.fn().mockResolvedValueOnce(mockUpdatedNotification);

      const result = await notificationService.updateNotification(
        'US',
        'user123',
        'content456',
        'COMMENT',
        { markAsRead: true, snippet: 'Updated snippet' },
      );

      expect(result).toMatchObject(mockUpdatedNotification);
      expect(mockBackend.updateNotification).toHaveBeenCalledWith(
        'US',
        'user123',
        'content456',
        'COMMENT',
        { markAsRead: true, snippet: 'Updated snippet' },
      );
      expect(mockBackend.getNotification).toHaveBeenCalledWith(
        'US',
        'user123',
        'content456',
        'COMMENT',
      );
    });

    it('should throw NotificationNotFoundException when notification not found', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.updateNotification = jest.fn().mockResolvedValueOnce(false);

      await expect(
        notificationService.updateNotification('US', 'user123', 'content456', 'COMMENT', {
          markAsRead: true,
        }),
      ).rejects.toThrow(NotificationNotFoundException);
    });
  });

  describe('deleteNotification', () => {
    it('should delete notification successfully', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.deleteNotification = jest.fn().mockResolvedValueOnce(true);

      const result = await notificationService.deleteNotification(
        'US',
        'user123',
        'content456',
        'COMMENT',
      );

      expect(result).toBe(true);
      expect(mockBackend.deleteNotification).toHaveBeenCalledWith(
        'US',
        'user123',
        'content456',
        'COMMENT',
      );
    });

    it('should delete notification when not found (no exception)', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.deleteNotification = jest.fn().mockResolvedValueOnce(false);

      const result = await notificationService.deleteNotification(
        'US',
        'user123',
        'content456',
        'COMMENT',
      );

      expect(result).toBe(false);
    });
  });

  describe('listUserNotifications', () => {
    it('should return paginated notifications', async () => {
      const mockResult = {
        notifications: [
          {
            userKey: { countryCode: 'US', userId: 'user123' },
            contentActionKey: { action: 'COMMENT', contentId: 'content456' },
            postId: 789,
            snippet: 'Test comment 1',
            count: 1,
          },
          {
            userKey: { countryCode: 'US', userId: 'user123' },
            contentActionKey: { action: 'REPLY', contentId: 'content789' },
            postId: 789,
            replyId: 101,
            snippet: 'Test reply',
            count: 2,
          },
        ],
        lastEvaluatedKey: 'some-key',
        unreadCount: 5,
      };

      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.listUserNotifications = jest.fn().mockResolvedValueOnce(mockResult);

      const result = await notificationService.listUserNotifications('US', 'user123', {
        pageSize: 20,
      });

      expect(result.notifications).toHaveLength(2);
      expect(result.unreadCount).toBe(5);
      expect(result.lastEvaluatedKey).toBe('some-key');
      expect(mockBackend.listUserNotifications).toHaveBeenCalledWith('US', 'user123', {
        pageSize: 20,
      });
    });

    it('should return empty result when no notifications', async () => {
      const mockResult = {
        notifications: [],
        unreadCount: 0,
      };

      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.listUserNotifications = jest.fn().mockResolvedValueOnce(mockResult);

      const result = await notificationService.listUserNotifications('US', 'user123');

      expect(result.notifications).toHaveLength(0);
      expect(result.unreadCount).toBe(0);
    });
  });

  describe('readAllNotifications', () => {
    it('should mark all notifications as read', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.markNotificationsAsRead = jest.fn().mockResolvedValueOnce(3);

      const result = await notificationService.readAllNotifications('US', 'user123');

      expect(result).toBe(3);
      expect(mockBackend.markNotificationsAsRead).toHaveBeenCalledWith('US', 'user123');
    });
  });

  describe('cleanupExpiredNotifications', () => {
    it('should delete expired notifications', async () => {
      const mockBackend = notificationService[
        'backend'
      ] as unknown as jest.Mocked<NotificationBackend>;
      mockBackend.cleanupExpiredNotifications = jest.fn().mockResolvedValueOnce(10);

      const result = await notificationService.cleanupExpiredNotifications();

      expect(result).toBe(10);
      expect(mockBackend.cleanupExpiredNotifications).toHaveBeenCalled();
    });
  });

  describe('parseUserKey', () => {
    it('should parse base64 encoded user key', () => {
      const encoded = Buffer.from('US,user123').toString('base64');
      const result = NotificationService.parseUserKey(encoded);
      expect(result).toEqual({
        countryCode: 'US',
        userId: 'user123',
      });
    });

    it('should throw error for invalid user key format', () => {
      expect(() => NotificationService.parseUserKey('invalid')).toThrow('Invalid user key format');
    });
  });

  describe('parseGlobalAuthId', () => {
    it('should parse global auth ID', () => {
      const result = NotificationService.parseGlobalAuthId('global123');
      expect(result).toEqual({
        countryCode: 'US',
        userId: 'global123',
      });
    });
  });
});
