import {
  encodeUser<PERSON><PERSON>,
  decodeU<PERSON><PERSON><PERSON>,
  isValidUser<PERSON>ey,
  encodeUser<PERSON>eyForUrl,
  decodeUserKeyFromUrl,
} from '../../../src/utils/user-key-encoding';

describe('User Key Encoding', () => {
  describe('encodeUserKey', () => {
    it('should encode user key correctly', () => {
      expect(encodeUserKey('US', '12345')).toBe('US,12345');
      expect(encodeUserKey('GB', 'abc123')).toBe('GB,abc123');
    });
  });

  describe('decodeUserKey', () => {
    it('should decode valid user key correctly', () => {
      expect(decodeUserKey('VVMsMTIzNDU=')).toEqual({
        countryCode: 'US',
        userId: '12345',
      });
      expect(decodeUserKey('R0IsYWJjMTIz')).toEqual({
        countryCode: 'GB',
        userId: 'abc123',
      });
    });

    it('should handle whitespace correctly', () => {
      expect(decodeUserKey(' VVMsMTIzNDU= ')).toEqual({
        countryCode: 'US',
        userId: '12345',
      });
    });

    it('should throw error for invalid format', () => {
      expect(() => decodeUserKey('invalid')).toThrow('Invalid user key format');
      expect(() => decodeUserKey('VVMsMTIzNDUsZXh0cmE=')).toThrow('Invalid user key format');
      expect(() => decodeUserKey('')).toThrow('Invalid user key: must be a non-empty string');
      expect(() => decodeUserKey('VVMs')).toThrow(
        'Invalid user key: country code and user ID must be non-empty',
      );
      expect(() => decodeUserKey('LDEyMzQ1')).toThrow(
        'Invalid user key: country code and user ID must be non-empty',
      );
    });
  });

  describe('isValidUserKey', () => {
    it('should return true for valid user keys', () => {
      expect(isValidUserKey('VVMsMTIzNDU=')).toBe(true);
      expect(isValidUserKey('R0IsYWJjMTIz')).toBe(true);
    });

    it('should return false for invalid user keys', () => {
      expect(isValidUserKey('invalid')).toBe(false);
      expect(isValidUserKey('')).toBe(false);
      expect(isValidUserKey('US,')).toBe(false);
      expect(isValidUserKey(',12345')).toBe(false);
    });
  });

  describe('encodeUserKeyForUrl', () => {
    it('should encode user key for URL correctly', () => {
      const encoded = encodeUserKeyForUrl('US', '12345');
      expect(encoded).toBe(Buffer.from('US,12345').toString('base64'));
    });
  });

  describe('decodeUserKeyFromUrl', () => {
    it('should decode URL encoded user key correctly', () => {
      const encoded = Buffer.from('VVMsMTIzNDU=').toString('base64');
      expect(decodeUserKeyFromUrl(encoded)).toEqual({
        countryCode: 'US',
        userId: '12345',
      });
    });

    it('should throw error for invalid base64', () => {
      expect(() => decodeUserKeyFromUrl('invalid-base64')).toThrow('Invalid encoded user key');
    });

    it('should throw error for invalid user key after decoding', () => {
      const invalidEncoded = Buffer.from('invalid-format').toString('base64');
      expect(() => decodeUserKeyFromUrl(invalidEncoded)).toThrow('Invalid encoded user key');
    });
  });
});
