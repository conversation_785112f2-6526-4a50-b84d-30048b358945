#!/usr/bin/env ts-node
/**
 * Local testing script for Lambda functions
 * Run with: npx ts-node tests/lambda/test-lambdas-locally.ts
 */

// Update the import path if the file is located elsewhere, for example:
import { handler as communityHandler } from '../../src/lambda/community-events-handler';
// Or, if the file does not exist, create '../../src/lambda/communityEventsHandler.ts' and export a handler function from it.
import { handler as subscriptionHandler } from '../../src/lambda/subscription-events-handler';
import { SQSEvent, Context } from 'aws-lambda';

// Mock environment variables
process.env.DYNAMO_GLOBAL_SUBSCRIPTION_TABLE = 'GlobalSubscription';
process.env.DYNAMO_NOTIFICATION_TABLE = 'Notification';
process.env.DYNAMO_MEMBER_ACTIVITY_TABLE = 'MemberActivityStatus';
process.env.DYNAMO_SUBSCRIPTION_TABLE = 'Subscription';
process.env.NOTIFICATION_LIFETIME_DAYS = '7';

// Mock AWS region
process.env.AWS_REGION = 'us-east-1';

// Mock AWS credentials (won't be used as DynamoDB is mocked)
process.env.AWS_ACCESS_KEY_ID = 'mock';
process.env.AWS_SECRET_ACCESS_KEY = 'mock';

const createMockContext = (): Context => ({
  awsRequestId: 'test-request-id',
  functionName: 'test-function',
  functionVersion: '1',
  invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:test',
  memoryLimitInMB: '256',
  getRemainingTimeInMillis: () => 30000,
  logGroupName: '/aws/lambda/test',
  logStreamName: '2025/07/15/test-stream',
  callbackWaitsForEmptyEventLoop: false,
  done: (): void => { },
  fail: (): void => { },
  succeed: (): void => { },
});

const createSQSEvent = (messageBody: Record<string, unknown>): SQSEvent => ({
  Records: [
    {
      messageId: 'test-message-id',
      receiptHandle: 'test-receipt-handle',
      body: JSON.stringify(messageBody),
      attributes: {
        ApproximateReceiveCount: '1',
        SentTimestamp: Date.now().toString(),
        SenderId: 'test-sender',
        ApproximateFirstReceiveTimestamp: Date.now().toString(),
      },
      messageAttributes: {},
      md5OfBody: 'test-md5',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-1:123456789012:test-queue',
      awsRegion: 'us-east-1',
    },
  ],
});

async function testCommunityEvents(): Promise<void> {
  console.log('Testing Community Events Lambda...\n');

  const testMessages = [
    {
      name: 'Comment Event',
      data: {
        eventType: 'comment',
        countryCode: 'en_US',
        contentId: '12345',
        globalMemberId: 'user123',
        snippet: 'This is a test comment',
        genericContent: {
          globalUserId: 'commenter456',
          postId: 789,
          message: 'This is a test comment on a post',
        },
      },
    },
    {
      name: 'Reply Event',
      data: {
        eventType: 'reply',
        countryCode: 'en_US',
        contentId: '12345',
        globalMemberId: 'replier123',
        snippet: 'This is a test reply',
        genericContent: {
          globalUserId: 'original-commenter456',
          postId: 789,
          replyId: 999,
          message: 'This is a reply to a comment',
        },
      },
    },
    {
      name: 'Reaction Event',
      data: {
        eventType: 'react_on_post',
        countryCode: 'en_US',
        contentId: '12345',
        globalMemberId: 'reactor123',
        snippet: '👍 liked your post',
        genericContent: {
          globalUserId: 'post-author456',
          postId: 789,
          reactionType: 'like',
        },
      },
    },
  ];

  for (const testCase of testMessages) {
    try {
      console.log(`Testing: ${testCase.name}`);
      const event = createSQSEvent(testCase.data);
      const context = createMockContext();

      await communityHandler(event, context);
      console.log(`✓ ${testCase.name} processed successfully\n`);
    } catch (error) {
      console.error(`✗ ${testCase.name} failed:`, error);
      console.log('');
    }
  }
}

async function testSubscriptionEvents(): Promise<void> {
  console.log('Testing Subscription Events Lambda...\n');

  const testMessages = [
    {
      name: 'Subscribe Event',
      data: {
        subscribed: true,
        globalMemberId: 'user123',
        subscriptionId: '12345',
        countryCode: 'en_US',
      },
    },
    {
      name: 'Unsubscribe Event',
      data: {
        subscribed: false,
        globalMemberId: 'user123',
        subscriptionId: '12345',
        countryCode: 'en_US',
      },
    },
  ];

  for (const testCase of testMessages) {
    try {
      console.log(`Testing: ${testCase.name}`);
      const event = createSQSEvent(testCase.data);
      const context = createMockContext();

      await subscriptionHandler(event, context);
      console.log(`✓ ${testCase.name} processed successfully\n`);
    } catch (error) {
      console.error(`✗ ${testCase.name} failed:`, error);
      console.log('');
    }
  }
}

async function testInvalidMessages(): Promise<void> {
  console.log('Testing Invalid Messages...\n');

  const invalidMessages = [
    {
      name: 'Missing Required Fields',
      data: {
        eventType: 'comment',
        // Missing countryCode, contentId, globalMemberId
      },
    },
    {
      name: 'Invalid Event Type',
      data: {
        eventType: 'invalid-type',
        countryCode: 'en_US',
        contentId: '12345',
        globalMemberId: 'user123',
      },
    },
  ];

  for (const testCase of invalidMessages) {
    try {
      console.log(`Testing: ${testCase.name}`);
      const event = createSQSEvent(testCase.data);
      const context = createMockContext();

      await communityHandler(event, context);
      console.log(`✓ ${testCase.name} handled gracefully\n`);
    } catch (error) {
      console.error(`✗ ${testCase.name} failed:`, error);
      console.log('');
    }
  }
}

async function main(): Promise<void> {
  console.log('Lambda Local Testing Suite\n');
  console.log('=====================================\n');

  try {
    await testCommunityEvents();
    await testSubscriptionEvents();
    await testInvalidMessages();

    console.log('All tests completed!');
  } catch (error) {
    console.error('Test suite failed:', error);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main();
}

export { testCommunityEvents, testSubscriptionEvents, testInvalidMessages };
