{"Records": [{"messageId": "unsubscribe-test-message-id", "receiptHandle": "test-receipt-handle", "body": "{\"subscribed\":false,\"globalMemberId\":\"user123\",\"subscriptionId\":\"12345\",\"countryCode\":\"en_US\"}", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1642678800000", "SenderId": "test-sender", "ApproximateFirstReceiveTimestamp": "1642678800000"}, "messageAttributes": {}, "md5OfBody": "test-md5", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:bc-subscription-notification-svc-qa", "awsRegion": "us-east-1"}]}