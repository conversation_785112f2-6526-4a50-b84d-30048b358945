{"Records": [{"messageId": "reply-test-message-id", "receiptHandle": "test-receipt-handle", "body": "{\"eventType\":\"reply\",\"countryCode\":\"en_US\",\"contentId\":\"12345\",\"globalMemberId\":\"replier123\",\"snippet\":\"This is a test reply\",\"genericContent\":{\"globalUserId\":\"original-commenter456\",\"postId\":789,\"replyId\":999,\"message\":\"This is a reply to a comment\"}}", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1642678800000", "SenderId": "test-sender", "ApproximateFirstReceiveTimestamp": "1642678800000"}, "messageAttributes": {}, "md5OfBody": "test-md5", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:bc-community-notification-svc-qa", "awsRegion": "us-east-1"}]}