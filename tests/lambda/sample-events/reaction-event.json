{"Records": [{"messageId": "reaction-test-message-id", "receiptHandle": "test-receipt-handle", "body": "{\"eventType\":\"react_on_post\",\"countryCode\":\"en_US\",\"contentId\":\"12345\",\"globalMemberId\":\"reactor123\",\"snippet\":\"👍 liked your post\",\"genericContent\":{\"globalUserId\":\"post-author456\",\"postId\":789,\"reactionType\":\"like\"}}", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1642678800000", "SenderId": "test-sender", "ApproximateFirstReceiveTimestamp": "1642678800000"}, "messageAttributes": {}, "md5OfBody": "test-md5", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:bc-community-notification-svc-qa", "awsRegion": "us-east-1"}]}