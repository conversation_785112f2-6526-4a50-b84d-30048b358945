{"Records": [{"messageId": "comment-test-message-id", "receiptHandle": "test-receipt-handle", "body": "{\"eventType\":\"comment\",\"countryCode\":\"en_US\",\"contentId\":\"12345\",\"globalMemberId\":\"user123\",\"snippet\":\"This is a test comment\",\"genericContent\":{\"globalUserId\":\"commenter456\",\"postId\":789,\"message\":\"This is a test comment on a post\"}}", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1642678800000", "SenderId": "test-sender", "ApproximateFirstReceiveTimestamp": "1642678800000"}, "messageAttributes": {}, "md5OfBody": "test-md5", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:bc-community-notification-svc-qa", "awsRegion": "us-east-1"}]}