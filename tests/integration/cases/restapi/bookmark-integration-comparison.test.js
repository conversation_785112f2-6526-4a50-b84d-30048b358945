import axios from 'axios';
import { expect } from 'chai';
import Config from '../../config/index.js';

// Test both environments to compare behavior
const TEST_ENV = process.env.TEST_ENV || 'stag'; // Default to stag for comparison
const API_URL = Config.getApiUrl('bookmark', TEST_ENV);
const MEMBER_ID = Config.getMemberId(TEST_ENV);

console.log(`Running tests against ${TEST_ENV} environment`);
console.log(`API URL: ${API_URL}`);
console.log(`Member ID: ${MEMBER_ID}`);

/**
 * @group integration-comparison
 * Comparison test to identify differences between local and staging environments
 */
describe(`Bookmark REST API Integration - ${TEST_ENV.toUpperCase()} Environment`, () => {
  let jwt;

  before(async function () {
    this.timeout(10000); // Increase timeout for token generation
    try {
      jwt = await Config.getToken({}, TEST_ENV);
      console.log(`JWT token obtained for ${TEST_ENV}`);
      console.log(`Token length: ${jwt.length}`);
      console.log(`Token starts with: ${jwt.substring(0, 20)}...`);
    } catch (error) {
      console.error(`Failed to get token for ${TEST_ENV}:`, error.message);
      throw error;
    }
  });

  describe('Environment Connectivity Tests', () => {
    it('should connect to the API endpoint', async function () {
      this.timeout(15000);
      try {
        // Simple health check - try to get bookmarks (expect 401 without auth)
        const response = await axios.get(
          `${API_URL}/bookmark/member/${MEMBER_ID}`,
          { validateStatus: () => true }, // Accept any status code
        );

        console.log(`API Response Status: ${response.status}`);
        console.log(`API Response Headers:`, response.headers);

        // We expect 401 without auth, which means the endpoint is reachable
        expect([401, 200]).to.include(response.status);
      } catch (error) {
        console.error('Connectivity test failed:', error.message);
        if (error.code) console.error('Error code:', error.code);
        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response data:', error.response.data);
        }
        throw error;
      }
    });

    it('should authenticate successfully', async function () {
      this.timeout(15000);
      try {
        const response = await axios.get(`${API_URL}/bookmark/member/${MEMBER_ID}`, {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
          validateStatus: () => true,
        });

        console.log(`Auth test response status: ${response.status}`);
        console.log(`Auth test response data type:`, typeof response.data);
        console.log(
          `Auth test response data length:`,
          Array.isArray(response.data) ? response.data.length : 'N/A',
        );

        expect(response.status).to.equal(200);
        expect(response.data).to.be.an('array');
      } catch (error) {
        console.error('Authentication test failed:', error.message);
        if (error.response) {
          console.error('Auth error status:', error.response.status);
          console.error('Auth error data:', error.response.data);
        }
        throw error;
      }
    });
  });

  describe('Basic CRUD Operations - Environment Comparison', () => {
    const testData = {
      contentSource: 'bcsite',
      contentType: 'post',
      contentId: `env-test-${TEST_ENV}-${Date.now()}`,
    };

    afterEach(async function () {
      this.timeout(10000);
      // Clean up test bookmark
      try {
        await axios.delete(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}/${testData.contentId}`,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            validateStatus: () => true,
          },
        );
      } catch (error) {
        console.log('Cleanup error (expected if bookmark was not created):', error.message);
      }
    });

    it('should create a bookmark successfully', async function () {
      this.timeout(15000);
      try {
        const createPayload = {
          memberId: MEMBER_ID,
          contentSource: testData.contentSource,
          contentType: testData.contentType,
          contentId: testData.contentId,
        };

        console.log(`Creating bookmark with payload:`, createPayload);
        console.log(
          `POST URL: ${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}/${testData.contentId}`,
        );

        const createResponse = await axios.post(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}/${testData.contentId}`,
          createPayload,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
              'Content-Type': 'application/json',
            },
            validateStatus: () => true,
          },
        );

        console.log(`Create response status: ${createResponse.status}`);
        console.log(`Create response data:`, createResponse.data);

        expect(createResponse.status).to.equal(201);
        expect(createResponse.data).to.have.property('id');
        expect(createResponse.data).to.have.property('memberId', MEMBER_ID);
        expect(createResponse.data).to.have.property('contentSource', testData.contentSource);
        expect(createResponse.data).to.have.property('contentType', testData.contentType);
        expect(createResponse.data).to.have.property('contentId', testData.contentId);
      } catch (error) {
        console.error('Create bookmark failed:', error.message);
        if (error.response) {
          console.error('Create error status:', error.response.status);
          console.error('Create error data:', error.response.data);
          console.error('Create error headers:', error.response.headers);
        }
        throw error;
      }
    });

    it('should retrieve bookmarks by content type', async function () {
      this.timeout(15000);
      try {
        // First create a bookmark
        await axios.post(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}/${testData.contentId}`,
          {
            memberId: MEMBER_ID,
            contentSource: testData.contentSource,
            contentType: testData.contentType,
            contentId: testData.contentId,
          },
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
              'Content-Type': 'application/json',
            },
          },
        );

        // Then retrieve bookmarks
        const getResponse = await axios.get(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}`,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            validateStatus: () => true,
          },
        );

        console.log(`Get response status: ${getResponse.status}`);
        console.log(`Get response data type:`, typeof getResponse.data);
        console.log(
          `Get response data length:`,
          Array.isArray(getResponse.data) ? getResponse.data.length : 'N/A',
        );

        expect(getResponse.status).to.equal(200);
        expect(getResponse.data).to.be.an('array');

        // Find our test bookmark
        const bookmark = getResponse.data.find((b) => b.contentId === testData.contentId);
        expect(bookmark).to.exist;
        expect(bookmark.contentSource).to.equal(testData.contentSource);
        expect(bookmark.contentType).to.equal(testData.contentType);
      } catch (error) {
        console.error('Retrieve bookmarks failed:', error.message);
        if (error.response) {
          console.error('Retrieve error status:', error.response.status);
          console.error('Retrieve error data:', error.response.data);
        }
        throw error;
      }
    });

    it('should delete a bookmark successfully', async function () {
      this.timeout(15000);
      try {
        // First create a bookmark
        await axios.post(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}/${testData.contentId}`,
          {
            memberId: MEMBER_ID,
            contentSource: testData.contentSource,
            contentType: testData.contentType,
            contentId: testData.contentId,
          },
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
              'Content-Type': 'application/json',
            },
          },
        );

        // Then delete it
        const deleteResponse = await axios.delete(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${testData.contentSource}/${testData.contentType}/${testData.contentId}`,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            validateStatus: () => true,
          },
        );

        console.log(`Delete response status: ${deleteResponse.status}`);
        console.log(`Delete response data:`, deleteResponse.data);

        expect(deleteResponse.status).to.equal(204);
      } catch (error) {
        console.error('Delete bookmark failed:', error.message);
        if (error.response) {
          console.error('Delete error status:', error.response.status);
          console.error('Delete error data:', error.response.data);
        }
        throw error;
      }
    });
  });

  describe('Pagination Tests - Environment Comparison', () => {
    const paginationTestData = {
      contentSource: 'bcsite',
      contentType: 'article',
      contentIds: [
        `pagination-${TEST_ENV}-1-${Date.now()}`,
        `pagination-${TEST_ENV}-2-${Date.now()}`,
        `pagination-${TEST_ENV}-3-${Date.now()}`,
      ],
    };

    before(async function () {
      this.timeout(20000);
      // Create test bookmarks for pagination
      for (const contentId of paginationTestData.contentIds) {
        try {
          await axios.post(
            `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${paginationTestData.contentSource}/${paginationTestData.contentType}/${contentId}`,
            {
              memberId: MEMBER_ID,
              contentSource: paginationTestData.contentSource,
              contentType: paginationTestData.contentType,
              contentId: contentId,
            },
            {
              headers: {
                Authorization: `Bearer ${jwt}`,
                'Content-Type': 'application/json',
              },
            },
          );
        } catch (error) {
          console.error(`Failed to create pagination test bookmark ${contentId}:`, error.message);
        }
      }
    });

    after(async function () {
      this.timeout(20000);
      // Clean up pagination test bookmarks
      for (const contentId of paginationTestData.contentIds) {
        try {
          await axios.delete(
            `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${paginationTestData.contentSource}/${paginationTestData.contentType}/${contentId}`,
            {
              headers: {
                Authorization: `Bearer ${jwt}`,
              },
              validateStatus: () => true,
            },
          );
        } catch (error) {
          console.log(`Cleanup error for ${contentId}:`, error.message);
        }
      }
    });

    it('should return paginated results', async function () {
      this.timeout(15000);
      try {
        const pageSize = 2;
        const pageIndex = 0;

        const response = await axios.get(
          `${API_URL}/bookmark/v2/member/${MEMBER_ID}/content/${paginationTestData.contentSource}/${paginationTestData.contentType}?pageSize=${pageSize}&pageIndex=${pageIndex}`,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            validateStatus: () => true,
          },
        );

        console.log(`Pagination response status: ${response.status}`);
        console.log(`Pagination response data:`, response.data);

        expect(response.status).to.equal(200);
        expect(response.data).to.be.an('object');
        expect(response.data).to.have.property('items').that.is.an('array');
        expect(response.data).to.have.property('totalItems');
        expect(response.data).to.have.property('pageIndex', pageIndex);
        expect(response.data).to.have.property('pageSize', pageSize);
      } catch (error) {
        console.error('Pagination test failed:', error.message);
        if (error.response) {
          console.error('Pagination error status:', error.response.status);
          console.error('Pagination error data:', error.response.data);
        }
        throw error;
      }
    });
  });

  describe('Error Handling - Environment Comparison', () => {
    it('should return 401 when no auth token is provided', async function () {
      this.timeout(10000);
      try {
        const response = await axios.get(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/bcsite/post`,
          { validateStatus: () => true },
        );

        console.log(`No auth response status: ${response.status}`);
        expect(response.status).to.equal(401);
      } catch (error) {
        console.error('No auth test failed:', error.message);
        throw error;
      }
    });

    it('should return 404 for non-existent bookmark', async function () {
      this.timeout(10000);
      try {
        const nonExistentId = `non-existent-${Date.now()}`;
        const response = await axios.get(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/bcsite/article/${nonExistentId}`,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            validateStatus: () => true,
          },
        );

        console.log(`Non-existent bookmark response status: ${response.status}`);
        expect(response.status).to.equal(404);
      } catch (error) {
        console.error('Non-existent bookmark test failed:', error.message);
        if (error.response) {
          console.error('Non-existent error status:', error.response.status);
          console.error('Non-existent error data:', error.response.data);
        }
        throw error;
      }
    });

    it('should handle invalid member ID', async function () {
      this.timeout(10000);
      try {
        const invalidMemberId = 'invalid-member-id';
        const response = await axios.get(`${API_URL}/bookmark/member/${invalidMemberId}`, {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
          validateStatus: () => true,
        });

        console.log(`Invalid member ID response status: ${response.status}`);
        console.log(`Invalid member ID response data:`, response.data);

        // This might behave differently between environments
        expect([401, 403, 404]).to.include(response.status);
      } catch (error) {
        console.error('Invalid member ID test failed:', error.message);
        if (error.response) {
          console.error('Invalid member error status:', error.response.status);
          console.error('Invalid member error data:', error.response.data);
        }
        throw error;
      }
    });
  });

  describe('Alternative Endpoints - Environment Comparison', () => {
    const altTestData = {
      contentSource: 'bcsite',
      contentType: 'video',
      contentId: `alt-test-${TEST_ENV}-${Date.now()}`,
    };

    afterEach(async function () {
      this.timeout(10000);
      try {
        await axios.delete(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${altTestData.contentSource}/${altTestData.contentType}/${altTestData.contentId}`,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
            },
            validateStatus: () => true,
          },
        );
      } catch (error) {
        console.log('Alt cleanup error:', error.message);
      }
    });

    it('should save bookmark using alternative route', async function () {
      this.timeout(15000);
      try {
        const bookmarkData = {
          memberId: MEMBER_ID,
          contentSource: altTestData.contentSource,
          contentType: altTestData.contentType,
          contentId: altTestData.contentId,
        };

        console.log(`Alternative save URL: ${API_URL}/bookmark/v1/member/${MEMBER_ID}/content`);
        console.log(`Alternative save payload:`, bookmarkData);

        const response = await axios.post(
          `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content`,
          bookmarkData,
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
              'Content-Type': 'application/json',
            },
            validateStatus: () => true,
          },
        );

        console.log(`Alternative save response status: ${response.status}`);
        console.log(`Alternative save response data:`, response.data);

        // This endpoint might behave differently between environments
        if (response.status === 200 || response.status === 201) {
          expect(response.data).to.have.property('id');
          expect(response.data).to.have.property('memberId', MEMBER_ID);
        } else {
          console.log(
            `Alternative endpoint returned status ${response.status} - may not be fully implemented in ${TEST_ENV}`,
          );
          expect([400, 404, 500]).to.include(response.status);
        }
      } catch (error) {
        console.error('Alternative save test failed:', error.message);
        if (error.response) {
          console.error('Alternative save error status:', error.response.status);
          console.error('Alternative save error data:', error.response.data);
        }
        // Don't throw error here as this endpoint might not work in all environments
        console.log('Alternative endpoint test completed with error - this may be expected');
      }
    });

    it('should test batch save functionality', async function () {
      this.timeout(15000);
      try {
        const batchData = [
          {
            memberId: MEMBER_ID,
            contentSource: 'bcsite',
            contentType: 'article',
            contentId: `batch-1-${TEST_ENV}-${Date.now()}`,
          },
          {
            memberId: MEMBER_ID,
            contentSource: 'bcsite',
            contentType: 'article',
            contentId: `batch-2-${TEST_ENV}-${Date.now()}`,
          },
        ];

        console.log(`Batch save URL: ${API_URL}/bookmark/member/${MEMBER_ID}`);
        console.log(`Batch save payload:`, { bookmarks: batchData });

        const response = await axios.post(
          `${API_URL}/bookmark/member/${MEMBER_ID}`,
          { bookmarks: batchData },
          {
            headers: {
              Authorization: `Bearer ${jwt}`,
              'Content-Type': 'application/json',
            },
            validateStatus: () => true,
          },
        );

        console.log(`Batch save response status: ${response.status}`);
        console.log(`Batch save response data:`, response.data);

        if (response.status === 201) {
          expect(response.data).to.be.an('array');
          expect(response.data.length).to.be.greaterThan(0);
        } else {
          console.log(`Batch save returned status ${response.status} in ${TEST_ENV} environment`);
        }

        // Clean up batch created bookmarks
        for (const bookmark of batchData) {
          try {
            await axios.delete(
              `${API_URL}/bookmark/v1/member/${MEMBER_ID}/content/${bookmark.contentSource}/${bookmark.contentType}/${bookmark.contentId}`,
              {
                headers: {
                  Authorization: `Bearer ${jwt}`,
                },
                validateStatus: () => true,
              },
            );
          } catch (cleanupError) {
            console.log('Batch cleanup error:', cleanupError.message);
          }
        }
      } catch (error) {
        console.error('Batch save test failed:', error.message);
        if (error.response) {
          console.error('Batch save error status:', error.response.status);
          console.error('Batch save error data:', error.response.data);
        }
        throw error;
      }
    });
  });

  describe('Environment-Specific Debugging', () => {
    it('should log environment configuration details', async function () {
      console.log('\n=== ENVIRONMENT CONFIGURATION ===');
      console.log(`Environment: ${TEST_ENV}`);
      console.log(`API URL: ${API_URL}`);
      console.log(`Member ID: ${MEMBER_ID}`);
      console.log(`JWT Token Length: ${jwt ? jwt.length : 'N/A'}`);
      console.log(`JWT Token Preview: ${jwt ? jwt.substring(0, 50) + '...' : 'N/A'}`);

      // Test basic connectivity with detailed logging
      try {
        const startTime = Date.now();
        const response = await axios.get(`${API_URL}/bookmark/member/${MEMBER_ID}`, {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
          timeout: 10000,
          validateStatus: () => true,
        });
        const endTime = Date.now();

        console.log(`Response Time: ${endTime - startTime}ms`);
        console.log(`Response Status: ${response.status}`);
        console.log(`Response Headers:`, JSON.stringify(response.headers, null, 2));
        console.log(`Response Data Type: ${typeof response.data}`);
        console.log(
          `Response Data Length: ${Array.isArray(response.data) ? response.data.length : 'N/A'}`,
        );

        if (response.status === 200) {
          console.log('✅ Environment is working correctly');
        } else {
          console.log(`❌ Environment returned status ${response.status}`);
        }
      } catch (error) {
        console.log('❌ Environment connectivity failed');
        console.log(`Error: ${error.message}`);
        console.log(`Error Code: ${error.code || 'N/A'}`);
        if (error.response) {
          console.log(`Error Status: ${error.response.status}`);
          console.log(`Error Data:`, error.response.data);
        }
      }
      console.log('=== END CONFIGURATION ===\n');
    });
  });
});
