import axios from 'axios';
import { expect } from 'chai';
import Config from '../../config/index.js';

const API_URL = Config.getApiUrl('notification');

/**
 * @group integration
 */
describe('Root Endpoints (from original Java service)', () => {
  describe('Root endpoint /', () => {
    it('should return 200 for root endpoint', async () => {
      const response = await axios.get(`${API_URL}/`);

      expect(response.status).to.equal(200);
      // The root endpoint typically returns a simple status or service info
      // Just verify it responds successfully without authentication
    });
  });

  describe('Robots.txt endpoint', () => {
    it('should return robots.txt content', async () => {
      const response = await axios.get(`${API_URL}/robots.txt`);

      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.match(/text\/plain/);
      // Robots.txt should contain basic directives
      expect(response.data).to.be.a('string');
    });
  });

  describe('Health Endpoints', () => {
    // only in localhost
    if (API_URL !== 'http://localhost:4000') {
      return;
    }
    it('should return 200 for basic health check', async () => {
      const response = await axios.get(`${API_URL}/health`);
      expect(response.status).to.equal(200);
      // API returns JSON object, not plain text
      expect(response.data).to.be.an('object');
      expect(response.data).to.have.property('status', 'healthy');
    });

    it('should return health status for deep health check', async () => {
      const response = await axios.get(`${API_URL}/health/deep`);
      expect(response.status).to.be.oneOf([200, 503]);
      expect(response.data).to.be.an('object');
      expect(response.data).to.have.property('status');
      expect(response.data).to.have.property('timestamp');
      expect(response.data).to.have.property('service', 'bc-svc-bookmark-notif');
      expect(response.data).to.have.property('checks').that.is.an('object');
    });
  });
});
