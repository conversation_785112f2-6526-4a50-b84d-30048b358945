import crypto from 'crypto';

/**
 * JavaScript equivalent of the Java KeyEncrypter class for integration tests.
 * This matches the implementation in src/utils/key-encrypter.ts
 */
export class KeyEncrypter {
  static SECRET_KEY = 'Hi Ni Ha<PERSON>!';
  static keyValue = Buffer.from(KeyEncrypter.SECRET_KEY, 'utf8');

  /**
   * Encrypts data using AES encryption and returns base64-encoded result.
   * Equivalent to Java's KeyEncrypter.encode() method.
   */
  static encode(data) {
    try {
      const algorithm = 'aes-128-ecb';
      const key = new Uint8Array(KeyEncrypter.keyValue.subarray(0, 16));

      const cipher = crypto.createCipheriv(algorithm, key, null);
      let encrypted = cipher.update(data, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      return encrypted;
    } catch (error) {
      throw new Error(`Encryption failed: ${error}`);
    }
  }

  /**
   * Decrypts base64-encoded data using AES decryption.
   * Equivalent to Java's KeyEncrypter.decode() method.
   */
  static decode(data) {
    try {
      const algorithm = 'aes-128-ecb';
      const key = new Uint8Array(KeyEncrypter.keyValue.subarray(0, 16));

      const decipher = crypto.createDecipheriv(algorithm, key, null);
      let decrypted = decipher.update(data, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      throw new Error(`Decryption failed: ${error}`);
    }
  }
}
