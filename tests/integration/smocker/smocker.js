import axios from 'axios';

// https://smocker.dev/
// https://github.com/smocker-dev/smocker

const DEBUG = false;

export async function resetSmocker() {
  if (DEBUG) console.log('Smocker - reset');
  await axios.post('http://localhost:8081/reset');
  if (DEBUG) console.log('Smocker - done');
}

export async function registerSmockerMock(data) {
  if (DEBUG) console.log('Smocker - register');
  await axios
    .post('http://localhost:8081/mocks', JSON.stringify(data), {
      headers: { 'Content-Type': 'application/json' },
    })
    .catch((e) => {
      console.log('Error registering mock on Smocker', e);
      throw e;
    });
  if (DEBUG) console.log('Smocker - done');
}
