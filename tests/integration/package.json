{"name": "bookmark_service_api_tests", "version": "0.1.0", "description": "Tests for Bookmark Service API", "repository": "https://github.com/babycenter/bc-svc-bookmark-notif", "type": "module", "scripts": {"test": "mocha --timeout 10000", "database-seed": "node database-seed/database-seed.js"}, "dependencies": {"@types/chai": "4.3.11", "@types/faker": "6.6.9", "@types/mocha": "10.0.6", "@types/node": "22.0.0", "axios": "1.10.0", "chai": "5.0.0", "faker": "5.5.3", "joi": "17.11.0", "jsonwebtoken": "9.0.2", "mocha": "10.8.2", "mysql2": "3.11.4"}}