import fs from 'fs';
import jwt from 'jsonwebtoken';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';

// ---------------------------
// Axios Global Setup (CI-safe)
// ---------------------------
// axios.defaults.timeout = 30000;
// axios.defaults.maxRedirects = 3;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Default env
const DEFAULT_ENV = 'stag';

const ENVIRONMENTS = {
  local: {
    notification: 'http://localhost:4000',
    bookmark: 'http://localhost:4000',
    memberId: 'dlYBPsTMQ09YQyAz',
    legacyMemberId: '165492658255',
  },
  qa: {
    notification: 'https://notifications-qa.babycenter.com',
    bookmark: 'https://qa-bookmark-service.babycenter.com',
    graphql: 'https://qa-dev-service.babycenter.com/graphql',
    memberId: 'PikCcgGS8yOpcB2R',
    legacyMemberId: '165493171992',
    user: {
      email: '<EMAIL>',
      password: 'Test1234!',
    },
  },
  stag: {
    notification: 'https://notifications-stag.babycenter.com',
    bookmark: 'https://stag-bookmark-service.babycenter.com',
    graphql: 'https://stag-service.babycenter.com/graphql',
    memberId: 'qiItSFBGFtVqpLAP',
    legacyMemberId: '165565986020',
    user: {
      email: '<EMAIL>',
      password: 'Test1234!',
    },
  },
};

// ---------------------------
// API Utils
// ---------------------------
function getApiUrl(service = 'bookmark', env = DEFAULT_ENV) {
  const url = ENVIRONMENTS[env]?.[service];
  if (!url) throw new Error(`API URL not defined for service "${service}" in env "${env}"`);
  return url;
}

function getMemberId(env = DEFAULT_ENV) {
  return ENVIRONMENTS[env]?.memberId || null;
}

function getLegacyMemberId(env = DEFAULT_ENV) {
  return ENVIRONMENTS[env]?.legacyMemberId || null;
}

// ---------------------------
// Local JWT Generator
// ---------------------------
function getLocalJwtToken(customPayload = {}) {
  const envData = ENVIRONMENTS.local;
  const payload = {
    aud: [
      'photo-service',
      'notification-service',
      'registry-service',
      'babyname-service',
      'bookmark-service',
      'tool-service',
    ],
    sub: envData.memberId,
    site_user: `bcsite,${envData.legacyMemberId}`,
    scope: ['siteUser'],
    policy: 'web',
    ...customPayload,
  };

  const projectRoot = path.resolve(__dirname, '../../../');
  const privateKeyPath = path.join(projectRoot, 'keys', 'dev_key_private.pem');
  const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

  return jwt.sign(payload, privateKey, {
    algorithm: 'RS256',
    expiresIn: '10m',
  });
}

// ---------------------------
// Remote Token via GraphQL Login
// ---------------------------
async function getRemoteToken(env) {
  const envData = ENVIRONMENTS[env];
  const graphqlUrl = envData?.graphql;
  const user = envData?.user;

  if (!graphqlUrl || !user) {
    throw new Error(`Missing graphql URL or user credentials for environment "${env}"`);
  }

  const query = `
    mutation {
      loginV2(input: {
        email: "${user.email}"
        password: "${user.password}"
      }) {
        userAccountInfo {
          userProfile {
            legacyId
            globalUserId
          }
        }
      }
    }
  `;

  try {
    const response = await axios.post(
      graphqlUrl,
      { query },
      {
        headers: {
          'Content-Type': 'application/json',
          Cookie: 'bcstag=a3J1c3R5OkNsMHdu;',
        },
      },
    );

    const cookies = response.headers['set-cookie'] || [];
    const tokenCookie = cookies.find((c) => c.startsWith('bcat_en_us='));
    if (!tokenCookie) throw new Error('bcat_en_us cookie not found in response');
    const token = tokenCookie.split(';')[0].split('=')[1];
    return token;
  } catch (error) {
    console.error(error.response?.data || error);
    throw error;
  }
}

// ---------------------------
// Unified Token Generator
// ---------------------------
async function getToken(payload = {}, env = DEFAULT_ENV) {
  if (env === 'local') {
    return getLocalJwtToken(payload);
  }
  return await getRemoteToken(env);
}

// ---------------------------
// Exports
// ---------------------------
export default {
  getApiUrl,
  getMemberId,
  getLegacyMemberId,
  getToken,
};
