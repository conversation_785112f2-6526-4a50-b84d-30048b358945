AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  bc-svc-bookmark-notif

  SAM Template for notification processing lambdas

Parameters:
  Environment:
    Type: String
    AllowedValues: [qa, stag, prod]
    Description: Environment name

Mappings:
  EnvironmentSuffixes:
    qa:
      Suffix: '-qa'
    stag:
      Suffix: '-stag'
    prod:
      Suffix: ''

# Pre-calculate the suffix as a template variable
Globals:
  Function:
    Timeout: 30
    Runtime: nodejs20.x
    MemorySize: 256
    Environment:
      Variables:
        NODE_ENV: !Ref Environment
        AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1

Resources:
  # Community Events Lambda Function
  CommunityEventsFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        Minify: true
        Target: es2022
        Sourcemap: true
        EntryPoints:
          - community-events-handler.ts
    Properties:
      FunctionName: !Sub 'bc-community-notification-processor-${Environment}'
      CodeUri: src/lambda/
      Handler: community-events-handler.handler
      Description: Process community notification events from SQS
      Environment:
        Variables:
          DYNAMO_GLOBAL_SUBSCRIPTION_TABLE: GlobalSubscription
          DYNAMO_NOTIFICATION_TABLE: Notification
          DYNAMO_MEMBER_ACTIVITY_TABLE: MemberActivityStatus
          DYNAMO_SUBSCRIPTION_TABLE: Subscription
          NOTIFICATION_LIFETIME_DAYS: 7
      DeadLetterQueue:
        Type: SQS
        TargetArn: !GetAtt CommunityEventsDeadLetterQueue.Arn
      Policies:
        - DynamoDBCrudPolicy:
            TableName: GlobalSubscription
        - DynamoDBCrudPolicy:
            TableName: Notification
        - DynamoDBCrudPolicy:
            TableName: MemberActivityStatus
        - DynamoDBCrudPolicy:
            TableName: Subscription
      Events:
        CommunityEventsQueue:
          Type: SQS
          Properties:
            Queue: !Sub
              - 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:bc-community-notification-svc${Suffix}'
              - Suffix: !FindInMap [EnvironmentSuffixes, !Ref Environment, Suffix]
            BatchSize: 1
            MaximumBatchingWindowInSeconds: 0
            FunctionResponseTypes:
              - ReportBatchItemFailures

  # Subscription Events Lambda Function
  SubscriptionEventsFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        Minify: true
        Target: es2022
        Sourcemap: true
        EntryPoints:
          - subscription-events-handler.ts
    Properties:
      FunctionName: !Sub 'bc-subscription-notification-processor-${Environment}'
      CodeUri: src/lambda/
      Handler: subscription-events-handler.handler
      Description: Process subscription notification events from SQS
      Environment:
        Variables:
          DYNAMO_GLOBAL_SUBSCRIPTION_TABLE: GlobalSubscription
          DYNAMO_NOTIFICATION_TABLE: Notification
          DYNAMO_MEMBER_ACTIVITY_TABLE: MemberActivityStatus
          DYNAMO_SUBSCRIPTION_TABLE: Subscription
      DeadLetterQueue:
        Type: SQS
        TargetArn: !GetAtt SubscriptionEventsDeadLetterQueue.Arn
      Policies:
        - DynamoDBCrudPolicy:
            TableName: GlobalSubscription
        - DynamoDBCrudPolicy:
            TableName: Notification
        - DynamoDBCrudPolicy:
            TableName: MemberActivityStatus
        - DynamoDBCrudPolicy:
            TableName: Subscription
      Events:
        SubscriptionEventsQueue:
          Type: SQS
          Properties:
            Queue: !Sub
              - 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:bc-subscription-notification-svc${Suffix}'
              - Suffix: !FindInMap [EnvironmentSuffixes, !Ref Environment, Suffix]
            BatchSize: 1
            MaximumBatchingWindowInSeconds: 0
            FunctionResponseTypes:
              - ReportBatchItemFailures

  # Dead Letter Queues for Lambda Functions
  CommunityEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub
        - 'bc-community-notification-lambda-dlq${Suffix}'
        - Suffix: !FindInMap [EnvironmentSuffixes, !Ref Environment, Suffix]
      MessageRetentionPeriod: 1209600

  SubscriptionEventsDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub
        - 'bc-subscription-notification-lambda-dlq${Suffix}'
        - Suffix: !FindInMap [EnvironmentSuffixes, !Ref Environment, Suffix]
      MessageRetentionPeriod: 1209600

Outputs:
  CommunityEventsFunctionArn:
    Description: 'Community Events Lambda Function ARN'
    Value: !GetAtt CommunityEventsFunction.Arn

  SubscriptionEventsFunctionArn:
    Description: 'Subscription Events Lambda Function ARN'
    Value: !GetAtt SubscriptionEventsFunction.Arn
