module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020, // Allows for the parsing of modern ECMAScript features
    sourceType: 'module',
  },
  extends: [
    'plugin:@typescript-eslint/recommended', // recommended rules from the @typescript-eslint/eslint-plugin
    'plugin:prettier/recommended', // Enables eslint-plugin-prettier and eslint-config-prettier. This will display prettier errors as ESLint errors. Make sure this is always the last configuration in the extends array.
  ],
  rules: {
    // Place to specify ESLint rules. Can be used to overwrite rules specified from the extended configs
    '@typescript-eslint/no-unused-vars': [
      'error',
      { varsIgnorePattern: '^_', argsIgnorePattern: '^_' },
    ],
    '@typescript-eslint/no-explicit-any': 'warn', // Encourage proper typing
    '@typescript-eslint/ban-ts-comment': 'warn', // Discourage @ts-ignore usage
    '@typescript-eslint/no-var-requires': 'error', // Enforce ES6 imports
    '@typescript-eslint/explicit-function-return-type': 'warn', // Encourage explicit return types
  },
  overrides: [
    {
      // Allow more flexibility in test files
      files: ['**/*.test.ts', '**/*.test.js', '**/*.spec.ts', '**/*.spec.js'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'warn',
        '@typescript-eslint/explicit-function-return-type': 'off',
      },
    },
    {
      // Disable TypeScript rules for JavaScript files
      files: ['**/*.js'],
      rules: {
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/no-var-requires': 'off',
      },
    },
    {
      // Allow require() in config files and scripts (keeping for clarity)
      files: [
        '*.config.js',
        'jest.config.js',
        'jest.config.ci.js',
        '.eslintrc.js',
        'scripts/**/*.js',
        'src/server.ts',
      ],
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
      },
    },
  ],
};
