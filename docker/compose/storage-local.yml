version: "3.8"
services:
  dynamodb:
    image: amazon/dynamodb-local:latest
    container_name: bc-notification-dynamodb
    command: "-jar DynamoDBLocal.jar -sharedDb"
    volumes:
      - ../../data/dynamodb:/home/<USER>/data
    ports:
      - "8000:8000"
    expose:
      - "8000"
  dynamodb-seeder:
    image: amazon/aws-cli
    depends_on:
      - dynamodb
    links:
      - dynamodb
    container_name: dynamodb-seeder
    environment:
      AWS_DEFAULT_PROFILE: dynamodb-local
      AWS_ACCESS_KEY_ID: default_access_key
      AWS_SECRET_ACCESS_KEY: default_secret_key
      REGION: us-east-1
      AWS_ENDPOINT: --endpoint-url=http://dynamodb:8000
    entrypoint: /bin/sh -c
    volumes:
      - "../config/dynamodb/init-scripts:/init-scripts"
      - "../../misc/ddl:/ddl"
    command: >
      '
       for script_name in /init-scripts/*.sh; do
         sh $$script_name
       done
      '
