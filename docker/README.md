### Docker for DynamoDB

Docker DynamoDB is purely optional right now, but if you're interested
in doing it, here's what we have set up.

#### Quick Start

Assuming you have docker already installed and configured,
then from the project root:

    > docker-compose -f docker/compose/storage-local.yml up

This will build a DynamoDB image locally.
It will also automatically create all schema on `misc/ddl/*.json`.

And that's all! You are set!

### Tips

- If you wish to start a specific container, just use the commands below:

For DynamoDB:

    > docker-compose -f docker/compose/storage-local.yml up dynamodb

