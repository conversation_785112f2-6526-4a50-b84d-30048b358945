services:
  mysql:
    image: mysql:8.0.35
    ports:
      - '3307:3306'
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: bookmarks
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-ppassword']
      interval: 5s
      timeout: 5s
      retries: 5

  dynamodb-local:
    image: amazon/dynamodb-local:latest
    ports:
      - '8000:8000'
    command: '-jar DynamoDBLocal.jar -sharedDb -inMemory'

  migrations:
    image: mysql:8.0.35
    command: >
      bash -c "until mysql -hmysql -P3306 -uroot -ppassword -e 'SELECT 1;' &>/dev/null; do echo 'Waiting for MySQL...'; sleep 3; done && echo 'Running migrations...' && for migration in \$$(find /migrations -name '*.sql' | sort); do echo \"Applying: \$$(basename \$$migration)\"; mysql -hmysql -uroot -ppassword bookmarks < \$$migration; done && if [ \"\$$SEED_DATA\" = \"true\" ]; then echo 'Seeding data...'; mysql -hmysql -uroot -ppassword bookmarks < /seeds/0_bookmark_seed.sql; fi && echo 'Migrations completed'"
    environment:
      - SEED_DATA=true
    volumes:
      - ./migrations:/migrations
      - ./tests/database/mysql/seed:/seeds
    depends_on:
      mysql:
        condition: service_healthy

  dynamodb-setup:
    image: node:22-slim
    working_dir: /app
    command: >
      sh -c "
        echo 'Waiting for DynamoDB to be available...'
        apt-get update && apt-get install -y curl && \
        until curl -s http://dynamodb-local:8000/ > /dev/null 2>&1; do
          echo 'DynamoDB is not available yet, waiting...'
          sleep 3
        done
        echo 'Setting up DynamoDB tables...'
        node /app/tests/database/dynamodb/setup.js
      "
    environment:
      - DYNAMODB_ENDPOINT=http://dynamodb-local:8000
      - AWS_ACCESS_KEY_ID=local
      - AWS_SECRET_ACCESS_KEY=local
    volumes:
      - ./tests/database/dynamodb:/app/tests/database/dynamodb
      - ./node_modules:/app/node_modules
    depends_on:
      - dynamodb-local

  api:
    build: .
    ports:
      - '4000:4000'
    environment:
      - DB_HOST=mysql
      - DB_USER=root
      - DB_PASS=password
      - DB_NAME=bookmarks
      - DB_PORT=3306
      - ENVIRONMENT=dev
      - PORT=4000
      - BC_AUTH_SIGNATURE=file:keys/dev_key_public.pem
      - BC_AUTH_BASE_URL=https://qa-auth-service-internal.babycenter.com
      - DYNAMODB_ENDPOINT=http://dynamodb-local:8000
      - DYNAMODB_REGION=us-east-1
      - DYNAMODB_TABLE_NAME=notification
      - US_MEMBERS_EVENTS_SNS_ARN=arn:aws:sns:us-east-1:123456789012:test-topic
    depends_on:
      - migrations
      - dynamodb-setup

volumes:
  mysql_data:
  dynamodb_data:
