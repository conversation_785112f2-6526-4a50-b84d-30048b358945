require 'bc_cloudjoy/monkeypatch/monkeypatch'
require 'cloudjoy/utils/names'

@region = "us-east-1"
@environment = "stag"
@name = "bookmarkservice-fxtr"
@stack_name = Cloudjoy::Utils::Names.fixture_stack_name_for_formation("BookmarkserviceStack#{cloc}", @region, @environment)
#@stack_name = "BookmarkserviceStackStag"
@region_config = stack_config(@stack_name, @region, @environment)

@formation = CloudFormation.create @stack_name, project, @region_config do |t|
  t.with_defaults
  
  @fxtr_sec_group = t.ec2_security_group "BookmarkserviceStackFxtrSecGrp#{@environment.capitalize}"

  t.simple_elb "BookmarkserviceClusterExtElb#{@environment.capitalize}" do |elb|
    elb.label = :external
    elb.external
    elb.fxtr_security_group = @fxtr_sec_group
    
    elb.with_https_termination_cnx({:port => 443, :instance_port => 8080}) do |cnx|
      cnx.allow_ingress_from_anywhere
      cnx.ssl_certificate_id = "arn:aws:acm:us-east-1:762359486493:certificate/cfeddb9b-884b-4371-8e22-a905549b245f"
    end
    elb.with_default_health_check do |hc|
      hc.target = "HTTP:8081/admin/health"
      # hc.target = "TCP:8080"
    end
    elb.load_balancer.allow_icmp_echo_from_babycenter
  end

  t.simple_elb "BookmarkserviceClusterIntElb#{@environment.capitalize}" do |elb|
    elb.label = :internal
    elb.fxtr_security_group = @fxtr_sec_group
    
    elb.with_http_cnx({:port => 80, :instance_port => 8081}) do |cnx|
      cnx.allow_ingress_from_babycenter
    end
    elb.with_default_health_check do |hc|
      hc.target = "HTTP:8081/admin/health"
    end
    elb.load_balancer.allow_icmp_echo_from_babycenter
  end

  t.add_output_value(Cloudjoy::Utils::Names.fxtr_security_group_output_key, @fxtr_sec_group)
end
