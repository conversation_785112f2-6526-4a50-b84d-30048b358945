require 'cloudjoy/resource/misc/alarms'
require 'bc_cloudjoy/monkeypatch/monkeypatch'
require 'bc_cloudjoy/trait/qa/cluster'
require 'cloudjoy_ext/trait/code_deploy'
require 'cloudjoy/package_type/jar'
require 'cloudjoy/build_type/gradle'
require 'cloudjoy_ext/trait/chef_node'

@region = "us-east-1"
@environment = "qa"
@name = "bookmarkservice"
@stack_name = "BookmarkserviceStack#{cloc}"
@region_config = stack_config(@stack_name, @region, @environment)

@formation = CloudFormation.create @stack_name, project, @region_config do |t|
  t.with_defaults
  
  t.cluster_app "BookmarkserviceCluster#{@environment.capitalize}", :as => [CloudjoyExt::Trait::ChefNode, CloudjoyExt::Trait::CodeDeploy, BCCloudjoy::Trait::QA::Cluster] do |cl|
    java_options = [
      '-Dspring.profiles.active=' + @environment,
      '-Dspring.config.location=./secure/secure.properties',
      '-XX:+UseG1GC',
      '-verbose:gc',
      '-XX:MaxGCPauseMillis=200',
      '-XX:MaxMetaspaceSize=128m',
      '-Xms256m',
      '-Xmx1536m',
      '-Djava.rmi.server.hostname=%(host_node_name)s',
      '-Dcom.sun.management.jmxremote',
      '-Dcom.sun.management.jmxremote.port=9696',
      '-Dcom.sun.management.jmxremote.rmi.port=9697',
      '-Dcom.sun.management.jmxremote.authenticate=false',
      '-Dcom.sun.management.jmxremote.ssl=false'
    ]
    cl.application.build_type = BuildType::Gradle.factory({
      :dist_name => @name, 
      :build_info => {
          :health_url => "http://localhost:8081/admin/health"
      }, 
      :package_type => Cloudjoy::PackageType::Jar.new({:exec_options => java_options})
    })
    cl.exact_size = 1
    cl.instance_type = 't2.small'
    cl.in_private_subnets
    cl.set_hostname({:local_hostname => true, :override_domain => ".ec2.internal"})
    cl.ssh_to = :private_ip
    cl.open_port_to_babycenter 22
    cl.open_port_to_babycenter 9696
    cl.open_port_to_babycenter 9697
    cl.open_port_to_vpc 8080
    cl.open_port_to_vpc 8081
    cl.open_port_to_babycenter 8080
    cl.open_port_to_babycenter 8081
    cl.open_port_to_babycenter 4110
    cl.allow_icmp_echo_from_babycenter
    cl.allow_snmp_from_babycenter
    cl.add_security_group 'sg-8e9e9bf6' #sg is for allowing inbound prometheus_exporter ports among other universal things for all stacks req by babycenter. Created from hog/cloudjoyenvs project
    
    cl.publish_events [:terminate, :terminate_error], to_topics: t.config['sns_topic_arns']['auto_scaling']['termination']
    
    cl.chef_node.manage_environment 'bookmarkservice-app_us-east-1_qa'
    cl.chef_node.manage_roles ['bookmarkservice-app']
    cl.chef_node.environment_name = 'bookmarkservice-app_us-east-1_qa'
    cl.chef_node.node_runlist = ['role[base-bc]', 'role[spring-boot-app]', 'role[bookmarkservice-app-qa]']
    
    # cl.with_alarm_set "StandardAlarms" do |alarm_set|
      # alarm_set.notify_actions = t.config['sns_topic_arns']['aws_alert']
      # alarm_set.add_alarm Cloudjoy::Resource::Misc::Alarms.asg_min_instances
      # alarm_set.add_alarm Cloudjoy::Resource::Misc::Alarms.elb_spillover
      # alarm_set.add_alarm Cloudjoy::Resource::Misc::Alarms.elb_surge_queue
    # end

    t.with_fxtr_stack do |fxtr_outputs|
      cl.add_fxtr_security_group fxtr_outputs
      cl.behind_fxtr_elb fxtr_outputs, :external
      cl.behind_fxtr_elb fxtr_outputs, :internal
    end
  end
end

