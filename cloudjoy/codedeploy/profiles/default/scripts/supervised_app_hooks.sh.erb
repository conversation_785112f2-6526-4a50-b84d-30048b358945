#!/usr/bin/env bash

set -e

if [ "$LIFECYCLE_EVENT" == "ApplicationStop" ]; then
  echo "Running ApplicationStop"
  echo "Stopping applications <%= dist_output[:dist_name] %>, <%= dist_output[:dist_name] %>_version"
  /usr/local/bin/supervisorctl stop <%= dist_output[:dist_name] %> <%= dist_output[:dist_name] %>_version
  echo "Removing old distribution /var/local/data/<%= dist_output[:dist_name] %>"
  rm -rf /var/local/data/<%= dist_output[:dist_name] %>
elif  [ "$LIFECYCLE_EVENT" == "BeforeInstall" ]; then
  echo "Running BeforeInstall"
elif  [ "$LIFECYCLE_EVENT" == "AfterInstall" ]; then
  echo "Running AfterInstall"
  chown -R webuser:webgroup /var/local/data/<%= dist_output[:dist_name] %>
elif  [ "$LIFECYCLE_EVENT" == "ApplicationStart" ]; then
  echo "Running ApplicationStart"
  echo "Restarting supervisor"
  /usr/bin/chef-client -F min
  service supervisor update
  echo "Starting applications <%= dist_output[:dist_name] %>, <%= dist_output[:dist_name] %>_version"
  exec 5>&1
  /usr/local/bin/supervisorctl restart <%= dist_output[:dist_name] %> <%= dist_output[:dist_name] %>_version | tee >(cat - >&5) | grep -v "ERROR (not running)" | grep -c -v ERROR
elif  [ "$LIFECYCLE_EVENT" == "ValidateService" ]; then
  echo "Running ValidateService"
  /usr/local/bin/health_check --interval 10 --attempts 12 --url <%= dist_output[:health_url] %>
fi