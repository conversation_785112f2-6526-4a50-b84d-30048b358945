<% release_dir = %(/var/local/data/#{dist_output[:dist_name]}/dist) -%>
<% exec_command = dist_output[:exec_command].gsub("%{release_dir}", release_dir).gsub("%{release_binary}", dist_output[:package_name]) -%>
[program:<%= dist_output[:dist_name] %>]
command=<%= exec_command %>
user=webuser
process_name=%(program_name)s
numprocs=1
numprocs_start=0
priority=999
autostart=true
autorestart=unexpected
startsecs=3
startretries=3
exitcodes=0,2
stopsignal=TERM
stopwaitsecs=20
redirect_stderr=false
stdout_logfile=AUTO
stdout_logfile_maxbytes=100MB
stdout_logfile_backups=10
stdout_capture_maxbytes=0
stdout_events_enabled=false
stderr_logfile=AUTO
stderr_logfile_maxbytes=100MB
stderr_logfile_backups=10
stderr_capture_maxbytes=0
stderr_events_enabled=false
directory=<%= release_dir %>
serverurl=AUTO