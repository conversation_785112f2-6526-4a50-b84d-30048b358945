module.exports = {
  transform: { '^.+\\.ts?$': 'ts-jest' },
  testEnvironment: 'node',
  testRegex: '/tests/unit/.*\\.test\\.(ts|tsx)$',
  moduleFileExtensions: ['ts', 'tsx', 'js'],

  // Only collect coverage when explicitly requested (npm test -- --coverage)
  collectCoverage: process.env.COLLECT_COVERAGE === 'true',
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov'],
  collectCoverageFrom: ['src/**/*.ts', '!src/gen/**', '!src/**/*.d.ts', '!src/@types/*.ts'],

  // Performance optimizations
  maxWorkers: '50%', // Limit CPU usage
  testTimeout: 5000, // Increased timeout to reduce pressure

  // Cleanup options
  forceExit: true,
  detectOpenHandles: true,
};
