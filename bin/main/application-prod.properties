
#
# datastore credentials are setup in secure/secure.properties
#
#spring.datasource.url=********************************************************************************************************************
# spring.datasource.username = bookmarks-rw
# spring.datasource.password = ????????

#
# local user server port 8080
#
server.port=8080

# secret to secure internal api calls - should be set into secure-properties using secure data bag
# bookmarksServiceSecret=qa secret

# Auth server properties
authServiceBaseUrl=https://auth-service-internal.babycenter.com
authServiceIssuer=auth.babycenter.com
authServiceValidationKey=-----B<PERSON>IN PUBLIC KEY-----\n\
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwCVy9FvE7s9AHmBKr6yu\n\
  o3/6yr0zmwZAZbaLPtQ+6U2mAULOn1rmaiq8Xeu4dmyRTuYe+JfEE3WvRmGYVNzx\n\
  h+4kELihfZiSRzWs+B4fa3d2YylazzQsY6avELwSMeu9YQnfrVFk6ByaUFZzmYPg\n\
  UdTp8d4R9q+aTIDBvDWGb34bdU7ySRrnOGDrxepLGzmrDeB8D+BmSXtqc8qMwIZP\n\
  mWwtcJh1IVaMuxqO3E3YrbXg+75NIuJDU05faf+G+uhgw434rWSQLSL/D6ui5SFB\n\
  Ug3bxQabzLoXfr1Zify5N1e/H4M5A3Mgr5NQQOORNhkp2vVGX8Sp6u5T4hSaBLk8\n\
  wQIDAQAB\n\
  -----END PUBLIC KEY-----

# Profile Event Service
profileEventService.usMemberEvents.awsRegion=us-east-1
profileEventService.usMemberEvents.topicArn=arn:aws:sns:us-east-1:551062308343:bc-us-member-events
profileEventService.subscriptionEvents.awsRegion=us-west-2
profileEventService.subscriptionEvents.topicArn=arn:aws:sns:us-west-2:551062308343:bc-subscription-events
#profileEventService.accessKeyId=
#profileEventService.secretAccessKey=

#
# graphite config
#
babycenter.statsd.enabled = true
babycenter.statsd.host = statsd-prod-app.babycenter.com
babycenter.statsd.port = 8125
babycenter.statsd.stat.prefix: prod.bookmarkservice
