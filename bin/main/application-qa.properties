
#
# datastore credentials are setup in secure/secure.properties
#
#spring.datasource.url=***************************************************************************************************************************************
#spring.datasource.username = bookmarks-rw
#spring.datasource.password = ????????

#
# local user server port 8080
#
server.port=8080

# secret to secure internal api calls
bookmarksServiceSecret=qa-bookmark-secret

# Auth server properties
authServiceBaseUrl=https://qa-auth-service-internal.babycenter.com
authServiceIssuer=qa-auth.babycenter.com
authServiceValidationKey=-----BEGIN PUBLIC KEY-----\n\
  MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd\n\
  oLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw\n\
  lf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux\n\
  xw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl\n\
  9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq\n\
  yb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe\n\
  +QIDAQAB\n\
  -----END PUBLIC KEY-----

# Profile Event Service
profileEventService.usMemberEvents.awsRegion=us-east-1
profileEventService.usMemberEvents.topicArn=arn:aws:sns:us-east-1:762359486493:bc-us-member-events-qa
profileEventService.subscriptionEvents.awsRegion=us-east-1
profileEventService.subscriptionEvents.topicArn=arn:aws:sns:us-east-1:762359486493:bc-subscription-events-qa
#profileEventService.accessKeyId=
#profileEventService.secretAccessKey=

#
# graphite config
#
babycenter.statsd.enabled = true
babycenter.statsd.host = statsd-dev-app.babycenter.com
babycenter.statsd.port = 8125
babycenter.statsd.stat.prefix: qa.bookmarkservice

# enable the health management to start using the Serving pattern for orange/black configuration
management.health.serving.isEnabled=false
# default value is serving.txt if nothing specified
management.health.serving.fileLocation=serving.txt
# enable the health management for jvm (e.g. number of threads ratio, memory threshold)
management.health.jvm.isEnabled=false
# used memory vs available memory ratio
management.health.jvm.memory.threshold=95

logging.level.com.babycenter.bookmarkservice.rest.BookmarkRestController=DEBUG
logging.level.com.babycenter.bookmarkservice.auth.RequestToken=DEBUG
