#
# by default, assume we are running in the sandbox
# for local development, override with jvm argument, like; -Dspring.profiles.active=local
#
# spring.profiles.active = default

#
# datasource url and credentials are in the application-{profile].properties file
# or the ./secure/secure.properties written by chef.
#
# spring.datasource.url =
# spring.datasource.username =
# spring.datasource.password =


# Keep the connection alive if idle for a long time (needed in production)
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.testWhileIdle = true
spring.datasource.initialSize: 10
spring.datasource.minIdle: 10
spring.datasource.maxActive: 50
spring.datasource.maxIdle: 50
spring.datasource.maxWait: 1000
spring.datasource.defaultAutoCommit: false
spring.datasource.validationQuery: "select 1"
spring.datasource.validationInterval: 30000
spring.datasource.timeBetweenEvictionRunsMillis: 5000
spring.datasource.removeAbandoned: true
spring.datasource.removeAbandonedTimeout: 30


# Show or not log for each sql query
spring.jpa.show-sql = false

# Hibernate ddl auto off (no create, create-drop, update; we do this with dbdeploy)
spring.jpa.hibernate.ddl-auto = none

# Naming strategy
spring.jpa.hibernate.naming-strategy = org.hibernate.cfg.ImprovedNamingStrategy

# Use spring.jpa.properties.* for Hibernate native properties (the prefix is
# stripped before adding them to the entity manager)

# The SQL dialect makes Hibernate generate better SQL for the chosen database
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5Dialect


#
# SSL config
#
# keystore holds certificates that are provided servers requiring them
#
#server.port=8443
#server.ssl.key-store=classpath:keystore.jks
#server.ssl.key-store-password=secret
#server.ssl.key-password=another-secret

#
# trustore holds certificates used to verify identities of servers
#
#server.ssl.trust-store=./certs/truststore.jks
#server.ssl.trust-store-password=changeit


# security.require-ssl = true
security.user.name = admin
security.user.password = iocane
security.basic.path = /admin/**

# admin management
management.context-path = /admin
management.port = 8081
management.security.role = ADMIN
endpoints.info.sensitive=true

# Enabling

# health check config
management.health.diskspace.threshold = 1048576
management.health.db.enabled= true
# enable the health management to start using the Serving pattern for orange/black configuration
management.health.serving.isEnabled=false
# default value is serving.txt if nothing specified
management.health.serving.fileLocation=serving.txt
# enable the health management for jvm (e.g. number of threads ratio, memory threshold)
management.health.jvm.isEnabled=false
# used memory vs available memory ratio
management.health.jvm.memory.threshold=95

#logging
logging.level.com.babycenter.bookmarkservice.rest.BookmarkRestController=DEBUG

