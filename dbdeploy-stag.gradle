//
// gradle buildscript for dbdeploy task
//
// to run the update scripts, run this command from the project's root folder;
//   gradle update -b dbdeploy-stag.gradle
//
buildscript {
	repositories {
		maven {
			url "https://plugins.gradle.org/m2/"
			jcenter()
		}
	}
	dependencies {
		classpath "gradle.plugin.com.lv.plugins:gradle-dbdeploy-plugin:1.2"
		classpath 'mysql:mysql-connector-java:8.0.30'
	}
}
apply plugin: "com.lv.dbdeploy"


//
// dbdeploy config - user and password come from project's properties
//
dbdeploy {
	scriptdirectory = file('src/main/sql/dbdeploy/bookmarks')
	driver = 'com.mysql.jdbc.Driver'
	url = '***********************************************************************************'
	userid = project.hasProperty('dbdeployuser') ? project.getProperty('dbdeployuser') : 'bookmarks_stag-dp'
	password = project.hasProperty('dbdeploypw') ? project.getProperty('dbdeploypw') : ''
}

