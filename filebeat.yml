filebeat.inputs:
  - type: filestream
    id: bc-non-prod-svc-bkmk-notif
    enabled: true
    paths:
      - /app/logs/*.log
    fields:
      project: 'bc'
      environment: 'non-prod'
      container-name: 'bc-svc-bkmk-notif'
      data_stream.dataset: 'non_prod.web.platform'
      event.dataset: 'non_prod.web.platform'
      data_stream.type: 'logs'
      tags:
        - 'bc'
        - 'bkmk-notif'
        - 'non-prod'
        - 'web'
    fields_under_root: true

    input_type: log
    json.keys_under_root: true
    json.add_error_key: true

#============================= Filebeat modules ===============================

filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

#==================== Elasticsearch template settings =========================

setup.template.settings:
  index.number_of_shards: 1

#================================ Kibana ======================================

setup.kibana:
  host: 'https://elastic.waterfrontmedia.net:5601'

#============================= Elasticsearch Output ===========================

output.elasticsearch:
  hosts: ['https://elastic.waterfrontmedia.net:9200']
  protocol: 'https'
  api_key: 'CnB3_JEBCAmv3j-3nyLr:LDOSsuNWRmOKCo8-aLQdLQ'

#============================= Processors =====================================

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_cloud_metadata: ~
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~
  - decode_json_fields:
      fields: ['message']
      process_array: false
      max_depth: 5
      target: ''
      overwrite_keys: true
      add_error_key: false
