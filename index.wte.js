const jwt = require("jsonwebtoken");

const payload = {
  Id: "64d72cce9a214f498252c20030a35ead",
  ClientId: "72fb9eafaaa846ecae74e43cfde556d1",
  WteId: 930608158,
  Email: "<EMAIL>",
};
// represents 1 year in seconds
const EXPIRATION_SPAN = 365 * 24 * 60 * 60;

// //dev
// const CLIENT_ID = "6d129e2f0b2c4942866b60fe5c2a59cf";
//staging
// const CLIENT_ID = "72fb9eafaaa846ecae74e43cfde556d1";

const SIGNATURE_SECRET =
  "p1fU46aYdTuNsjfBJDc/VbUXsDfmE2B6r3EHgrGNNr/1PrSQIKsHpoTMZu8Bh13vWSEen3fWgNiPlCszPRyPcQ==";

const generateToken = () => {
  const token = jwt.sign(payload, SIGNATURE_SECRET, {
    expiresIn: EXPIRATION_SPAN,
  });
  return token;
};

console.log(generateToken());
