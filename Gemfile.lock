GEM
  remote: https://rubygems.org/
  remote: http://gems.babycenter.com/
  specs:
    activesupport (4.2.8)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    addressable (2.5.0)
      public_suffix (~> 2.0, >= 2.0.2)
    aws-sdk (2.8.11)
      aws-sdk-resources (= 2.8.11)
    aws-sdk-core (2.8.11)
      aws-sigv4 (~> 1.0)
      jmespath (~> 1.0)
    aws-sdk-resources (2.8.11)
      aws-sdk-core (= 2.8.11)
    aws-sigv4 (1.0.0)
    bc-cloudjoy (0.2.8)
      cloudjoy (~> 0.2)
      cloudjoyext-chefnode (~> 0.1)
      cloudjoyext-codedeploy (~> 0.1)
      cloudjoyext-lambdaapp (~> 0.1)
      cloudjoyext-miscgenerator (~> 0.1)
    buff-config (2.0.0)
      buff-extensions (~> 2.0)
      varia_model (~> 0.6)
    buff-extensions (2.0.0)
    buff-ignore (1.2.0)
    buff-ruby_engine (1.0.0)
    buff-shell_out (1.1.0)
      buff-ruby_engine (~> 1.0)
    builder (3.2.3)
    celluloid (0.16.0)
      timers (~> 4.0.0)
    celluloid-io (0.16.2)
      celluloid (>= 0.16.0)
      nio4r (>= 1.1.0)
    chef (12.19.36)
      addressable
      bundler (>= 1.10)
      chef-config (= 12.19.36)
      chef-zero (>= 4.8)
      diff-lcs (~> 1.2, >= 1.2.4)
      erubis (~> 2.7)
      ffi-yajl (~> 2.2)
      highline (~> 1.6, >= 1.6.9)
      iniparse (~> 1.4)
      mixlib-archive (~> 0.4)
      mixlib-authentication (~> 1.4)
      mixlib-cli (~> 1.7)
      mixlib-log (~> 1.3)
      mixlib-shellout (~> 2.0)
      net-sftp (~> 2.1, >= 2.1.2)
      net-ssh (>= 2.9, < 5.0)
      net-ssh-multi (~> 1.2, >= 1.2.1)
      ohai (>= 8.6.0.alpha.1, < 13)
      plist (~> 3.2)
      proxifier (~> 1.0)
      rspec-core (~> 3.5)
      rspec-expectations (~> 3.5)
      rspec-mocks (~> 3.5)
      rspec_junit_formatter (~> 0.2.0)
      serverspec (~> 2.7)
      specinfra (~> 2.10)
      syslog-logger (~> 1.6)
      uuidtools (~> 2.1.5)
    chef-config (12.19.36)
      addressable
      fuzzyurl
      mixlib-config (~> 2.0)
      mixlib-shellout (~> 2.0)
    chef-zero (5.3.1)
      ffi-yajl (~> 2.2)
      hashie (>= 2.0, < 4.0)
      mixlib-log (~> 1.3)
      rack (~> 2.0)
      uuidtools (~> 2.1)
    cloudjoy (0.2.28)
      activesupport (~> 4.2)
      aws-sdk (~> 2.5)
      chef (~> 12)
      colored (~> 1.2)
      diffy (~> 3.0)
      extlib (~> 0.9)
      json (= 1.8.3)
      ridley (~> 5.0)
    cloudjoyext-chefnode (0.1.7)
      cloudjoy (~> 0.2.1)
    cloudjoyext-codedeploy (0.2.0)
      cloudjoy (~> 0.2.0)
    cloudjoyext-lambdaapp (0.1.4)
      cloudjoy (~> 0.2.0)
    cloudjoyext-miscgenerator (0.2.2)
      cloudjoy (~> 0.2.0)
      cloudjoyext-codedeploy (~> 0.2.0)
    colored (1.2)
    diff-lcs (1.3)
    diffy (3.2.0)
    erubis (2.7.0)
    extlib (0.9.16)
    faraday (0.9.2)
      multipart-post (>= 1.2, < 3)
    ffi (1.9.18)
    ffi-yajl (2.3.0)
      libyajl2 (~> 1.2)
    fuzzyurl (0.9.0)
    hashie (3.5.5)
    highline (1.7.8)
    hitimes (1.2.4)
    httpclient (2.8.3)
    i18n (0.8.1)
    iniparse (1.4.2)
    ipaddress (0.8.3)
    jmespath (1.3.1)
    json (1.8.3)
    libyajl2 (1.2.0)
    minitest (5.10.1)
    mixlib-archive (0.4.1)
      mixlib-log
    mixlib-authentication (1.4.1)
      mixlib-log
    mixlib-cli (1.7.0)
    mixlib-config (2.2.4)
    mixlib-log (1.7.1)
    mixlib-shellout (2.2.7)
    multi_json (1.12.1)
    multipart-post (2.0.0)
    net-scp (1.2.1)
      net-ssh (>= 2.6.5)
    net-sftp (2.1.2)
      net-ssh (>= 2.6.5)
    net-ssh (4.1.0)
    net-ssh-gateway (2.0.0)
      net-ssh (>= 4.0.0)
    net-ssh-multi (1.2.1)
      net-ssh (>= 2.6.5)
      net-ssh-gateway (>= 1.2.0)
    net-telnet (0.1.1)
    nio4r (2.0.0)
    ohai (8.23.0)
      chef-config (>= 12.5.0.alpha.1, < 13)
      ffi (~> 1.9)
      ffi-yajl (~> 2.2)
      ipaddress
      mixlib-cli
      mixlib-config (~> 2.0)
      mixlib-log (>= 1.7.1, < 2.0)
      mixlib-shellout (~> 2.0)
      plist (~> 3.1)
      systemu (~> 2.6.4)
      wmi-lite (~> 1.0)
    plist (3.2.0)
    proxifier (1.0.3)
    public_suffix (2.0.5)
    rack (2.0.1)
    retryable (2.0.4)
    ridley (5.1.0)
      addressable
      buff-config (~> 2.0)
      buff-extensions (~> 2.0)
      buff-ignore (~> 1.2)
      buff-shell_out (~> 1.0)
      celluloid (~> 0.16.0)
      celluloid-io (~> 0.16.1)
      chef-config (>= 12.5.0)
      erubis
      faraday (~> 0.9.0)
      hashie (>= 2.0.2, < 4.0.0)
      httpclient (~> 2.7)
      json (>= 1.7.7)
      mixlib-authentication (>= 1.3.0)
      retryable (~> 2.0)
      semverse (~> 2.0)
      varia_model (~> 0.6)
    rspec (3.5.0)
      rspec-core (~> 3.5.0)
      rspec-expectations (~> 3.5.0)
      rspec-mocks (~> 3.5.0)
    rspec-core (3.5.4)
      rspec-support (~> 3.5.0)
    rspec-expectations (3.5.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.5.0)
    rspec-its (1.2.0)
      rspec-core (>= 3.0.0)
      rspec-expectations (>= 3.0.0)
    rspec-mocks (3.5.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.5.0)
    rspec-support (3.5.0)
    rspec_junit_formatter (0.2.3)
      builder (< 4)
      rspec-core (>= 2, < 4, != 2.12.0)
    semverse (2.0.0)
    serverspec (2.38.0)
      multi_json
      rspec (~> 3.0)
      rspec-its
      specinfra (~> 2.53)
    sfl (2.3)
    specinfra (2.67.6)
      net-scp
      net-ssh (>= 2.7, < 5.0)
      net-telnet
      sfl
    syslog-logger (1.6.8)
    systemu (2.6.5)
    thread_safe (0.3.6)
    timers (4.0.4)
      hitimes
    tzinfo (1.2.3)
      thread_safe (~> 0.1)
    uuidtools (2.1.5)
    varia_model (0.6.0)
      buff-extensions (~> 2.0)
      hashie (>= 2.0.2, < 4.0.0)
    wmi-lite (1.0.0)

PLATFORMS
  ruby

DEPENDENCIES
  bc-cloudjoy (~> 0.2.0)!

BUNDLED WITH
   1.12.5
