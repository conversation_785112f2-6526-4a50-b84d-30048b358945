-- Migration: 20250429000001_create_bookmark_table
-- Create bookmark table for storing user bookmarks

-- Create the bookmark table
CREATE TABLE IF NOT EXISTS `bookmark` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `member_id` VARCHAR(255) NOT NULL,
  `content_source` VARCHAR(50) NOT NULL,
  `content_type` VARCHAR(50) NOT NULL,
  `content_id` VARCHAR(255) NOT NULL,
  `create_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `create_user` VARCHAR(50) DEFAULT 'system',
  `update_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `update_user` VARCHAR(50) DEFAULT 'system',
  
  -- Add unique constraint to prevent duplicate bookmarks
  UNIQUE KEY `uk_bookmark_unique` (`member_id`, `content_source`, `content_type`, `content_id`),
  
  -- Add indexes for common query patterns
  INDEX `idx_member_id` (`member_id`),
  INDEX `idx_content_source` (`content_source`),
  INDEX `idx_content_type` (`content_type`),
  INDEX `idx_content_id` (`content_id`),
  INDEX `idx_bookmark_member_source_type` (`member_id`, `content_source`, `content_type`),
  INDEX `idx_bookmark_batch_delete` (`member_id`, `content_source`, `content_type`, `content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;