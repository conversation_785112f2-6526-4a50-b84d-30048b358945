//
// gradle buildscript for dbdeploy task
//
// to run the update scripts, run this command from the project's root folder;
//   gradle update -b dbdeploy-sandbox.gradle
//
buildscript {
	repositories {
		maven {
			url "https://plugins.gradle.org/m2/"
			jcenter()
		}
	}
	dependencies {
		classpath "gradle.plugin.com.lv.plugins:gradle-dbdeploy-plugin:1.2"
		classpath 'mysql:mysql-connector-java:8.0.30'
	}
}
apply plugin: "com.lv.dbdeploy"


//
// dbdeploy config
//
dbdeploy {
	scriptdirectory = file('src/main/sql/dbdeploy/bookmarks')
	driver = 'com.mysql.jdbc.Driver'
	url = '***********************************************************************************'
	userid = 'bookmarks-dp'
	password = 'p@e0v!eho'
}
