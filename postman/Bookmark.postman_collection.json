{"info": {"_postman_id": "62262f26-45d0-432b-8e03-0cc219815213", "name": "Bookmark", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Save bookmark", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user-access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "bsToken", "value": "{{bstoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"memberId\": \"{{user-global-auth-id}}\",\n    \"contentSource\": \"community:{{country-code}}\",\n    \"contentType\": \"post\",\n    \"contentId\": \"676\",\n    \"createDate\": \"2021-05-28T17:45:00.000Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{server}}/bookmark/v1/member/{{user-global-auth-id}}/content/community:{{country-code}}/post/676", "host": ["{{server}}"], "path": ["bookmark", "v1", "member", "{{user-global-auth-id}}", "content", "community:{{country-code}}", "post", "676"]}}, "response": []}, {"name": "Delete bookmark", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user-access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "bsToken", "value": "{{bstoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{server}}/bookmark/v1/member/{{user-global-auth-id}}/content/community:{{country-code}}/post/76580762", "host": ["{{server}}"], "path": ["bookmark", "v1", "member", "{{user-global-auth-id}}", "content", "community:{{country-code}}", "post", "76580762"]}}, "response": []}, {"name": "Delete bookmarks (batch)", "request": {"method": "GET", "header": [], "url": null}, "response": []}, {"name": "List bookmark", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user-access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "bsToken", "value": "{{bstoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{server}}/bookmark/v1/member/{{user-global-auth-id}}/content/community:{{country-code}}/post", "host": ["{{server}}"], "path": ["bookmark", "v1", "member", "{{user-global-auth-id}}", "content", "community:{{country-code}}", "post"]}}, "response": []}, {"name": "List bookmark Paginated", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user-access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "bsToken", "value": "{{bstoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{server}}/bookmark/v2/member/{{user-global-auth-id}}/content/community:{{country-code}}/post?pageIndex=0&pageSize=1", "host": ["{{server}}"], "path": ["bookmark", "v2", "member", "{{user-global-auth-id}}", "content", "community:{{country-code}}", "post"], "query": [{"key": "pageIndex", "value": "0"}, {"key": "pageSize", "value": "1"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user-access-token}}", "type": "string"}, {"key": "password", "value": "{{<PERSON>T<PERSON>}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}