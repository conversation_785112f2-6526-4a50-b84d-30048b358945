version = 0.1

[default.deploy]
[default.deploy.parameters]
stack_name = "bc-svc-bookmark-notif"
s3_bucket = "bc-ue1-lq-sam-builds"
s3_prefix = "bc-svc-bookmark-notif"
region = "us-east-1"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=dev"

[qa.deploy]
[qa.deploy.parameters]
stack_name = "bc-svc-bookmark-notif-qa"
s3_bucket = "bc-ue1-lq-sam-builds"
s3_prefix = "bc-svc-bookmark-notif"
region = "us-east-1"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=qa"

[stag.deploy]
[stag.deploy.parameters]
stack_name = "bc-svc-bookmark-notif-stag"
s3_bucket = "bc-uw2-lq-sam-builds"
s3_prefix = "bc-svc-bookmark-notif"
region = "us-west-2"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=stag"

[prod.deploy]
[prod.deploy.parameters]
stack_name = "bc-svc-bookmark-notif-prod"
s3_bucket = "bc-uw2-ls-sam-builds"
s3_prefix = "bc-svc-bookmark-notif"
region = "us-west-2"
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=prod"
