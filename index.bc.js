const jose = require("jose");
const crypto = require("crypto");
const alg = "RS256";
const privateKeyPem = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
// const payload = {
//   vrsn: 0,
//   aud: [
//     "photo-service",
//     "notification-service",
//     "registry-service",
//     "babyname-service",
//     "bookmark-service",
//     "tool-service",
//   ],
//   sub: "prr3DcmaRpMezjHM",
//   site_user: "bcsite,165418997098",
//   scope: ["siteUser", "userRoleManager", "communityUser", "fullCommunityUser", "communityAdmin", "configAdmin", "userAdmin", "editor"],
//   iss: "qa-auth.babycenter.com",
//   exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
//   gvrsn: 2,
//   grant: "access",
//   iat: Math.floor(Date.now() / 1000),
//   jti: "refreshed" + Date.now(),
//   policy: "web",
// };

const payload = {
  vrsn: 0,
  aud: [
    "photo-service",
    "notification-service",
    "registry-service",
    "babyname-service",
    "bookmark-service",
    "tool-service",
  ],
  sub: "GDMGeO7jIP0NGFE6",
  site_user: "bcsite,165493171990",
  scope: ["siteUser"],
  iss: "qa-auth.babycenter.com",
  exp: 1753215156,
  gvrsn: 2,
  grant: "access",
  iat: 1753214856,
  jti: "MvB2mxnzdJKw4NdQ",
  policy: "web",
};

async function generateJWT() {
  const privateKey = crypto.createPrivateKey(privateKeyPem);
  const token = await new jose.SignJWT(payload)
    .setProtectedHeader({ alg }) // Set the algorithm header
    .sign(privateKey); // Sign the token with the key

  console.log(token);
}

generateJWT();

// async function verifyJWT() {
//   const publicKey = await jose.importSPKI(spki, alg);
//   const jwt =
//     "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

//   const { payload, protectedHeader } = await jose.jwtVerify(jwt, publicKey, {
//     issuer: "qa-auth.babycenter.com",
//   });

//   console.log(payload, protectedHeader);
// }

// verifyJWT();

// const publicKey = await jose.importSPKI(spki, alg);
// const jwt =
//   "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

// const { payload, protectedHeader } = await jose.jwtVerify(jwt, publicKey, {
//   issuer: "stag-auth.babycenter.com",
// });

// -----BEGIN PUBLIC KEY-----
// MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApvaZSiv5LHwNUC15eULd
// oLVTeeYSIuEemJUE5rtKsOQEZBBempYtoBiTT8hJQTQLHPb1lsFOZvzGX/ItiWBw
// lf/yhCOupa2zN2OrMjEOOyOwiHlifPdIBkRKpKFClmpT98C/hDsTmsnO85B+Jgux
// xw6ahgvrfqacdQzFqWRCGDmij6ClIdN62JMHRJECSaN1nZE8HkK/MNCRt0r63Awl
// 9ooukNBTB6LjmXSBTIJ0GgEn/FC8n3mH97NmNUcvxWoo3X8EAoJVRffH1QYCeDsq
// yb8VEX5t7ID67FOWKFj2gZzkGu99sQ6P70nvDfx5jiKiSBaGJRZCWkY3ltQHjfGe
// +QIDAQAB
// -----END PUBLIC KEY-----
// -----BEGIN PUBLIC KEY-----
// MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz3+XGbBu/CcVFAfEyrVu
// O3/WzcjR/EH3if5j9ZqMLmQZ/uOiRYaXX7emuyFnFTK9vwypLnTwG2AAtYGiJKVf
// ljNiDuTEjdpoaI5Ip3cevoePar5MKl6QA2RyLgZQ4E/k3506QKqPvS8fDuJhQMS1
// rxRGd+QFDrP0Mk8Q5FVj9IWETPYdcMzvo0KVFvNo02e9HQukI9c3elzBTNpXH7S+
// ZMaQq9yXWn9UZ/iFegDPF/6lT/sHEmFwHEDMe63EzwZ1owRvW2PqcqDS10Gp7MS5
// WHrYC0VVynkaChGTDLnZvtIp3uecEz84Qid/x/j+OFRbVia5pTtM1QZVXyeUZZ46
// 5wIDAQAB
// -----END PUBLIC KEY-----

// {
//   "vrsn": 0,
//   "aud": [
//     "photo-service",
//     "notification-service",
//     "registry-service",
//     "babyname-service",
//     "bookmark-service",
//     "tool-service"
//   ],
//   "sub": "lVliVcQHDgge6m2h",
//   "site_user": "bcsite,165565982436",
//   "scope": [
//     "siteUser"
//   ],
//   "iss": "stag-auth.babycenter.com",
//   "exp": 1692386529,
//   "gvrsn": 2,
//   "grant": "access",
//   "iat": 1692386229,
//   "jti": "ZmvU9PPSVfj7814n",
//   "policy": "web"
// }

// console.log(protectedHeader);
// console.log(payload);
// 1
