{"name": "arcdoc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "likec4:serve": "likec4 serve", "likec4:build": "likec4 build --base /viewer --use-hash-history", "likec4:preview": "likec4 preview --base /viewer", "gen": "likec4 generate react -o ./src/generated/likec4.generated.js ./src/c4"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.7", "likec4": "^1.31.0", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "typescript": "^5"}}