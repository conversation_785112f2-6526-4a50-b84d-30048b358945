{"name": "bookmark_service_api", "version": "0.1.0", "description": "Bookmark API: Endpoints", "repository": "https://github.com/babycenter/bc-svc-bookmark-notif", "main": "build/index.js", "engines": {"node": ">=22.0.0"}, "scripts": {"start": "node build/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "npm run build:ecs && npm run build:sam", "build:ecs": "rm -rf build && tsc", "build:sam": "tsc -p tsconfig.lambda.json --noEmit && sam build", "test": "jest", "test:ci": "jest", "test:coverage": "COLLECT_COVERAGE=true jest", "test:integration": "cd tests/integration && npm install && npm test", "lint": "eslint . --quiet --fix", "lint:ci": "eslint . --quiet --fix", "prettier": "prettier --write .", "docker:up": "docker compose up -d", "docker:rebuild": "docker compose build --no-cache", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "sam:lambda:comment": "sam local invoke CommunityEventsFunction -e tests/lambda/sample-events/comment-event.json --env-vars tests/lambda/env.json", "sam:lambda:reply": "sam local invoke CommunityEventsFunction -e tests/lambda/sample-events/reply-event.json --env-vars tests/lambda/env.json", "sam:lambda:reaction": "sam local invoke CommunityEventsFunction -e tests/lambda/sample-events/reaction-event.json --env-vars tests/lambda/env.json", "sam:lambda:subscribe": "sam local invoke SubscriptionEventsFunction -e tests/lambda/sample-events/subscribe-event.json --env-vars tests/lambda/env.json", "sam:lambda:unsubscribe": "sam local invoke SubscriptionEventsFunction -e tests/lambda/sample-events/unsubscribe-event.json --env-vars tests/lambda/env.json", "test:lambda": "npx ts-node tests/lambda/test-lambdas-locally.ts", "sam:deploy:qa": "sam deploy --config-env qa", "sam:deploy:stag": "sam deploy --config-env stag", "sam:deploy:prod": "sam deploy --config-env prod"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.670.0", "@aws-sdk/client-secrets-manager": "3.670.0", "@aws-sdk/client-sqs": "3.670.0", "@aws-sdk/lib-dynamodb": "3.670.0", "@types/cors": "2.8.17", "awilix": "12.0.2", "aws-sdk": "2.1692.0", "cors": "2.8.5", "dotenv": "16.4.5", "express": "4.21.2", "express-winston": "4.2.0", "helmet": "8.1.0", "hot-shots": "10.2.1", "ioredis": "5.4.1", "joi": "17.13.3", "jsonwebtoken": "9.0.2", "mysql2": "3.14.1", "winston": "3.15.0", "winston-daily-rotate-file": "5.0.0"}, "devDependencies": {"@types/aws-lambda": "8.10.92", "@types/express": "5.0.0", "@types/jest": "29.5.14", "@types/jsonwebtoken": "9.0.7", "@types/node": "22.0.0", "@typescript-eslint/eslint-plugin": "7.5.0", "@typescript-eslint/parser": "7.5.0", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "jest": "29.7.0", "jest-junit": "16.0.0", "nock": "14.0.0-beta.15", "prettier": "3.3.3", "ts-jest": "29.2.5", "ts-node": "10.9.2", "ts-node-dev": "2.0.0", "typescript": "5.6.3"}}