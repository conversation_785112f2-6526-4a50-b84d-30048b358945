import { createPub<PERSON><PERSON><PERSON> } from "crypto";

const Key =
  "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz3+XGbBu/CcVFAfEyrVu\nO3/WzcjR/EH3if5j9ZqMLmQZ/uOiRYaXX7emuyFnFTK9vwypLnTwG2AAtYGiJKVf\nljNiDuTEjdpoaI5Ip3cevoePar5MKl6QA2RyLgZQ4E/k3506QKqPvS8fDuJhQMS1\nrxRGd+QFDrP0Mk8Q5FVj9IWETPYdcMzvo0KVFvNo02e9HQukI9c3elzBTNpXH7S+\nZMaQq9yXWn9UZ/iFegDPF/6lT/sHEmFwHEDMe63EzwZ1owRvW2PqcqDS10Gp7MS5\nWHrYC0VVynkaChGTDLnZvtIp3uecEz84Qid/x/j+OFRbVia5pTtM1QZVXyeUZZ46\n5wIDAQAB\n-----END PUBLIC KEY-----";
function identifyKeyAlgorithm(publicKey: string) {
  try {
    publicKey = publicKey.replace(/\n/g, "\r\n");
    const keyObject = createPublicKey(publicKey);
    return keyObject.asymmetricKeyType; // 'rsa', 'ec', 'dsa', etc.
  } catch (error) {
    console.error("Failed to identify key algorithm:", error);
    return null;
  }
}

const algorithm = identifyKeyAlgorithm(Key);
console.log("Algorithm:", algorithm);
