FROM node:22-alpine AS base

RUN apk update && apk upgrade && apk add --no-cache bash git openssh curl libc6-compat gcompat iproute2 net-tools


# 1. Install all dependencies so Docker caches a layer out of this
FROM base AS build-deps
WORKDIR /buildDeps
COPY ./package.json ./package-lock.json ./
RUN npm ci

# 2. Install only run dependencies (smaller) so Docker caches a layer out of this
FROM base AS prod-deps
WORKDIR /prodDeps
COPY ./package.json ./package-lock.json ./
RUN npm ci --omit=dev

# 3. Build code
FROM base AS builder
WORKDIR /builder
# Copy dependencies from dev dependencies layer
COPY --from=build-deps /buildDeps/node_modules ./node_modules
# Make sure to add node_modules to .dockerignore so it doesn't overwrite the node_modules we just brought in
COPY . .

# Run full build process instead of individual steps
RUN npm run build:ecs

# Ensure directories exist and are not empty
RUN mkdir -p build/services/bookmark/queries build/includes \
  && touch build/services/bookmark/queries/.keep build/includes/.keep

# Check if SQL migrations exist and create a placeholder if not
RUN echo "Checking SQL migrations..." \
  && if [ ! -d migrations ] || [ -z "$(ls -A migrations 2>/dev/null)" ]; then \
  echo "No migrations found. Creating placeholder..."; \
  mkdir -p migrations; \
  echo "-- Placeholder migration" > migrations/placeholder.sql; \
  fi

# Debug information to help troubleshoot
RUN echo "Listing build directory contents:" && ls -la build/ && \
  echo "Listing services directory:" && ls -la build/services/ 2>/dev/null || echo "No services directory" && \
  echo "Listing bookmark queries directory:" && ls -la build/services/bookmark/queries/ 2>/dev/null || echo "No SQL files" && \
  echo "Listing includes directory:" && ls -la build/includes/ 2>/dev/null || echo "No include files" && \
  echo "Listing migrations directory:" && ls -la migrations/ 2>/dev/null || echo "No migrations" && \
  echo "Node version:" && node --version && \
  echo "NPM version:" && npm --version

# 4. Create runtime image
FROM base AS runtime
WORKDIR /build

# Install Filebeat 1st so Docker can cache this as a layer
RUN curl -L -O https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-8.13.2-linux-x86_64.tar.gz && \
  tar xzvf filebeat-8.13.2-linux-x86_64.tar.gz && \
  mv filebeat-8.13.2-linux-x86_64 /usr/share/filebeat && \
  rm filebeat-8.13.2-linux-x86_64.tar.gz

# Copy filebeat.yml configuration file (adjust the path as necessary)
COPY filebeat.yml /usr/share/filebeat/filebeat.yml

# Give execution permissions to the Filebeat binary
RUN chmod +x /usr/share/filebeat/filebeat

COPY entrypoint.sh /entrypoint.sh

RUN chmod +x /entrypoint.sh

HEALTHCHECK --interval=1m --timeout=3s --start-period=15s \
  CMD curl -f http://localhost:4000/health || exit 1

# Copy built artifact (the compiled JS files go directly to workdir /build)
COPY --from=builder /builder/build .
# Copy runtime dependencies only
COPY --from=prod-deps /prodDeps/node_modules ./node_modules

# Copy the package.json because it has 'type: module' set in it
# This is necessary for ESM to work
# The node CLI will run CJS without this set (and the --input-type=module does not work)
# We have to use ESM to support the lambda package, and this is a shared codebase
COPY --from=builder /builder/package.json ./package.json


COPY --from=builder /builder/keys ./keys

# Copy environment files if they exist
COPY --from=builder /builder/.env* ./

# Run the server
ENTRYPOINT ["/entrypoint.sh", "node", "index.js"]
