# Note - given how the Dockerfile uses stages and only copies the node_modules and builder folders to the app
# These are all basically ignored in the final image regardless of this file
# Granted, not coping larger artifacts (i.e. node_modules) is more efficient

# Compiled TS
/build

# Node module dependencies
/node_modules

# Environment variable configurations (commented out to allow copying)
# /.env*

# Jenkins job configurations
/jenkins

# We don't need migrations in the container
/sql

# We don't need tests and documentation in the container
/events
/postman
/tests
/swagger
*.md

/logs

/jenkins-temp