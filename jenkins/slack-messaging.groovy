// slack-messaging.groovy
// Standardized Slack messaging utilities for bc-svc-bookmark-notif

def sendJobStartMessage(job_build_user, jobName, branchName, environment, deploymentType = null) {
  // Clean up user display - remove if system user or show clean name
  def userDisplay = (job_build_user == 'jenkins-system' || job_build_user == 'anonymous') ? 
                   '' : "${job_build_user} "
  
  def message = "${jobName} - #${env.BUILD_NUMBER} Started"
  if (userDisplay) {
    message += " by user ${userDisplay.trim()}"
  }
  message += " (${environment})"
  
  if (deploymentType && deploymentType != 'both') {
    message += " - ${deploymentType}"
  }
  
  slackSend(
    channel: '#bc-jenkins',
    message: message,
    color: '#439FE0', // Blue color for start messages
    teamDomain: 'evdy-pp',
    tokenCredentialId: 'jenkins-build-react-graphql'
  )
}

def sendJobCompletionMessage(job_build_user, jobName, branchName, environment, result, deploymentType = null, childResults = null, childResultsWithLinks = null) {
  // Clean up user display - remove if system user or show clean name
  def userDisplay = (job_build_user == 'jenkins-system' || job_build_user == 'anonymous') ? 
                   '' : "${job_build_user} "
  
  def message = "${jobName} - #${env.BUILD_NUMBER} ${result}"
  if (userDisplay) {
    message += " by user ${userDisplay.trim()}"
  }
  message += " (${environment})"
  
  if (deploymentType && deploymentType != 'both') {
    message += " - ${deploymentType}"
  }
  
  // Add duration if available
  def duration = currentBuild.durationString?.replace(' and counting', '')
  if (duration) {
    message += " after ${duration}"
  }
  
  // Add child job results in simple format
  if (childResultsWithLinks) {
    message += "\nComponents:"
    childResultsWithLinks.each { component, info ->
      message += "\n${component}: ${info.result}"
    }
  } else if (childResults) {
    message += "\nComponents:"
    childResults.each { component, componentResult ->
      message += "\n${component}: ${componentResult ?: 'Not run'}"
    }
  }
  
  // Determine color based on result
  def color = result == 'SUCCESS' ? 'good' : 'danger'
  
  slackSend(
    channel: '#bc-jenkins',
    message: message,
    color: color,
    teamDomain: 'evdy-pp',
    tokenCredentialId: 'jenkins-build-react-graphql'
  )
}

def sendStepMessage(job_build_user, jobName, stepName, buildNumber) {
  def message = "${jobName} - #${buildNumber}"
  message += "\nStep: ${stepName}"
  
  slackSend(
    channel: '#bc-jenkins',
    message: message,
    color: '#439FE0', // Blue color for step messages
    teamDomain: 'evdy-pp',
    tokenCredentialId: 'jenkins-build-react-graphql'
  )
}

return this
