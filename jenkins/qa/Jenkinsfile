@Library('global-jenkins-lib')_

// Pa<PERSON> job for QA environment - triggers both ECS and SAM deployments
properties([
  buildDiscarder(
    logRotator(
      artifactDaysToKeepStr: '', 
      artifactNumToKeepStr: '', 
      daysToKeepStr: '', 
      numToKeepStr: '10'
    )
  ), 
  disableResume(),
  parameters([
    gitParameter(
      branch: '',
      branchFilter: 'origin/(.*)',
      defaultValue: 'active',
      description: '',
      name: 'GIT_BRANCH_NAME',
      quickFilterEnabled: false,
      selectedValue: 'NONE',
      sortMode: 'ASCENDING_SMART',
      tagFilter: '*',
      type: 'PT_BRANCH_TAG',
      useRepository: "**************:babycenter/bc-svc-bookmark-notif.git"
    ),
    choice(
      choices: ['both', 'ecs', 'sam'],
      description: 'Choose which deployments to run',
      name: 'DEPLOYMENT_TYPE',
      defaultValue: 'both'
    )
  ])
])

node {
  def PROJECT_NAME = 'bc-svc-bookmark-notif'
  def job_build_user = ''
  def currentResult = ''
  def ecsResult = ''
  def samResult = ''
  def ecsJobInfo = null
  def samJobInfo = null
  def shouldDeployEcs = false
  def shouldDeploySam = false
  
  // Checkout code first to get access to slack-messaging.groovy
  checkout scmGit(
    branches: [[name: "origin/${params.GIT_BRANCH_NAME}"]], 
    extensions: [[$class: 'WipeWorkspace']], 
    userRemoteConfigs: [[
      credentialsId: "jenkins-key-14-feb-2017", 
      url: "**************:babycenter/bc-svc-bookmark-notif.git"
    ]]
  )
  
  // Load Slack messaging utilities
  def slackUtils = load 'jenkins/slack-messaging.groovy'
  
  wrap([$class: 'BuildUser']) {
    job_build_user = env.BUILD_USER?.replaceAll(" ", "_") ?: 'anonymous'
  }

  stage('Initialize') {
    echo "Starting ${PROJECT_NAME} QA deployment orchestration..."
    echo "Git branch: ${params.GIT_BRANCH_NAME}"
    echo "Deployment type: ${params.DEPLOYMENT_TYPE}"
    echo "UPDATED: Using boolean variables for deployment control"
    
    // Determine what to deploy based on DEPLOYMENT_TYPE parameter
    echo "DEBUG: DEPLOYMENT_TYPE value = '${params.DEPLOYMENT_TYPE}'"
    echo "DEBUG: DEPLOYMENT_TYPE class = ${params.DEPLOYMENT_TYPE.getClass()}"
    
    switch(params.DEPLOYMENT_TYPE) {
      case 'ecs':
        echo "DEBUG: Matched 'ecs' case"
        shouldDeployEcs = true
        shouldDeploySam = false
        break
      case 'sam':
        echo "DEBUG: Matched 'sam' case"
        shouldDeployEcs = false
        shouldDeploySam = true
        break
      case 'both':
        echo "DEBUG: Matched 'both' case"
        shouldDeployEcs = true
        shouldDeploySam = true
        break
      default:
        echo "DEBUG: Matched 'default' case"
        shouldDeployEcs = true
        shouldDeploySam = true
        break
    }
    
    echo "Should deploy ECS: ${shouldDeployEcs}"
    echo "Should deploy SAM: ${shouldDeploySam}"
  }

  stage('Slack Start') {
    slackUtils.sendJobStartMessage(
      job_build_user, 
      PROJECT_NAME, 
      params.GIT_BRANCH_NAME, 
      'qa', 
      params.DEPLOYMENT_TYPE
    )
  }

  try {
    echo "DEBUG: Checking deployment conditions..."
    echo "DEBUG: shouldDeployEcs = ${shouldDeployEcs}"
    echo "DEBUG: shouldDeploySam = ${shouldDeploySam}"
    echo "DEBUG: Both check: ${shouldDeployEcs && shouldDeploySam}"
    
    // Run deployments in parallel if both are enabled
    if (shouldDeployEcs && shouldDeploySam) {
      stage('Deploy Both (Parallel)') {
        parallel(
          "ECS Deployment": {
            try {
              echo "Triggering ECS deployment..."
              def ecsJob = build job: 'bc-svc-bookmark-notif-qa-ecs',
                parameters: [
                  string(name: 'GIT_BRANCH_NAME', value: params.GIT_BRANCH_NAME)
                ],
                wait: true,
                propagate: false
              
              ecsJobInfo = [
                buildNumber: ecsJob.number,
                buildUrl: ecsJob.absoluteUrl,
                result: ecsJob.result,
                duration: ecsJob.duration
              ]
              ecsResult = ecsJob.result
              echo "ECS deployment result: ${ecsResult}"
              echo "ECS build URL: ${ecsJobInfo.buildUrl}"
              
              // Add build link to Jenkins dashboard
              currentBuild.description = (currentBuild.description ?: '') + 
                "<br/>ECS: <a href='${ecsJobInfo.buildUrl}'>#${ecsJobInfo.buildNumber}</a> (${ecsResult})"
              
              if (ecsResult != 'SUCCESS') {
                error "ECS deployment failed with result: ${ecsResult}"
              }
            } catch (Exception e) {
              ecsResult = 'FAILURE'
              echo "ECS deployment failed: ${e.message}"
              throw e
            }
          },
          "SAM Deployment": {
            try {
              echo "Triggering SAM deployment..."
              def samJob = build job: 'bc-svc-bookmark-notif-qa-sam',
                parameters: [
                  string(name: 'GIT_BRANCH_NAME', value: params.GIT_BRANCH_NAME),
                  booleanParam(name: 'SEND_SLACK_MESSAGES', value: false)
                ],
                wait: true,
                propagate: false
              
              samJobInfo = [
                buildNumber: samJob.number,
                buildUrl: samJob.absoluteUrl,
                result: samJob.result,
                duration: samJob.duration
              ]
              samResult = samJob.result
              echo "SAM deployment result: ${samResult}"
              echo "SAM build URL: ${samJobInfo.buildUrl}"
              
              // Add build link to Jenkins dashboard
              currentBuild.description = (currentBuild.description ?: '') + 
                "<br/>SAM: <a href='${samJobInfo.buildUrl}'>#${samJobInfo.buildNumber}</a> (${samResult})"
              
              if (samResult != 'SUCCESS') {
                error "SAM deployment failed with result: ${samResult}"
              }
            } catch (Exception e) {
              samResult = 'FAILURE'
              echo "SAM deployment failed: ${e.message}"
              throw e
            }
          }
        )
      }
    } else {
      // Run deployments sequentially if only one is enabled
      if (shouldDeployEcs) {
        stage('Deploy ECS') {
          try {
            echo "Triggering ECS deployment..."
            def ecsJob = build job: 'bc-svc-bookmark-notif-qa-ecs',
              parameters: [
                string(name: 'GIT_BRANCH_NAME', value: params.GIT_BRANCH_NAME)
              ],
              wait: true,
              propagate: false
            
            ecsJobInfo = [
              buildNumber: ecsJob.number,
              buildUrl: ecsJob.absoluteUrl,
              result: ecsJob.result,
              duration: ecsJob.duration
            ]
            ecsResult = ecsJob.result
            echo "ECS deployment result: ${ecsResult}"
            echo "ECS build URL: ${ecsJobInfo.buildUrl}"
            
            // Add build link to Jenkins dashboard
            currentBuild.description = (currentBuild.description ?: '') + 
              "<br/>ECS: <a href='${ecsJobInfo.buildUrl}'>#${ecsJobInfo.buildNumber}</a> (${ecsResult})"
            
            if (ecsResult != 'SUCCESS') {
              error "ECS deployment failed with result: ${ecsResult}"
            }
          } catch (Exception e) {
            ecsResult = 'FAILURE'
            echo "ECS deployment failed: ${e.message}"
            throw e
          }
        }
      }
      
      if (shouldDeploySam) {
        stage('Deploy SAM') {
          try {
            echo "Triggering SAM deployment..."
            def samJob = build job: 'bc-svc-bookmark-notif-qa-sam',
              parameters: [
                string(name: 'GIT_BRANCH_NAME', value: params.GIT_BRANCH_NAME),
                booleanParam(name: 'SEND_SLACK_MESSAGES', value: false)
              ],
              wait: true,
              propagate: false
            
            samJobInfo = [
              buildNumber: samJob.number,
              buildUrl: samJob.absoluteUrl,
              result: samJob.result,
              duration: samJob.duration
            ]
            samResult = samJob.result
            echo "SAM deployment result: ${samResult}"
            echo "SAM build URL: ${samJobInfo.buildUrl}"
            
            // Add build link to Jenkins dashboard
            currentBuild.description = (currentBuild.description ?: '') + 
              "<br/>SAM: <a href='${samJobInfo.buildUrl}'>#${samJobInfo.buildNumber}</a> (${samResult})"
            
            if (samResult != 'SUCCESS') {
              error "SAM deployment failed with result: ${samResult}"
            }
          } catch (Exception e) {
            samResult = 'FAILURE'
            echo "SAM deployment failed: ${e.message}"
            throw e
          }
        }
      }
    }
    
    currentResult = 'SUCCESS'
    
    stage('Deployment Summary') {
      echo "=== DEPLOYMENT SUMMARY ==="
      echo "Environment: QA"
      echo "Branch: ${params.GIT_BRANCH_NAME}"
      echo "Deployment Type: ${params.DEPLOYMENT_TYPE}"
      
      if (ecsJobInfo) {
        echo "ECS Deployment:"
        echo "  - Build: #${ecsJobInfo.buildNumber}"
        echo "  - Result: ${ecsJobInfo.result}"
        echo "  - URL: ${ecsJobInfo.buildUrl}"
        echo "  - Duration: ${ecsJobInfo.duration}ms"
      }
      
      if (samJobInfo) {
        echo "SAM Deployment:"
        echo "  - Build: #${samJobInfo.buildNumber}"
        echo "  - Result: ${samJobInfo.result}"
        echo "  - URL: ${samJobInfo.buildUrl}"
        echo "  - Duration: ${samJobInfo.duration}ms"
      }
      
      // Create comprehensive build description
      def description = "QA Deployment (${params.DEPLOYMENT_TYPE}) - Branch: ${params.GIT_BRANCH_NAME}"
      
      if (ecsJobInfo) {
        def ecsIcon = ecsJobInfo.result == 'SUCCESS' ? '✅' : '❌'
        description += " | ECS: #${ecsJobInfo.buildNumber} (${ecsJobInfo.result})"
      }
      
      if (samJobInfo) {
        def samIcon = samJobInfo.result == 'SUCCESS' ? '✅' : '❌'
        description += " | SAM: #${samJobInfo.buildNumber} (${samJobInfo.result})"
      }
      
      currentBuild.description = description
      currentBuild.displayName = "#${env.BUILD_NUMBER} - ${params.DEPLOYMENT_TYPE} (${params.GIT_BRANCH_NAME})"
    }
    
  } catch (e) {
    echo 'Orchestration failed'
    currentResult = 'FAILURE'
    throw e
  } finally {
    echo "===currentBuild.currentResult : ${currentBuild.currentResult}"
    echo "===currentBuild.result: ${currentBuild.result}"

    if (currentResult != 'FAILURE') {
      currentResult = currentBuild.currentResult
    }

    // Build summary of child job results with links
    def childResults = [:]
    if (shouldDeployEcs) {
      childResults['ECS'] = ecsResult
    }
    if (shouldDeploySam) {
      childResults['SAM'] = samResult
    }

    // Create enhanced child results with job links for Slack
    def childResultsWithLinks = [:]
    if (ecsJobInfo) {
      childResultsWithLinks['ECS'] = [
        result: ecsJobInfo.result,
        url: ecsJobInfo.buildUrl,
        buildNumber: ecsJobInfo.buildNumber
      ]
    }
    if (samJobInfo) {
      childResultsWithLinks['SAM'] = [
        result: samJobInfo.result,
        url: samJobInfo.buildUrl,
        buildNumber: samJobInfo.buildNumber
      ]
    }

    slackUtils.sendJobCompletionMessage(
      job_build_user,
      PROJECT_NAME,
      params.GIT_BRANCH_NAME,
      'qa',
      currentResult,
      params.DEPLOYMENT_TYPE,
      childResults,
      childResultsWithLinks
    )
      
    cleanWs()
  }
}
