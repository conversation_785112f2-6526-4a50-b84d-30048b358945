@Library('ecs_modules') _

properties([
    githubProjectProperty('https://github.com/babycenter/bc-svc-bookmark-notif'),

    parameters([
        gitParameter(
            branch: '',
            branchFilter: 'origin/(.*)',
            defaultValue: 'active',
            description: '',
            name: 'GIT_BRANCH_NAME',
            quickFilterEnabled: false,
            selectedValue: 'NONE',
            sortMode: 'ASCENDING_SMART',
            tagFilter: '*',
            type: 'PT_BRANCH_TAG',
            useRepository: "**************:babycenter/bc-svc-bookmark-notif.git"
        )
    ])
])

jenkinsPipelineBC(
  deployBranch: "${params.GIT_BRANCH_NAME}".toString(),
  gethubRepo: "**************:babycenter/bc-svc-bookmark-notif.git",
  deployLevel: "FULL",
  deployEnv: "qa",
  awsRegion: "us-east-1",
  githubCredID: "jenkins-key-14-feb-2017",
  agentName: "default",
  configFile: "jenkins/qa/ecs/config.properties",
  tagFile: "jenkins/qa/ecs/tags.properties",
  envFile: "jenkins/qa/ecs/env-info.properties",
  dockerFile: "Dockerfile",
  initScript: "bash jenkins/node-version-set.sh 'npm ci'; bash jenkins/node-version-set.sh 'npm run build:ecs'",
  healthcheckEnv: "",
  healthcheck: false,
  slackLevel: false,
  slackChannel: "bc-jenkins",
  nodeVersion: "",
  archiveFiles: "",
  nodeBuildSteps: ""
)

