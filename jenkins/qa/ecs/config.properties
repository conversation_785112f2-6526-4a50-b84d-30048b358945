clusterName=bc-ue1-lq-bookmark
taskFamily=bc-ue1-lq-bookmark-service
serviceName=bc-ue1-lq-bookmark-service
ecsTaskRole=arn:aws:iam::762359486493:role/bc-ecs-qa-bookmarkservice
ecsExecuteRole=arn:aws:iam::762359486493:role/ecsTaskExecutionRole
buName=bc
logGroup=/bc/ue1/ecs/qa/bookmarkservice/
efsID=
taskMemory=1024
taskCPU=512
ecsRepoBase=762359486493.dkr.ecr.us-east-1.amazonaws.com
ecsRepo=pp-svcs-bookmarkservice
ecrRetentionDays=15
containerPort=4000
hostPort=4000
serviceType=fargate
desiredCount=1
awsRegion=us-east-1
retentionDays=3
dockerFile=docker/Dockerfile
subnets=subnet-f1a21087,subnet-ee04c4c4,subnet-806db8d8,subnet-9847dfa5 
securityGroups=sg-05ebdfe129f38677a
healthcheck_path=/health
awsProfile=jenkins-eng-qa
targetGroup=arn:aws:elasticloadbalancing:us-east-1:762359486493:targetgroup/bc-ue1-lq-bookmark-ecs-ext-tg/a5093ce5b9e4ee98,arn:aws:elasticloadbalancing:us-east-1:762359486493:targetgroup/bc-ue1-lq-bookmark-ecs-int-tg/104329d0408cb4ff
task_healthcheck=true,30,5,3,15