@Library('global-jenkins-lib')_

// Define properties and parameters for QA environment
properties([
  buildDiscarder(
    logRotator(
      artifactDaysToKeepStr: '', 
      artifactNumToKeepStr: '', 
      daysToKeepStr: '', 
      numToKeepStr: '10'
    )
  ), 
  disableResume(),
  parameters([
    gitParameter(
      branch: '',
      branchFilter: 'origin/(.*)',
      defaultValue: 'active',
      description: '',
      name: 'GIT_BRANCH_NAME',
      quickFilterEnabled: false,
      selectedValue: 'NONE',
      sortMode: 'ASCENDING_SMART',
      tagFilter: '*',
      type: 'PT_BRANCH_TAG',
      useRepository: "**************:babycenter/bc-svc-bookmark-notif.git"
    ),
    booleanParam(
      defaultValue: true,
      description: 'Send Slack notifications (set to false when called from parent job)',
      name: 'SEND_SLACK_MESSAGES'
    )
  ])
])

node {
  // Checkout code first to get access to jenkins-common.groovy
  checkout scmGit(
    branches: [[name: "origin/${params.GIT_BRANCH_NAME}"]], 
    extensions: [[$class: 'WipeWorkspace']], 
    userRemoteConfigs: [[
      credentialsId: "jenkins-key-14-feb-2017", 
      url: "**************:babycenter/bc-svc-bookmark-notif.git"
    ]]
  )
  
  def common = load 'jenkins/jenkins-common.groovy'
  common.runPipeline('qa', params.SEND_SLACK_MESSAGES)
}
