#!/bin/bash
# Setup Node.js environment for this command
command=$1
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "${NODE_VERSION}" -lt 22 ]; then
        echo "Node.js $(node --version) found but version is too old (need v22+)"
        if [ -s "${HOME}/.nvm/nvm.sh" ]; then
            . "${HOME}/.nvm/nvm.sh"
            nvm use 22
        fi
    fi
else
    echo "Node.js not found, activating via nvm"
    if [ -s "${HOME}/.nvm/nvm.sh" ]; then
        . "${HOME}/.nvm/nvm.sh"
        nvm use 22
    fi
fi

# Verify Node.js is available
echo "Using Node.js version: $(node --version)"
echo "Using npm version: $(npm --version)"

# Install esbuild globally if not already installed
if ! command -v esbuild >/dev/null 2>&1; then
    echo "Installing esbuild@0.18.2 globally..."
    npm install -g esbuild@0.18.2
else
    echo "esbuild is already installed: $(esbuild --version)"
fi

# Execute the command
${command}
