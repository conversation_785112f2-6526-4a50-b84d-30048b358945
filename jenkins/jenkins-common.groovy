// jenkins-common.groovy
// Common Jenkins pipeline functions and configurations

def nodeCommand(command) {
  sh """#!/bin/bash
    # Use the node-version-set.sh script to setup Node.js and execute command
    chmod +x jenkins/node-version-set.sh
    jenkins/node-version-set.sh "${command}"
  """
}


def runPipeline(environment, sendSlackMessages = true) {
  def PROJECT_NAME = 'bc-svc-bookmark-notif'
  def currentResult = ''
  def job_build_user = ''
  
  // Load Slack messaging utilities if Slack is enabled
  def slackUtils = null
  if (sendSlackMessages) {
    slackUtils = load 'jenkins/slack-messaging.groovy'
  }
  
  // Try to configure Node.js tool, fallback to alternative if not available
  def nodejs = ''
  try {
    nodejs = tool name: 'NodeJS-22', type: 'nodejs'
  } catch (Exception e) {
    echo "NodeJS tool not configured, will attempt alternative Node.js setup"
  }
  
  wrap([$class: 'BuildUser']) {
    job_build_user = env.BUILD_USER?.replaceAll(" ", "_") ?: 'jenkins-system'
  }
  
  // Additional fallback for user detection
  if (job_build_user == 'jenkins-system' || job_build_user == 'anonymous' || !job_build_user) {
    // Try alternative methods to get user info
    try {
      job_build_user = env.BUILD_USER_ID ?: env.USER ?: 'jenkins-system'
    } catch (Exception e) {
      job_build_user = 'jenkins-system'
    }
  }

  stage('Initialize') {
    echo "Starting SAM deployment of ${PROJECT_NAME} to ${environment}..."
    echo "Git branch: ${params.GIT_BRANCH_NAME}"
    echo "AWS Profile: ${env.AWS_PROFILE}"
    echo "AWS Region: ${env.AWS_REGION}"
    
    // Environment-specific AWS configuration
    switch(environment) {
      case 'qa':
        env.AWS_ACCOUNT_ID = '************'
        env.AWS_REGION = 'us-east-1'
        env.AWS_PROFILE = 'jenkins-eng-qa'
        env.SAM_CONFIG_ENV = 'qa'
        break
      case 'stag':
        env.AWS_ACCOUNT_ID = '************'
        env.AWS_REGION = 'us-west-2'
        env.AWS_PROFILE = 'jenkins-eng-stag'
        env.SAM_CONFIG_ENV = 'stag'
        break
      case 'prod':
        env.AWS_ACCOUNT_ID = '************'
        env.AWS_REGION = 'us-west-2'
        env.AWS_PROFILE = 'jenkins-eng-prod'
        env.SAM_CONFIG_ENV = 'prod'
        break
    }
    
    env.ENVIRONMENT = environment
  }

  stage('Slack Start') {
    if (sendSlackMessages && slackUtils) {
      slackUtils.sendJobStartMessage(
        job_build_user, 
        "${PROJECT_NAME} SAM", 
        params.GIT_BRANCH_NAME, 
        environment
      )
    }
  }

  try {
    stage('Capture Git Info') {
      // Capture the Git commit hash (checkout already done in env-specific file)
      env.GIT_COMMIT = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
      echo "Git commit: ${env.GIT_COMMIT}"
    }

    stage('Install Dependencies') {
      if (nodejs) {
        withEnv(["PATH+NODE=${nodejs}/bin:${env.PATH}"]) {
          sh 'node --version'
          sh 'npm --version'
          sh 'npm ci'
        }
      } else {
        nodeCommand('node --version')
        nodeCommand('npm --version') 
        nodeCommand('npm ci')
      }
    }

    stage('Build SAM Application') {
      if (nodejs) {
        withEnv(["PATH+NODE=${nodejs}/bin:${env.PATH}"]) {
          sh 'npm run build:sam'
        }
      } else {
        nodeCommand('npm run build:sam')
      }
    }

    stage('Deploy SAM Application') {
      script {
        echo "Deploying SAM application to ${environment}..."
        
        // Deploy using SAM with samconfig.toml configuration
        sh """
          sam deploy \\
            --config-env ${env.SAM_CONFIG_ENV} \\
            --profile ${env.AWS_PROFILE} \\
            --no-confirm-changeset \\
            --no-fail-on-empty-changeset
        """
        
        echo "SAM deployment completed successfully!"
        
        // Get stack outputs for verification (using stack name from samconfig.toml)
        def stackName = environment == 'qa' ? 'bc-svc-bookmark-notif-qa' : 
                       environment == 'stag' ? 'bc-svc-bookmark-notif-stag' : 
                       'bc-svc-bookmark-notif-prod'
        
        sh """
          echo "Getting stack outputs..."
          aws --profile ${env.AWS_PROFILE} cloudformation describe-stacks \\
            --stack-name ${stackName} \\
            --region ${env.AWS_REGION} \\
            --query 'Stacks[0].Outputs' \\
            --output table || true
        """
      }
    }
  } catch (e) {
    echo 'Run failed'
    currentResult = 'FAILURE'
    throw e
  } finally {
    echo "===currentBuild.currentResult : ${currentBuild.currentResult}"
    echo "===currentBuild.result: ${currentBuild.result}"

    if (currentResult != 'FAILURE') {
      currentResult = currentBuild.currentResult
    }

    def previousResult = currentBuild.getPreviousBuild()?.result
    if (previousResult != null && previousResult != currentResult) {
      echo "Pipeline state changed"
    }

    if (sendSlackMessages && slackUtils) {
      slackUtils.sendJobCompletionMessage(
        job_build_user,
        "${PROJECT_NAME} SAM",
        params.GIT_BRANCH_NAME,
        environment,
        currentResult
      )
    }
      
    cleanWs()
  }
}

return this
