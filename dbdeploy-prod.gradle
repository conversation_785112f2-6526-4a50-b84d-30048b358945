//
// gradle buildscript for dbdeploy task
//
// to run the update scripts, run this command from the project's root folder;
//   gradle update -b dbdeploy-qa.gradle
//

//
// load url, user and password from secure properties
//
Properties dbprops = new Properties()
dbprops.load(new FileInputStream('secure/dbdeploy-prod.properties'))

buildscript {
	repositories {
		maven {
			url "https://plugins.gradle.org/m2/"
			jcenter()
		}
	}
	dependencies {
		classpath "gradle.plugin.com.lv.plugins:gradle-dbdeploy-plugin:1.2"
		classpath 'mysql:mysql-connector-java:8.0.30'
	}
}
apply plugin: "com.lv.dbdeploy"


//
// dbdeploy config
//
dbdeploy {
	scriptdirectory = file('src/main/sql/dbdeploy/bookmarks')
	driver = 'com.mysql.jdbc.Driver'
	url = dbprops.dburl
	userid = dbprops.dbuser
	password = dbprops.dbpass
	outputfile = file('src/main/sql/dbdeploy/output.sql')
	undoOutputfile = file('src/main/sql/dbdeploy/undooutput.sql')
	dbms = 'mysql'
}

