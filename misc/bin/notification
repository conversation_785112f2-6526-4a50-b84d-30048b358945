#!/bin/bash
#
# notification       Start/Stop Notification Services
#
#
# chkconfig: - 80 05
# description: Enable notification services by Babycenter LLC
#


# the base directory
NOTIFICATION_DIR="/var/local/data/notification-services"
CURRENT_DIR=`pwd`

# run the instance as user


case "$1" in
    start)
        echo "Starting Notification Services ..."
        cd $NOTIFICATION_DIR
        java -jar $NOTIFICATION_DIR/notification.jar >/dev/null 2>&1 &
		cd $CURRENT_DIR
        ;;
     stop)
        echo "Shutting down Notification Services ..."
        curl -X POST http://localhost:8080/admin/shutdown
        ;;
    restart)
        $0 stop
        $0 start
        ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
    exit 1
 ;;
esac