{"TableName": "Notification", "AttributeDefinitions": [{"AttributeName": "UserHashKey", "AttributeType": "S"}, {"AttributeName": "ContentRangeKey", "AttributeType": "S"}, {"AttributeName": "DateNumber", "AttributeType": "N"}], "GlobalSecondaryIndexUpdates": [{"Create": {"IndexName": "DateNumber-index", "KeySchema": [{"AttributeName": "DateNumber", "KeyType": "HASH"}], "Projection": {"ProjectionType": "ALL"}, "ProvisionedThroughput": {"ReadCapacityUnits": 100, "WriteCapacityUnits": 100}}}]}