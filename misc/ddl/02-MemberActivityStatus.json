{"TableName": "MemberActivityStatus", "AttributeDefinitions": [{"AttributeName": "UserHashKey", "AttributeType": "S"}, {"AttributeName": "DateNumber", "AttributeType": "N"}], "KeySchema": [{"AttributeName": "UserHashKey", "KeyType": "HASH"}], "ProvisionedThroughput": {"ReadCapacityUnits": 50, "WriteCapacityUnits": 50}, "GlobalSecondaryIndexes": [{"IndexName": "DateNumber-index", "KeySchema": [{"AttributeName": "DateNumber", "KeyType": "HASH"}], "Projection": {"ProjectionType": "KEYS_ONLY"}, "ProvisionedThroughput": {"ReadCapacityUnits": 100, "WriteCapacityUnits": 100}}]}