{"TableName": "Notification", "AttributeDefinitions": [{"AttributeName": "UserHashKey", "AttributeType": "S"}, {"AttributeName": "ContentRangeKey", "AttributeType": "S"}, {"AttributeName": "DateNumber", "AttributeType": "N"}], "KeySchema": [{"AttributeName": "UserHashKey", "KeyType": "HASH"}, {"AttributeName": "ContentRangeKey", "KeyType": "RANGE"}], "ProvisionedThroughput": {"ReadCapacityUnits": 100, "WriteCapacityUnits": 25}, "GlobalSecondaryIndexes": [{"IndexName": "DateNumber-index", "KeySchema": [{"AttributeName": "DateNumber", "KeyType": "HASH"}], "Projection": {"ProjectionType": "KEYS_ONLY"}, "ProvisionedThroughput": {"ReadCapacityUnits": 25, "WriteCapacityUnits": 50}}]}