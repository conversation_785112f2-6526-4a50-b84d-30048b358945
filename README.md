# Bookmark & Notification Service

A TypeScript/Node.js API service for managing user bookmarks and notifications with MySQL and DynamoDB backends.

## Quick Start

```bash
# Install dependencies
npm install

# Start all services with Docker (MySQL database + migrations)
npm run docker:up

# Run the service locally (development mode)
npm run dev
```

The API will be available at `http://localhost:4000`

## Development Setup

### Prerequisites

- Node.js 22+ and npm
- Docker and Docker Compose
- TypeScript knowledge

### Complete Setup Process

1. **Clone the repository**:

   ```bash
   git clone https://github.com/babycenter/bc-svc-bookmark-notif.git
   cd bc-svc-bookmark-notif
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Start the database and run migrations**:

   ```bash
   npm run docker:up
   ```

   This starts MySQL and automatically runs all migrations.

4. **DynamoDB tables** are automatically created by the Docker setup from configurations in `tests/database/dynamodb/tables/`.

5. **Start the development server**:

   ```bash
   npm run dev
   ```

6. **Run tests**:

   ```bash
   # Unit tests
   npm test

   # Integration tests (requires running database)
   npm run test:integration
   ```

## Architecture

This unified service uses a dual-database architecture to handle two distinct domains:

**Bookmark Management:**

- **MySQL**: Stores user bookmark data with relational integrity
- **Features**: CRUD operations, pagination, batch processing, content type support

**Notification Management:**

- **DynamoDB**: Stores notification data with automatic TTL cleanup
- **Features**: Multi-version API (V1/V2/V3), real-time event processing, subscription management

**Shared Infrastructure:**

- **Authentication**: Unified JWT-based auth for both domains
- **Event Publishing**: SNS integration for downstream processing
- **SQS Processing**: Lambda functions for community and subscription events

The development environment includes both databases running in Docker containers, with automatic setup and configuration.

## Docker Setup

The Docker Compose configuration includes:

- **MySQL database** (port 3307)
- **DynamoDB Local** (port 8000)
- **Migrations service** that automatically runs all SQL files in the `/migrations` folder
- **DynamoDB setup service** that creates tables based on the config in `tests/database/dynamodb/tables/`
- **API service** (optional, can be run locally with `npm run dev`)

**Note**: MySQL uses port 3307 instead of the standard 3306 to avoid conflicts with existing MySQL installations.

### Docker Compose Commands

```bash
# Start all services in detached mode
npm run docker:up

# View logs from all services
npm run docker:logs

# Stop all services
npm run docker:down

# Rebuild containers (if you make changes to Dockerfile)
docker compose build
docker compose up -d
```

## Database Migrations

Migrations are managed through SQL files in the `/migrations` directory:

- Files are executed in alphabetical/numerical order
- New migrations can be added by creating a SQL file with a timestamp prefix
- Migrations run automatically when starting the Docker environment

To run MySQL migrations manually:

```bash
# For development environment
npm run knex:migrate:dev

# For other environments
npm run knex:migrate:qa    # QA environment
npm run knex:migrate:stag  # Staging environment
npm run knex:migrate:prod  # Production environment
```

## Database Structure

### MySQL Database

- **Schema**: Database schema is defined in SQL migrations under the `/migrations` directory
- **Seed Data**: Test data is available in `tests/database/mysql/seed/*.sql`

### DynamoDB Setup

- **Table Definitions**: Stored as JSON in `tests/database/dynamodb/tables/*.json`
- **Seed Data**: Test data is stored in `tests/database/dynamodb/seed/*.json`
- **Setup Script**: `tests/database/dynamodb/setup.js` automatically creates tables and loads seed data

To modify table configurations:

1. Edit `tests/database/dynamodb/tables/*.json` to change table structure
2. Restart the Docker setup with `npm run docker:down` and `npm run docker:up`

For connecting to the local DynamoDB:

```bash
# DynamoDB local endpoint
http://localhost:8000

# AWS CLI example to list tables
aws dynamodb list-tables --endpoint-url http://localhost:8000

# AWS CLI example to scan a table
aws dynamodb scan --table-name notification-dev --endpoint-url http://localhost:8000
```

## Environment Setup

Copy `.env.example` to `.env` and configure for local development:

```bash
cp .env.example .env
```

Key environment variables:

- `ENVIRONMENT`: Environment (qa/stag/prod) (dev for local development)
- `PORT`: Server port (default: 4000)
- `DB_HOST`, `DB_USER`, `DB_PASS`, `DB_NAME`, `DB_PORT`: MySQL connection details
- `BC_AUTH_SIGNATURE`: Auth signature verification (file or text-based)
- `BC_AUTH_BASE_URL`: Base URL for authentication service
- `DYNAMODB_*`: DynamoDB configuration for notifications
- `AWS_SECRETS_REGION`: Region for AWS Secrets Manager
- `US_MEMBERS_EVENTS_SNS_ARN`: SNS topic ARN for member events

## Build System

This project has separate build configurations for API and Lambda functions:

### Build Commands

- `npm run build` - Build both API and Lambda (runs both build:api and build:lambda)
- `npm run build:api` - Build API files to `build/` folder
- `npm run build:lambda` - Build Lambda files to `dist/` folder

### Build Output Structure

- **`build/` folder**: Contains compiled API/server files (TypeScript → JavaScript)
  - All Express.js server components
  - Routes, middleware, services, database connections
  - Excludes Lambda handlers
- **`dist/` folder**: Contains compiled Lambda functions only
  - Lambda event handlers and processors
  - Lambda-specific utilities and types
  - Excludes API/server components

### TypeScript Configurations

- `tsconfig.json` - Main config for API build (excludes `src/lambda/**/*`)
- `tsconfig.lambda.json` - Lambda-specific config (includes only `src/lambda/**/*.ts` and shared types)

## Scripts

- `npm run dev` - Start development server
- `npm start` - Start production server
- `npm run build` - Build both API and Lambda
- `npm run build:api` - Build API files only
- `npm run build:lambda` - Build Lambda files only
- `npm test` - Run unit tests
- `npm run test:coverage` - Run tests with coverage
- `npm run test:integration` - Run integration tests
- `npm run lint` - Lint and fix code
- `npm run prettier` - Format code with Prettier
- `npm run docker:up` - Start Docker services
- `npm run docker:down` - Stop Docker services
- `npm run docker:logs` - View Docker container logs
- `npm run docker:rebuild` - Rebuild Docker containers
- `npm run knex:migrate:dev` - Run database migrations (development)
- `npm run knex:migrate:qa` - Run database migrations (QA)
- `npm run knex:migrate:stag` - Run database migrations (staging)
- `npm run knex:migrate:prod` - Run database migrations (production)

## Service Access & URL Routing

This consolidated service is accessible through existing DNS endpoints:

### Service URLs:
- **Bookmark Service**: `https://bc-bookmarks.babycenter.com`
- **Notification Service**: `https://bc-notifications.babycenter.com`

### API Endpoint Examples:
```bash
# Bookmark operations
GET https://bc-bookmarks.babycenter.com/bookmark/v2/member/{memberId}

# Notification operations  
GET https://bc-notifications.babycenter.com/v3/user/notifications
```

Both URLs serve the same consolidated service, providing access to all bookmark and notification endpoints.

## API Documentation

This service provides a comprehensive OpenAPI/Swagger specification at `swagger/openapi.yaml`. The API includes:

### Bookmark Endpoints

- **Single Bookmark Operations**

  - `GET /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}` - Get single bookmark
  - `POST /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}` - Save bookmark
  - `DELETE /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}` - Delete bookmark
  - `POST /bookmark/v1/member/{memberId}/content` - Save bookmark (alternative endpoint)

- **Member Bookmark Lists**

  - `GET /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}` - List bookmarks
  - `GET /bookmark/v2/member/{memberId}/content/{contentSource}/{contentType}` - List bookmarks (paginated)
  - `DELETE /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}` - Batch delete bookmarks by content IDs
  - `GET /bookmark/member/{memberId}` - Get all bookmarks for a member
  - `GET /bookmark/member/{memberId}/content/{contentSource}` - Get bookmarks by content source
  - `POST /bookmark/member/{memberId}` - Batch save multiple bookmarks

- **Batch Operations (Internal Services)**
  - `GET /bookmark/v1/content/{contentSource}/{contentType}/{contentId}` - Get by content
  - `DELETE /bookmark/v1/content/{contentSource}/{contentType}/{contentId}` - Delete by content
  - `GET /bookmark/v1/content/{contentSource}/contentType/{contentType}` - Get by content type

### Notification Endpoints

This service provides three API versions for backward compatibility:

- **V1** - No auth header required

  - `GET /v1/user/notifications` - List notifications
  - `GET /v1/user/acknowledge` - Acknowledge notifications

- **V2** - With auth header

  - `GET /v2/user/notifications` - List notifications
  - `GET /v2/user/acknowledge` - Acknowledge notifications
  - `POST /v2/user/read` - Mark specific notification as read
  - `POST /v2/user/readall` - Mark all notifications as read

- **V3** - Dual user system support (user key + global auth ID)
  - `GET /v3/user/notifications` - List notifications
  - `GET /v3/user/acknowledge` - Acknowledge notifications
  - `POST /v3/user/read` - Mark specific notification as read
  - `POST /v3/user/readall` - Mark all notifications as read

## Testing

### Unit Tests

Unit tests use Jest:

```bash
npm test
```

### Integration Tests

Integration tests use a separate test setup:

```bash
# Run integration tests
cd tests/integration
npm install
npm test
```

## Tech Stack

- **Runtime**: Node.js 22+
- **Backend**: TypeScript + Node.js + Express.js
- **Databases**:
  - MySQL 8.0 (bookmark domain)
  - DynamoDB (notification domain)
- **API Documentation**: OpenAPI 3.1
- **Authentication**: JWT-based auth (unified across domains)
- **Testing**: Jest (unit), Mocha (integration)
- **CI/CD**: Jenkins
- **Containerization**: Docker
- **Cloud Services**: AWS (DynamoDB, Secrets Manager, SNS, SQS, Lambda)
- **Dependency Injection**: Awilix
- **Logging**: Winston with daily rotate
- **Event Processing**: SNS publishing, SQS consumption, Lambda functions

## Service Consolidation

This service represents a consolidation of previously separate bookmark and notification services into a unified codebase. Key benefits:

- **Simplified Deployment**: Single service deployment instead of multiple services
- **Shared Infrastructure**: Common authentication, logging, and monitoring
- **Code Reuse**: Shared utilities, middleware, and patterns
- **Unified Documentation**: Single OpenAPI spec and documentation set
- **Transparent Access**: Existing URLs continue to work seamlessly

For detailed migration information, see `docs/SERVICE_CONSOLIDATION.md`.
