#Notifications Service#

- notifications services api [wiki](https://wiki.babycenter.com/display/DEV/Notifications+API)

## Running DynamoDB on Docker

If you wish to use Dock<PERSON> to install DynamoDB,
please refer to the [docker instructions](./docker/README.md).
With just one command you might be good to go! If you choose
this path, you can jump directly to step 7.

## Running Locally

Install and run dynamodb locally and create a run config to use the dev profile

1.  install dynamodb local: http://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.html
2.  run dynamodb local from install folder: <br/>
    java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -sharedDb<br/>
    You can interact with the database using http://localhost:8000/shell/ or use a gui project like https://github.com/aaronshaf/dynamodb-admin
3.  Create the following tables in dynamodb

    - table name = MemberActivityStatus, partition key = UserHashKey String
    - table name = Notification, partition key = UserHashKey String, primary sort key = ContentRangeKey String
    - table name = Notification_recover, partition key = UserHashKey String, primary sort key = ContentRangeKey String
    - table name = Subscription, partition key = ContentHashKey String, primary sort key = UserRangeKey Number

    For reference: https://aws.amazon.com/blogs/aws/sweet-treats-for-dynamodb-users/

    You can also use the definitions in misc/ddl/\*.json

4.  in intellij, create a run configuration to run using dev profile;
    - create a new run configuration for a spring boot app
    - main class is: notification.NotificationServicesApplication
    - VM options are: -ea
    - working directory: $MODULE_DIR$
    - use class path of module: notification
    - JRE: default (should be 1.8)
    - active profile is: dev
5.  run all unit tests to show everything works.
6.  Start Intl2 local (make sure it is pointing to local, see configuration in intl.properties) and create a comment in any community post.
    You should see a Subscription added in dynamodb.

subsequently, just start dynamodb, run notifications using dev profile configuration

###Notes on querying local DynamoDb instance
There is a nice UI in AWS console for DynamoDB. However, there is no such console when using a local instance of DynamoDB. Instead, you need to use the DynamoDB Javascript Shell. This uses the Javascript SDK to query the local instance.

- url is http://localhost:8000/shell/
- This uses the javascript sdk to query your local dynamodb. It's not SQL - you organize your query as json.
- Here is an example of a GetItem for a Subscription (member is subscribed to any post they make)

         var params = {
             TableName: 'Subscription',
             Key: {
                 "ContentHashKey": "en_CA,3318909",
                 "UserRangeKey": 20517
             }
         };
         docClient.get(params, function(err, data) {
             if (err) ppJson(err); // an error occurred
             else ppJson(data); // successful response
         });

- Here is an example of a GetItem for a Notification, for instance if a member comments on another member's post;

         var params = {
             TableName: 'Notification',
             Key: {
                 "UserHashKey": "en_CA,20517",
                 "ContentRangeKey": "3318909,comment"
             }
         };
         docClient.get(params, function(err, data) {
             if (err) ppJson(err); // an error occurred
             else ppJson(data); // successful response
         });

- Here is an example of a GetItem for a MemberActivityStatus record;

         var params = {
             TableName: 'MemberActivityStatus',
             Key: {
                 "UserHashKey": "en_CA,20517"
             }
         };
         docClient.get(params, function(err, data) {
             if (err) ppJson(err); // an error occurred
             else ppJson(data); // successful response
         });

- the Javascript query is typed in left panel, then executed using the triangular 'play' button. The results are show in the right panel.

## Making a call to notifications v2 endpoints

For most v2 endpoints will need two things to make the call:

- the userkey: this is in the format 'locale,memberId'. For <EMAIL>, a user on US Content with id = 5000, the user key is 'en_US,5000'.
- a fresh accessToken issued to the member from the auth service. Details on how to get that are below.

Most v2 endpoints require a parameter, ?userkey={value} where value is the userkey, like 'en_US,5000' encoded. In the case of the v2 endpoints, the only encoding required is that it be base64 encoded.

The v2 endpoints also require the accessToken be passed in the 'Authorization' header, prefixed by the string 'Bearer ' (note the space after Bearer). Getting the accessToken for a user is the biggest pain in this process. It is done indirectly, by looking at the BcSite logs. Whenever a page is loaded by a logged in user BcSite will get the user's accessToken from their session; if debug logging is turned on, then this is shown in the log.

- **Turn on DEBUG level logging for logger com.bc.web.security.oauth2.OAuth2TokenGeneratorFilter on the bcsite server where you intent to test**.
  To do that, you must be logged in as an admin user on the bcsite server in question. Then go to the jmx-console, like https://qa.babycenter.com:10043/jmx-console/. Under bc.admin section (first section), find service=logConfigurer and select it. That brings you to a screen where you can get and set log levels. In the setLoggerLevel() section, put the logger (com.bc.web.security.oauth2.OAuth2TokenGeneratorFilter) in the 'clazz' field and DEBUG in the 'level' field and select invoke. Note: each qa environment is a single tomcat server, so you are done at this point if you are in qa. In stag or prod, you have to do this for each tomcat server individually (see [noc](https://noc.babycenter.com:10043/cgi-bin/buildinfo/content.cgi) for the list of servers). Once you have one done it is easy to do the rest; on the result page from setting on the first server just change the url to point to a different server and resubmit it.

- Now with DEBUG logging on for com.bc.web.security.oauth2.OAuth2TokenGeneratorFilter, **start tailing the logs, then log in and refresh the home page**. To make it easy, filter the logs by 'com.bc.web.security.oauth2.OAuth2TokenGeneratorFilter' and looks for 'accessToken'. Here is an example:

        2018-03-16 09:34:21,333 DEBUG [vm-0629] [ajp-bio-8009-exec-7  ] [/] [com.bc.web.security.oauth2.OAuth2TokenGeneratorFilter] accessToken : eyJhbGciOiJSUzI1NiJ9.eyJ2cnNuIjowLCJhdWQiOiJjb250ZW50Iiwic3ViIjoiOEdGS2JZVGNBaGZtYThERSIsInNpdGVfdXNlciI6ImJjc2l0ZSw1MDAwIiwic2NvcGUiOlsiUkxVU1IiXSwiaXNzIjoicWEtYXV0aC5iYWJ5Y2VudGVyLmNvbSIsImV4cCI6MTUyMTIxODk2MSwiZ3Zyc24iOjIsImdyYW50IjoiYWNjZXNzIiwiaWF0IjoxNTIxMjE4MDYxLCJqdGkiOiI1WkpvaE12Y3pHVnUzTkJlIiwicG9saWN5Ijoid2ViIn0.L7TWW6PGUrw1mXGkRvUC9Dy2hxIkshkZuAeDlNhMaa-9JlhuzEFtbeG_Gw3Y9gUhrLtKQG5ksF9MYNA1Ir4CUT4eVaki2_avKv6M__Zo3JMFlpOp0oWwIFilGEnrVGPXG22ILTyaDQRwx-WgX2GRVpjxHuTO393jCltiB-cGSFHMmcRrTaizBcKU3nlU3vVUxls1tDKQ6CS9sjzfm4bB6MAQbbiPRhRGKEySbveg2MXeSNBMW6rUrXohpxa4yFyrSwa9TMLwu2R__YzLbxmaTANJUttiC33kSOlBjcvK5bkBzsIiPHS5I8FPccOiQdIiuOKpBtnY0gNxSy6IynSvMw

- **Copy the most recent accessToken for your user**. Note that access tokens have a short lifespan, so you want the most recent one. You can view the contents of the access token by decoding it at [jwt.io](jwt.io). If you are not sure about your user's memberId, you can find it in the site_user field of the accessToken.

- When making the call, **the Authorization header must be the string 'Bearer ' followed by the accessToken** (note the space after Bearer). So like :

        Bearer eyJhbGciOiJSUzI1NiJ9.eyJ2cnNuIjowLCJhdWQiOiJjb250ZW50Iiwic3ViIjoiOEdGS2JZVGNBaGZtYThERSIsInNpdGVfdXNlciI6ImJjc2l0ZSw1MDAwIiwic2NvcGUiOlsiUkxVU1IiXSwiaXNzIjoicWEtYXV0aC5iYWJ5Y2VudGVyLmNvbSIsImV4cCI6MTUyMTIxODk2MSwiZ3Zyc24iOjIsImdyYW50IjoiYWNjZXNzIiwiaWF0IjoxNTIxMjE4MDYxLCJqdGkiOiI1WkpvaE12Y3pHVnUzTkJlIiwicG9saWN5Ijoid2ViIn0.L7TWW6PGUrw1mXGkRvUC9Dy2hxIkshkZuAeDlNhMaa-9JlhuzEFtbeG_Gw3Y9gUhrLtKQG5ksF9MYNA1Ir4CUT4eVaki2_avKv6M__Zo3JMFlpOp0oWwIFilGEnrVGPXG22ILTyaDQRwx-WgX2GRVpjxHuTO393jCltiB-cGSFHMmcRrTaizBcKU3nlU3vVUxls1tDKQ6CS9sjzfm4bB6MAQbbiPRhRGKEySbveg2MXeSNBMW6rUrXohpxa4yFyrSwa9TMLwu2R__YzLbxmaTANJUttiC33kSOlBjcvK5bkBzsIiPHS5I8FPccOiQdIiuOKpBtnY0gNxSy6IynSvMw

- **The user key must be base64 encoded and used in the ?userkey= parameter**. So getting the list of <NAME_EMAIL> whose user key is 'en_US,5000' would look like:

        https://notifications-qa.babycenter.com/v2/user/notifications?userkey=ZW5fVVMsNTAwMA==

## Quick smoke test of notifications

- login, check your notifications and clear them all
- goto mom answers on bcsite: /mom-answers
- Ask a question
- logout and register as another user
- answer the question
- logout and login as the user that asked the question
- you should have a notification in the upper right next to your avatar
