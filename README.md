# Bookmark Service

## Setup

1. Create bookmarks database on local mysql  from the project root folder, run using this command:
    ```   
    mysql -uroot -pn3wb4by < ./src/main/sql/create_local_bookmarks_db.sql
    ```

2. Create changelog for local db; from the project root folder run this command:
    ```
    mysql -uroot -pn3wb4by bookmarks < ./src/main/sql/dbdeploy/changeLog/createChangeLogTable.sql
    ```

3.  To run the update scripts, run this command from the project's root folder:
    ```    
    gradle update -b dbdeploy-local.gradle
    ```
    
4. At Run/Debug edit configurations create a new configuration using the template Spring Boot and do the following steps:
   - Set the Main class to:
     ```
     com.babycenter.bookmarkservice.Application
     ``` 
   - Set the following VM option:
        ```
        -Dspring.profiles.active=local
        ```
     
## Test

1. Import to Postman the file postman/Bookmark.postman_collection.json
2. Import to Postman the env files:
    - postman/Bookmark Service Dev.postman_environment.json
    - postman/Bookmark Service QA.postman_environment.json