import type { NextConfig } from "next";
import createMDX from '@next/mdx'

const nextConfig: NextConfig = {
  // Support for both TSX pages and MDX pages
  pageExtensions: ['ts', 'tsx', 'mdx'],
  // Enable static export for GitHub Pages
  output: 'export',
  trailingSlash: true,
  /* config options here */
  reactStrictMode: true,
  images: {
    unoptimized: true, // <--- KEY SETTING FOR STATIC EXPORT!
  },
};

const withMDX = createMDX({
  extension: /\.mdx?$/,
  // providerImportSource: '@mdx-js/react', // Use the standard MDX provider
});

export default withMDX(nextConfig);
