<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>


	<groupId>com.bc.notification</groupId>
	<artifactId>notification</artifactId>
	<version>NOS.2024.01.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>notification-services</name>
	<description>Micro services project for babycenter notifications</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.4.1.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

    <repositories>
        <repository>
            <id>BabyCenterProxyNexus</id>
            <url>http://nexus.babycenter.com:8081/nexus/content/groups/public</url>
        </repository>
        <repository>
            <id>BabyCenterInternalNexus</id>
            <url>http://nexus.babycenter.com:8081/nexus/content/repositories/releases</url>
        </repository>
		<repository>
			<id>mvnrepository</id>
			<url>https://repo1.maven.org/maven2</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<releases>
				<enabled>true</enabled>
			</releases>
		</repository>
    </repositories>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<start-class>notification.NotificationServicesApplication</start-class>
		<java.version>1.8</java.version>
		<snippetsDirectory>${project.build.directory}/generated-snippets</snippetsDirectory>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-dynamodb</artifactId>
			<version>1.11.9</version>
		</dependency>
		<dependency>
			<groupId>com.github.derjust</groupId>
			<artifactId>spring-data-dynamodb</artifactId>
			<version>4.4.1</version>
			<exclusions>
				<exclusion>
					<artifactId>aws-java-sdk</artifactId>
					<groupId>com.amazonaws</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.bc.boot</groupId>
			<artifactId>spring-boot-starter-bc-admin</artifactId>
			<version>1.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-sql</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.gpars</groupId>
			<artifactId>gpars</artifactId>
			<version>1.2.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.restdocs</groupId>
			<artifactId>spring-restdocs-mockmvc</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.openclover/clover -->
		<dependency>
			<groupId>org.openclover</groupId>
			<artifactId>clover</artifactId>
			<version>4.2.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk</artifactId>
			<version>1.10.77</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.amazonaws/amazon-sqs-java-messaging-lib -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>amazon-sqs-java-messaging-lib</artifactId>
			<version>1.0.0</version>
			<type>jar</type>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.springframework/spring-jms -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>maven</groupId>
				<artifactId>wagon</artifactId>
				<version>3.5.3</version>
				<scope>compile</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<site>
			<id>site</id>
			<url>
				file://localhost/var/local/data/httpd-maven-repositories/www/html/sites/${project.artifactId}/${project.version}
			</url>
		</site>
		<repository>
			<id>BC.Nexus.Internal.Repository</id>
			<name>Internal Release Repository</name>
			<url>
				scpexe://nexus.babycenter.com/var/local/data/build/nexus-work/storage/releases/
			</url>
		</repository>
		<snapshotRepository>
			<id>BC.Nexus.Internal.Snapshot.Repository</id>
			<name>Internal Snapshot Repository</name>
			<url>
				scpexe://nexus.babycenter.com/var/local/data/build/nexus-work/storage/snapshots/
			</url>
		</snapshotRepository>
	</distributionManagement>


	<build>
		<extensions>
	      <extension>
	        <groupId>org.apache.maven.wagon</groupId>
	        <artifactId>wagon-ssh-external</artifactId>
	        <version>3.5.3</version>
	      </extension>
	    </extensions>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
				<compilerId>groovy-eclipse-compiler</compilerId>
				</configuration>
				<dependencies>
				<dependency>
					<groupId>org.codehaus.groovy</groupId>
					<artifactId>groovy-eclipse-compiler</artifactId>
					<version>2.9.1-01</version>
				</dependency>
				<dependency>
					<groupId>org.codehaus.groovy</groupId>
					<artifactId>groovy-eclipse-batch</artifactId>
					<version>2.3.7-01</version>
				</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.codehaus.groovy</groupId>
				<artifactId>groovy-eclipse-compiler</artifactId>
				<version>2.9.1-01</version>
				<extensions>true</extensions>
			</plugin>
			<plugin>
				<groupId>org.asciidoctor</groupId>
				<artifactId>asciidoctor-maven-plugin</artifactId>
				<version>1.5.2</version>
				<executions>
					<execution>
						<id>generate-docs</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>process-asciidoc</goal>
						</goals>
						<configuration>
							<backend>html</backend>
							<doctype>book</doctype>
							<attributes>
								<snippets>${snippetsDirectory}</snippets>
							</attributes>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.outputDirectory}/api/docs</outputDirectory>
							<resources>
								<resource>
									<directory>${project.build.directory}/generated-docs</directory>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.apache.maven.plugins
										</groupId>
										<artifactId>
											maven-compiler-plugin
										</artifactId>
										<versionRange>
											[3.1,)
										</versionRange>
										<goals>
											<goal>compile</goal>
											<goal>testCompile</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

</project>
