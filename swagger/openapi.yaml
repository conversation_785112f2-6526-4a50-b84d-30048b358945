openapi: 3.1.0
info:
  title: Bookmark & Notification API
  version: 1.0.0
  description: Consolidated API for managing bookmarks and notifications. This service provides unified endpoints for both bookmark and notification functionality.
servers:
  - url: https://bookmark-service.babycenter.com
    description: Production - Bookmark Service Endpoints
  - url: https://stag-bookmark-service.babycenter.com
    description: Staging - Bookmark Service Endpoints
  - url: https://qa-bookmark-service.babycenter.com
    description: QA - Bookmark Service Endpoints
  - url: https://notifications.babycenter.com
    description: Production - Notification Service Endpoints
  - url: https://notifications-stag.babycenter.com
    description: Staging - Notification Service Endpoints
  - url: https://notifications-qa.babycenter.com
    description: QA - Notification Service Endpoints
  - url: http://localhost:4000
    description: Local Development Environment
paths:
  /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}/{contentId}:
    get:
      summary: Get single bookmark
      operationId: getBookmark
      parameters:
        - name: memberId
          in: path
          required: true
          description: User Global Auth ID
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: contentId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Bookmark found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Bookmark'
        '404':
          description: Bookmark not found
        '401':
          description: Unauthorized
    post:
      summary: Save bookmark
      operationId: saveBookmark
      parameters:
        - name: memberId
          in: path
          required: true
          description: User Global Auth ID
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: contentId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                memberId:
                  type: string
                contentSource:
                  type: string
                contentType:
                  type: string
                contentId:
                  type: string
                createDate:
                  type: string
                  format: date-time
      responses:
        '200':
          description: Bookmark saved successfully
        '400':
          description: Bad request or too many bookmarks
        '401':
          description: Unauthorized
    delete:
      summary: Delete bookmark
      operationId: deleteBookmark
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: contentId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Bookmark deleted successfully
        '404':
          description: Bookmark not found
        '401':
          description: Unauthorized
  /bookmark/v1/member/{memberId}/content/{contentSource}/{contentType}:
    get:
      summary: List bookmark
      operationId: listBookmark
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of bookmarks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BookmarkSummary'
        '401':
          description: Unauthorized
    delete:
      summary: Batch delete bookmarks by content IDs
      operationId: batchDeleteBookmarks
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: contentIds
          in: query
          required: true
          description: Comma-separated list of content IDs to delete
          schema:
            type: string
      responses:
        '200':
          description: Bookmarks deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  deleted:
                    type: integer
                  failed:
                    type: integer
        '401':
          description: Unauthorized
  /bookmark/v1/member/{memberId}/content:
    post:
      summary: Save bookmark (alternative endpoint)
      operationId: saveBookmarkAlternative
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookmarkRequest'
      responses:
        '200':
          description: Bookmark saved successfully
        '400':
          description: Bad request or too many bookmarks
        '401':
          description: Unauthorized
  /bookmark/v2/member/{memberId}/content/{contentSource}/{contentType}:
    get:
      summary: List bookmark Paginated
      operationId: listBookmarkPaginated
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: pageIndex
          in: query
          required: true
          schema:
            type: integer
        - name: pageSize
          in: query
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Paginated list of bookmarks
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedBookmarks'
  /bookmark/member/{memberId}:
    get:
      summary: Get all bookmarks for a member
      operationId: listAllBookmarksByMember
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of all bookmarks for the member
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BookmarkSummary'
        '401':
          description: Unauthorized
    post:
      summary: Batch save multiple bookmarks
      operationId: batchSaveBookmarks
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                bookmarks:
                  type: array
                  items:
                    $ref: '#/components/schemas/BookmarkRequest'
      responses:
        '201':
          description: Bookmarks saved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchSaveResponse'
        '400':
          description: Bad request or too many bookmarks
        '401':
          description: Unauthorized
  /bookmark/member/{memberId}/content/{contentSource}:
    get:
      summary: Get bookmarks by member and content source
      operationId: listBookmarksByMemberAndSource
      parameters:
        - name: memberId
          in: path
          required: true
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of bookmarks for the member and content source
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BookmarkSummary'
        '401':
          description: Unauthorized

  # Batch content endpoints (internal services)
  /bookmark/v1/content/{contentSource}/{contentType}/{contentId}:
    get:
      summary: Get bookmarks by content (internal service)
      operationId: batchGetBookmarksByContent
      parameters:
        - name: bsToken
          in: header
          required: true
          description: Internal service token
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: contentId
          in: path
          required: true
          schema:
            type: string
        - name: pageIndex
          in: query
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: List of bookmarks for the content
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Bookmark'
        '401':
          description: Missing or invalid internal token
    delete:
      summary: Delete bookmarks by content (internal service)
      operationId: batchDeleteBookmarksByContent
      parameters:
        - name: bsToken
          in: header
          required: true
          description: Internal service token
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: contentId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Bookmarks deleted successfully
        '401':
          description: Missing or invalid internal token

  /bookmark/v1/content/{contentSource}/contentType/{contentType}:
    get:
      summary: Get bookmarks by content type (internal service)
      operationId: batchGetBookmarksByContentType
      parameters:
        - name: bsToken
          in: header
          required: true
          description: Internal service token
          schema:
            type: string
        - name: contentSource
          in: path
          required: true
          schema:
            type: string
        - name: contentType
          in: path
          required: true
          schema:
            type: string
        - name: pageIndex
          in: query
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: List of bookmarks for the content type
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Bookmark'
        '401':
          description: Missing or invalid internal token

  # Java service compatibility endpoints - Notification endpoints

  # V1 notification endpoints (legacy, no auth header required)
  /v1/user/notifications:
    get:
      summary: List notifications V1 (legacy)
      operationId: v1ListNotifications
      parameters:
        - name: userkey
          in: query
          required: true
          description: Base64 encoded user key
          schema:
            type: string
        - name: pageIndex
          in: query
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: List of notifications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JavaNotificationResponse'

  /v1/user/acknowledge:
    get:
      summary: Acknowledge notifications V1 (legacy)
      operationId: v1AcknowledgeNotifications
      parameters:
        - name: userkey
          in: query
          required: true
          description: Base64 encoded user key
          schema:
            type: string
      responses:
        '200':
          description: Notifications acknowledged

  # V2 notification endpoints (with auth header)
  /v2/user/notifications:
    get:
      summary: List notifications V2
      operationId: v2ListNotifications
      parameters:
        - name: userkey
          in: query
          required: true
          description: User key (country_code,user_id)
          schema:
            type: string
        - name: pageIndex
          in: query
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: List of notifications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JavaNotificationResponse'
        '401':
          description: Unauthorized

  /v2/user/acknowledge:
    get:
      summary: Acknowledge notifications V2
      operationId: v2AcknowledgeNotifications
      parameters:
        - name: userkey
          in: query
          required: true
          description: User key (country_code,user_id)
          schema:
            type: string
      responses:
        '200':
          description: Notifications acknowledged
        '401':
          description: Unauthorized

  /v2/user/read:
    post:
      summary: Mark specific notification as read V2
      operationId: v2ReadNotification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userKey:
                  type: string
                contentActionKey:
                  type: string
                  description: Format contentId#action
      responses:
        '200':
          description: Notification marked as read
        '404':
          description: Notification not found
        '401':
          description: Unauthorized

  /v2/user/readall:
    post:
      summary: Mark all notifications as read V2
      operationId: v2ReadAllNotifications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userKey:
                  type: string
      responses:
        '200':
          description: All notifications marked as read
        '401':
          description: Unauthorized

  # V3 notification endpoints (dual user system support)
  /v3/user/notifications:
    get:
      summary: List notifications V3 (dual user system)
      operationId: v3ListNotifications
      parameters:
        - name: userkey
          in: query
          description: User key (country_code,user_id)
          schema:
            type: string
        - name: globalAuthId
          in: query
          description: Global auth ID
          schema:
            type: string
        - name: pageIndex
          in: query
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: List of notifications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JavaNotificationResponse'
        '401':
          description: Unauthorized

  /v3/user/acknowledge:
    get:
      summary: Acknowledge notifications V3 (dual user system)
      operationId: v3AcknowledgeNotifications
      parameters:
        - name: userkey
          in: query
          description: User key (country_code,user_id)
          schema:
            type: string
        - name: globalAuthId
          in: query
          description: Global auth ID
          schema:
            type: string
      responses:
        '200':
          description: Notifications acknowledged
        '401':
          description: Unauthorized

  /v3/user/read:
    post:
      summary: Mark specific notification as read V3 (dual user system)
      operationId: v3ReadNotification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userKey:
                  type: string
                globalAuthId:
                  type: string
                contentActionKey:
                  type: string
                  description: Format contentId#action
      responses:
        '200':
          description: Notification marked as read
        '404':
          description: Notification not found
        '401':
          description: Unauthorized

  /v3/user/readall:
    post:
      summary: Mark all notifications as read V3 (dual user system)
      operationId: v3ReadAllNotifications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userKey:
                  type: string
                globalAuthId:
                  type: string
      responses:
        '200':
          description: All notifications marked as read
        '401':
          description: Unauthorized

  # Internal notification endpoints
  /internal/userkey/{userKey}:
    get:
      summary: Internal user key endpoint
      operationId: internalUserKey
      parameters:
        - name: userKey
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User key information
        '401':
          description: Unauthorized

  /internal/v1/user/notifications:
    get:
      summary: Internal V1 notifications endpoint
      operationId: internalV1Notifications
      parameters:
        - name: userkey
          in: query
          required: true
          schema:
            type: string
        - name: pageIndex
          in: query
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: Internal notifications response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JavaNotificationResponse'
        '401':
          description: Unauthorized

components:
  schemas:
    Bookmark:
      type: object
      properties:
        id:
          type: integer
        memberId:
          type: string
        contentSource:
          type: string
        contentType:
          type: string
        contentId:
          type: string
        createDate:
          type: string
          format: date-time
        updateDate:
          type: string
          format: date-time
    BookmarkSummary:
      type: object
      properties:
        content_source:
          type: string
        content_type:
          type: string
        content_id:
          type: string
        create_date:
          type: string
          format: date-time
        update_date:
          type: string
          format: date-time
    BookmarkRequest:
      type: object
      required:
        - memberId
        - contentSource
        - contentType
        - contentId
      properties:
        memberId:
          type: string
        contentSource:
          type: string
        contentType:
          type: string
        contentId:
          type: string
        createDate:
          type: string
          format: date-time
    PaginatedBookmarks:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BookmarkSummary'
        totalItems:
          type: integer
        pageIndex:
          type: integer
        pageSize:
          type: integer
    BatchSaveResponse:
      type: object
      properties:
        success:
          type: boolean
        savedCount:
          type: integer
        errors:
          type: array
          items:
            type: string

    # Notification schemas
    CreateNotificationRequest:
      type: object
      required:
        - countryCode
        - userId
        - contentId
        - action
      properties:
        countryCode:
          type: string
        userId:
          type: string
        contentId:
          type: string
        action:
          type: string
        postId:
          type: integer
        replyId:
          type: integer
        snippet:
          type: string
        genericContent:
          type: object
        lifetimeDays:
          type: integer
          default: 30

    UpdateNotificationRequest:
      type: object
      properties:
        count:
          type: integer
        snippet:
          type: string
        genericContent:
          type: object
        markAsRead:
          type: boolean

    NotificationResponse:
      type: object
      properties:
        success:
          type: boolean
        notification:
          type: object
          properties:
            userKey:
              type: string
            contentActionKey:
              type: string
            postId:
              type: integer
            replyId:
              type: integer
            snippet:
              type: string
            genericContent:
              type: object
            count:
              type: integer
            createDateTime:
              type: string
              format: date-time
            isRead:
              type: boolean

    PaginatedNotificationResponse:
      type: object
      properties:
        success:
          type: boolean
        notifications:
          type: array
          items:
            $ref: '#/components/schemas/NotificationResponse/properties/notification'
        totalCount:
          type: integer
        pageIndex:
          type: integer
        pageSize:
          type: integer

    JavaNotificationResponse:
      type: object
      properties:
        unreadCount:
          type: integer
        notifications:
          type: array
          items:
            type: object
            properties:
              userKey:
                type: string
              contentActionKey:
                type: string
              postId:
                type: integer
              replyId:
                type: integer
              snippet:
                type: string
              genericContent:
                type: object
              count:
                type: integer
              createDateTime:
                type: string
                format: date-time
              isRead:
                type: boolean

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
