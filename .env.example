# Environment Configuration for Local Development and Testing
# Copy this file to .env for local development
# For QA, Staging, and Production environments, configuration is managed through ECS task definitions
ENVIRONMENT=dev
PORT=4000
LOG_LEVEL=info

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASS=password
DB_NAME=bookmarks
DB_PORT=3307 # Change this to match your MySQL port
DB_ENGINE=mysql

# Auth Configuration (for tests - use mocked tokens)
BC_AUTH_SIGNATURE=text:mocked_test_signature
BC_AUTH_BASE_URL=https://auth.babycenter.com

# DynamoDB Configuration for Notifications
DYNAMODB_REGION=us-east-1
DYNAMODB_TABLE_NAME=Notification
DYNAMODB_TTL_DAYS=7
# For local development only:
DYNAMODB_ENDPOINT=http://localhost:8000

# SNS Configuration
US_MEMBERS_EVENTS_SNS_ARN=arn:aws:sns:us-east-1:123456789012:test-topic

# AWS Configuration
AWS_SECRETS_REGION=us-east-1

# StatsD Configuration (optional)
# STATSD_HOST=localhost

# Test-specific settings
# DATABASE_URL=mysql://root:password@localhost:3306/bookmark_service_test
# DB_NAME=bookmark_service_test

# Integration Test Settings
# Uncomment these when running integration tests
# PREDIKTOR_BASE_URL=http://localhost:8081
# PREDIKTOR_AUTHORIZATION_HEADER=test-prediktor-api-key
