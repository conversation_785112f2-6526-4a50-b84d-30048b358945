name: Deploy Next.js site to GitHub Pages & S3

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Allows GitHub Pages deployment
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
# This only applies to the GitHub Pages job.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 1) BUILD JOB
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Detect package manager
        id: detect-package-manager
        run: |
          if [ -f "${{ github.workspace }}/yarn.lock" ]; then
            echo "manager=yarn" >> $GITHUB_OUTPUT
            echo "install_command=install" >> $GITHUB_OUTPUT
            echo "build_runner=yarn" >> $GITHUB_OUTPUT
            exit 0
          elif [ -f "${{ github.workspace }}/package.json" ]; then
            echo "manager=npm" >> $GITHUB_OUTPUT
            echo "install_command=ci" >> $GITHUB_OUTPUT
            echo "build_runner=npx --no-install" >> $GITHUB_OUTPUT
            exit 0
          else
            echo "Unable to determine package manager"
            exit 1
          fi

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: ${{ steps.detect-package-manager.outputs.manager }}

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: .next/cache
          # Generate a new cache whenever packages or source files change.
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json', '**/yarn.lock') }}-${{ hashFiles('**.[jt]s', '**.[jt]sx') }}
          # If source files changed but packages didn't, rebuild from a prior cache.
          restore-keys: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json', '**/yarn.lock') }}-

      - name: Install dependencies
        run: ${{ steps.detect-package-manager.outputs.manager }} ${{ steps.detect-package-manager.outputs.install_command }}

      - name: Build with Next.js
        run: ${{ steps.detect-package-manager.outputs.build_runner }} next build

      # With output: 'export' set in next.config.ts, "next build" will generate the "out" directory.

      - name: Build Likec4 SPA
        run: npm run likec4:build

      - name: Merge Likec4 build output into Next.js export
        run: |
          mkdir -p out/viewer
          cp -r dist/* out/viewer/
      
      # Upload artifact for GitHub Pages (required by deploy-pages)
      - name: Upload artifact for GitHub Pages
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./out

      # Upload artifact for S3 deployment
      - name: Upload artifact for S3
        uses: actions/upload-artifact@v4
        with:
          name: site
          path: ./out

  # 2) DEPLOY TO GITHUB PAGES
  deploy_pages:
    environment:
      name: github-pages
      url: ${{ steps.deploy.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deploy
        uses: actions/deploy-pages@v4

  # 3) DEPLOY TO S3 + CLOUDFRONT
  deploy_s3:
    runs-on: ubuntu-latest
    needs: build
    steps:
      # Download the artifact for S3 deployment
      - name: Download artifact for S3
        uses: actions/download-artifact@v4
        with:
          name: site
          path: ./site

      # Configure AWS credentials (via OIDC role or secrets)
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::551062308343:role/arcdoc-github-role 
          aws-region: us-west-2

      # Sync the site to your private S3 bucket
      - name: Deploy to S3
        run: aws s3 sync ./site s3://arcdoc-us-west-2-babycenter-com --delete

      # Invalidate CloudFront cache
      - name: Invalidate CloudFront Cache
        run: aws cloudfront create-invalidation --distribution-id E1CTJ00TS7PCOQ --paths "/*"
