name: CI

on:
  push:
    branches: [main, active]
  pull_request:
    branches: ['**']

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - run: npm install -g esbuild@0.18.2
      - run: npm ci
      - run: npm run lint
      - run: npm run build
      - run: npm test

      - name: Start DB + API
        run: npm run docker:up

      - name: Integration tests
        run: npm run test:integration

      - name: Stop DB + API
        if: always()
        run: npm run docker:down
