name: Security & Dependency Checks

on:
  push:
    branches: [main, active, develop]
  pull_request:
    # Run security checks on ALL pull requests
    branches: ['**']

jobs:
  security-audit:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js 22.x
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: Check for known vulnerabilities
        run: npm audit --audit-level=high

      - name: Install integration test dependencies and audit
        working-directory: ./tests/integration
        run: |
          npm ci
          npm audit --audit-level=moderate
        continue-on-error: true

  dependency-check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js 22.x
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'

      - name: Check for outdated dependencies
        run: |
          npm ci
          npm outdated || true

      - name: Check package-lock.json is up to date
        run: |
          npm ci --dry-run
          git diff --exit-code package-lock.json || (echo "package-lock.json is not up to date" && exit 1)
